"use strict";const e=require("../../common/vendor.js"),t=e.defineComponent({__name:"result",setup(t){const o=e.ref(""),n=e.ref(""),a=e.ref({}),i=e.ref(!1),s=e.ref([]),l=e.ref("");e.onMounted(()=>{const t=getCurrentPages(),o=t[t.length-1].options||{};l.value=o.taskId||"",l.value?d():e.index.showToast({title:"参数错误",icon:"none"})});const d=async()=>{try{e.index.showLoading({title:"加载中..."});const t=await e.wx$1.cloud.callFunction({name:"get-task-detail",data:{taskId:l.value}});a.value=t.result;const i=await e.wx$1.cloud.callFunction({name:"get-video-url",data:{fileId:a.value.processedVideoFileId}});o.value=i.result.url,n.value=i.result.poster||"",e.index.hideLoading()}catch(t){console.error("加载结果失败:",t),e.index.hideLoading(),e.index.showToast({title:"加载失败",icon:"none"})}},r=()=>{o.value?(e.index.showLoading({title:"下载中..."}),e.index.downloadFile({url:o.value,success:t=>{200===t.statusCode&&e.index.saveVideoToPhotosAlbum({filePath:t.tempFilePath,success:()=>{e.index.hideLoading(),e.index.showToast({title:"保存成功",icon:"success"})},fail:t=>{console.error("保存失败:",t),e.index.hideLoading(),e.index.showToast({title:"保存失败",icon:"none"})}})},fail:t=>{console.error("下载失败:",t),e.index.hideLoading(),e.index.showToast({title:"下载失败",icon:"none"})}})):e.index.showToast({title:"视频未加载",icon:"none"})},c=()=>{e.index.share({provider:"weixin",scene:"WXSceneSession",type:5,videoPath:o.value,title:"视语翻译 - 带字幕视频",success:()=>{e.index.showToast({title:"分享成功",icon:"success"})},fail:t=>{console.error("分享失败:",t),e.index.showToast({title:"分享失败",icon:"none"})}})},u=async()=>{try{e.index.showLoading({title:"加载字幕..."});const t=await e.wx$1.cloud.callFunction({name:"get-subtitle",data:{taskId:l.value}});s.value=t.result||[],i.value=!0,e.index.hideLoading()}catch(t){console.error("加载字幕失败:",t),e.index.hideLoading(),e.index.showToast({title:"加载字幕失败",icon:"none"})}},x=()=>{i.value=!1},h=()=>{const t=s.value.map(e=>`${e.startTime} --\x3e ${e.endTime}\n${e.text}`).join("\n\n");e.index.setClipboardData({data:t,success:()=>{e.index.showToast({title:"复制成功",icon:"success"})}})},v=async()=>{try{await e.wx$1.cloud.callFunction({name:"get-subtitle-file",data:{taskId:l.value}});e.index.showToast({title:"字幕文件已生成",icon:"success"})}catch(t){console.error("下载字幕失败:",t),e.index.showToast({title:"下载失败",icon:"none"})}},g=e=>{if(!e)return"";const t=new Date(e);return`${t.getFullYear()}-${(t.getMonth()+1).toString().padStart(2,"0")}-${t.getDate().toString().padStart(2,"0")} ${t.getHours().toString().padStart(2,"0")}:${t.getMinutes().toString().padStart(2,"0")}`},w=e=>{if(!e)return"";return`${Math.floor(e/60)}:${Math.floor(e%60).toString().padStart(2,"0")}`};return(t,l)=>{return e.e({a:o.value},o.value?{b:o.value,c:n.value}:{},{d:e.t(g(a.value.finishTime)),e:e.t(w(a.value.duration)),f:e.t((d=a.value.fileSize,d?d<1024?d+"B":d<1048576?(d/1024).toFixed(1)+"KB":(d/1048576).toFixed(1)+"MB":"")),g:e.o(r),h:e.o(c),i:e.o(u),j:i.value},i.value?{k:e.o(x),l:e.f(s.value,(t,o,n)=>({a:e.t(t.startTime),b:e.t(t.endTime),c:e.t(t.text),d:o})),m:e.o(h),n:e.o(v),o:e.o(()=>{}),p:e.o(x)}:{});var d}}}),o=e._export_sfc(t,[["__scopeId","data-v-8f447252"]]);wx.createPage(o);
