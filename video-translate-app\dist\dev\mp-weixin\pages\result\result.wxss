
.result-container.data-v-d38065ce {
  padding: 40rpx;
  min-height: 100vh;
  background-color: #f5f5f5;
}
.video-section.data-v-d38065ce {
  background: white;
  border-radius: 20rpx;
  padding: 20rpx;
  margin-bottom: 40rpx;
}
.result-video.data-v-d38065ce {
  width: 100%;
  height: 400rpx;
  border-radius: 10rpx;
}
.video-loading.data-v-d38065ce {
  height: 400rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 28rpx;
}
.video-info.data-v-d38065ce {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 40rpx;
}
.info-item.data-v-d38065ce {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}
.info-item.data-v-d38065ce:last-child {
  border-bottom: none;
}
.info-label.data-v-d38065ce {
  font-size: 28rpx;
  color: #666;
}
.info-value.data-v-d38065ce {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}
.action-buttons.data-v-d38065ce {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}
.download-btn.data-v-d38065ce {
  background-color: #007AFF;
  color: white;
  border: none;
  border-radius: 10rpx;
  height: 88rpx;
  font-size: 32rpx;
}
.share-btn.data-v-d38065ce, .subtitle-btn.data-v-d38065ce {
  background-color: white;
  color: #007AFF;
  border: 2rpx solid #007AFF;
  border-radius: 10rpx;
  height: 88rpx;
  font-size: 32rpx;
}
.subtitle-modal.data-v-d38065ce {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}
.subtitle-content.data-v-d38065ce {
  background: white;
  border-radius: 20rpx;
  width: 90%;
  max-height: 80%;
  display: flex;
  flex-direction: column;
}
.subtitle-header.data-v-d38065ce {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}
.subtitle-title.data-v-d38065ce {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}
.close-btn.data-v-d38065ce {
  font-size: 60rpx;
  color: #999;
  line-height: 1;
}
.subtitle-list.data-v-d38065ce {
  flex: 1;
  padding: 20rpx 30rpx;
}
.subtitle-item.data-v-d38065ce {
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}
.subtitle-item.data-v-d38065ce:last-child {
  border-bottom: none;
}
.subtitle-time.data-v-d38065ce {
  display: block;
  font-size: 24rpx;
  color: #999;
  margin-bottom: 10rpx;
}
.subtitle-text.data-v-d38065ce {
  display: block;
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
}
.subtitle-actions.data-v-d38065ce {
  display: flex;
  gap: 20rpx;
  padding: 30rpx;
  border-top: 1rpx solid #f0f0f0;
}
.copy-btn.data-v-d38065ce, .download-subtitle-btn.data-v-d38065ce {
  flex: 1;
  background-color: #007AFF;
  color: white;
  border: none;
  border-radius: 8rpx;
}
