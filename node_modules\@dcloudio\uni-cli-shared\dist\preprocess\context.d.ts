export declare function getPreVueContext(): any;
export declare function getPreNVueContext(): any;
export declare function getPreUVueContext(): any;
export declare function initScopedPreContext(platform: UniApp.PLATFORM, userPreContext?: Record<string, boolean> | string, utsPlatform?: typeof process.env.UNI_UTS_PLATFORM, isX?: boolean): {
    preVueContext: any;
    preNVueContext: any;
    preUVueContext: any;
};
export declare function initPreContext(platform: UniApp.PLATFORM, userPreContext?: Record<string, boolean> | string, utsPlatform?: typeof process.env.UNI_UTS_PLATFORM, isX?: boolean): void;
