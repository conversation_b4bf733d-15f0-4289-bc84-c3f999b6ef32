"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "process",
  setup(__props) {
    const taskStatus = common_vendor.ref("processing");
    const currentStep = common_vendor.ref(1);
    const errorMsg = common_vendor.ref("");
    const fileId = common_vendor.ref("");
    const taskId = common_vendor.ref("");
    let pollTimer = null;
    common_vendor.onMounted(() => {
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      const options = currentPage.options || {};
      fileId.value = options.fileId || "";
      if (fileId.value) {
        startPolling();
      } else {
        common_vendor.index.showToast({
          title: "参数错误",
          icon: "none"
        });
      }
    });
    common_vendor.onUnmounted(() => {
      if (pollTimer) {
        clearInterval(pollTimer);
      }
    });
    const startPolling = () => {
      checkTaskStatus();
      pollTimer = setInterval(() => {
        if (taskStatus.value === "processing") {
          checkTaskStatus();
        } else {
          clearInterval(pollTimer);
        }
      }, 3e3);
    };
    const checkTaskStatus = async () => {
      try {
        const res = await common_vendor.wx$1.cloud.callFunction({
          name: "get-task-status",
          data: { fileId: fileId.value }
        });
        const task = res.result;
        if (task) {
          taskId.value = task._id;
          taskStatus.value = task.status;
          errorMsg.value = task.errorMsg || "";
          updateCurrentStep(task.status);
        }
      } catch (error) {
        console.error("查询任务状态失败:", error);
      }
    };
    const updateCurrentStep = (status) => {
      switch (status) {
        case "processing":
          currentStep.value = 2;
          break;
        case "completed":
          currentStep.value = 4;
          break;
      }
    };
    const getStatusTitle = () => {
      switch (taskStatus.value) {
        case "processing":
          return "正在处理中...";
        case "completed":
          return "处理完成";
        case "failed":
          return "处理失败";
        default:
          return "未知状态";
      }
    };
    const getStatusDesc = () => {
      switch (taskStatus.value) {
        case "processing":
          return "请耐心等待，通常需要1-3分钟";
        case "completed":
          return "视频字幕已生成完成";
        case "failed":
          return "处理过程中出现错误";
        default:
          return "";
      }
    };
    const viewResult = () => {
      common_vendor.index.navigateTo({
        url: `/pages/result/result?taskId=${taskId.value}`
      });
    };
    const retryProcess = () => {
      common_vendor.index.showModal({
        title: "确认重新处理",
        content: "是否重新处理该视频？",
        success: (res) => {
          if (res.confirm) {
            taskStatus.value = "processing";
            currentStep.value = 1;
            errorMsg.value = "";
            startPolling();
          }
        }
      });
    };
    const goBack = () => {
      common_vendor.index.switchTab({
        url: "/pages/index/index"
      });
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: taskStatus.value === "processing"
      }, taskStatus.value === "processing" ? {} : taskStatus.value === "completed" ? {} : taskStatus.value === "failed" ? {} : {}, {
        b: taskStatus.value === "completed",
        c: taskStatus.value === "failed",
        d: common_vendor.t(getStatusTitle()),
        e: common_vendor.t(getStatusDesc()),
        f: currentStep.value >= 1 ? 1 : "",
        g: currentStep.value > 1 ? 1 : "",
        h: currentStep.value >= 2 ? 1 : "",
        i: currentStep.value > 2 ? 1 : "",
        j: currentStep.value >= 3 ? 1 : "",
        k: currentStep.value > 3 ? 1 : "",
        l: currentStep.value >= 4 ? 1 : "",
        m: currentStep.value > 4 ? 1 : "",
        n: taskStatus.value === "completed"
      }, taskStatus.value === "completed" ? {
        o: common_vendor.o(viewResult)
      } : {}, {
        p: taskStatus.value === "failed"
      }, taskStatus.value === "failed" ? {
        q: common_vendor.o(retryProcess)
      } : {}, {
        r: common_vendor.o(goBack),
        s: errorMsg.value
      }, errorMsg.value ? {
        t: common_vendor.t(errorMsg.value)
      } : {});
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-dfd768e4"]]);
wx.createPage(MiniProgramPage);
