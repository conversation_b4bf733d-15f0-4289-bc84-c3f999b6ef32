<view class="result-container data-v-d38065ce"><view class="video-section data-v-d38065ce"><video wx:if="{{a}}" src="{{b}}" controls class="result-video data-v-d38065ce" poster="{{c}}" show-fullscreen-btn show-play-btn show-center-play-btn></video><view wx:else class="video-loading data-v-d38065ce"><text class="data-v-d38065ce">视频加载中...</text></view></view><view class="video-info data-v-d38065ce"><view class="info-item data-v-d38065ce"><text class="info-label data-v-d38065ce">处理时间:</text><text class="info-value data-v-d38065ce">{{d}}</text></view><view class="info-item data-v-d38065ce"><text class="info-label data-v-d38065ce">视频时长:</text><text class="info-value data-v-d38065ce">{{e}}</text></view><view class="info-item data-v-d38065ce"><text class="info-label data-v-d38065ce">文件大小:</text><text class="info-value data-v-d38065ce">{{f}}</text></view></view><view class="action-buttons data-v-d38065ce"><button bindtap="{{g}}" class="download-btn data-v-d38065ce" type="primary"> 下载视频 </button><button bindtap="{{h}}" class="share-btn data-v-d38065ce"> 分享视频 </button><button bindtap="{{i}}" class="subtitle-btn data-v-d38065ce"> 查看字幕 </button></view><view wx:if="{{j}}" class="subtitle-modal data-v-d38065ce" bindtap="{{p}}"><view class="subtitle-content data-v-d38065ce" catchtap="{{o}}"><view class="subtitle-header data-v-d38065ce"><text class="subtitle-title data-v-d38065ce">字幕内容</text><text class="close-btn data-v-d38065ce" bindtap="{{k}}">×</text></view><scroll-view class="subtitle-list data-v-d38065ce" scroll-y><view wx:for="{{l}}" wx:for-item="item" wx:key="d" class="subtitle-item data-v-d38065ce"><text class="subtitle-time data-v-d38065ce">{{item.a}} --> {{item.b}}</text><text class="subtitle-text data-v-d38065ce">{{item.c}}</text></view></scroll-view><view class="subtitle-actions data-v-d38065ce"><button bindtap="{{m}}" class="copy-btn data-v-d38065ce" size="mini">复制字幕</button><button bindtap="{{n}}" class="download-subtitle-btn data-v-d38065ce" size="mini">下载SRT</button></view></view></view></view>