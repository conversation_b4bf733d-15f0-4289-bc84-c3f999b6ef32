<view class="process-container data-v-cf63811e"><view class="status-card data-v-cf63811e"><view class="status-icon data-v-cf63811e"><text wx:if="{{a}}" class="processing-icon data-v-cf63811e">⏳</text><text wx:elif="{{b}}" class="success-icon data-v-cf63811e">✅</text><text wx:elif="{{c}}" class="error-icon data-v-cf63811e">❌</text></view><view class="status-text data-v-cf63811e"><text class="status-title data-v-cf63811e">{{d}}</text><text class="status-desc data-v-cf63811e">{{e}}</text></view></view><view class="steps-container data-v-cf63811e"><view class="{{['step-item', 'data-v-cf63811e', f && 'active', g && 'completed']}}"><view class="step-number data-v-cf63811e">1</view><view class="step-content data-v-cf63811e"><text class="step-title data-v-cf63811e">视频上传</text><text class="step-desc data-v-cf63811e">视频文件上传到云端</text></view></view><view class="{{['step-item', 'data-v-cf63811e', h && 'active', i && 'completed']}}"><view class="step-number data-v-cf63811e">2</view><view class="step-content data-v-cf63811e"><text class="step-title data-v-cf63811e">语音识别</text><text class="step-desc data-v-cf63811e">AI识别视频中的语音内容</text></view></view><view class="{{['step-item', 'data-v-cf63811e', j && 'active', k && 'completed']}}"><view class="step-number data-v-cf63811e">3</view><view class="step-content data-v-cf63811e"><text class="step-title data-v-cf63811e">字幕生成</text><text class="step-desc data-v-cf63811e">生成SRT字幕文件</text></view></view><view class="{{['step-item', 'data-v-cf63811e', l && 'active', m && 'completed']}}"><view class="step-number data-v-cf63811e">4</view><view class="step-content data-v-cf63811e"><text class="step-title data-v-cf63811e">视频合成</text><text class="step-desc data-v-cf63811e">将字幕烧录到视频中</text></view></view></view><view class="action-buttons data-v-cf63811e"><button wx:if="{{n}}" bindtap="{{o}}" class="result-btn data-v-cf63811e" type="primary"> 查看结果 </button><button wx:if="{{p}}" bindtap="{{q}}" class="retry-btn data-v-cf63811e" type="primary"> 重新处理 </button><button bindtap="{{r}}" class="back-btn data-v-cf63811e">返回首页</button></view><view wx:if="{{s}}" class="error-message data-v-cf63811e"><text class="data-v-cf63811e">{{t}}</text></view></view>