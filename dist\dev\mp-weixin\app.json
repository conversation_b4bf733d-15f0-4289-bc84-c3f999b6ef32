{"pages": ["pages/index/index", "pages/upload/upload", "pages/process/process", "pages/result/result", "pages/history/history"], "window": {"navigationBarTextStyle": "white", "navigationBarTitleText": "视语翻译", "navigationBarBackgroundColor": "#007AFF", "backgroundColor": "#F8F8F8"}, "tabBar": {"color": "#7A7E83", "selectedColor": "#007AFF", "borderStyle": "black", "backgroundColor": "#ffffff", "list": [{"pagePath": "pages/index/index", "iconPath": "static/tab-home.png", "selectedIconPath": "static/tab-home-active.png", "text": "首页"}, {"pagePath": "pages/history/history", "iconPath": "static/tab-history.png", "selectedIconPath": "static/tab-history-active.png", "text": "历史"}]}, "permission": {"scope.camera": {"desc": "用于拍摄视频"}, "scope.writePhotosAlbum": {"desc": "用于保存处理后的视频到相册"}}, "requiredBackgroundModes": ["audio"], "plugins": {"tencentCloudBase": {"version": "latest", "provider": "wx8a0cfcb3b2c8b3f8"}}, "usingComponents": {}}