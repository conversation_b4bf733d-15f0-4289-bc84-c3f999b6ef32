"use strict";const e=require("../../common/vendor.js"),a=e.defineComponent({__name:"upload",setup(a){const o=e.ref(null),l=e.ref(!1),t=e.ref(0),n=()=>{e.index.chooseVideo({sourceType:["camera","album"],maxDuration:300,success:e=>{console.log("选择视频成功:",e),o.value=e},fail:a=>{console.error("选择视频失败:",a),e.index.showToast({title:"选择视频失败",icon:"none"})}})},u=async()=>{if(o.value)try{l.value=!0,t.value=0;const a=await r();e.VodUploader.start({mediaFile:o.value,getSignature:()=>a,procedure:"SubtitleProcessing",onProgress:e=>{t.value=Math.round(100*e.percent)},onFinish:async a=>{console.log("上传完成:",a),await s(a.fileId),e.index.navigateTo({url:`/pages/process/process?fileId=${a.fileId}`})},onError:a=>{console.error("上传失败:",a),e.index.showToast({title:"上传失败",icon:"none"}),l.value=!1}})}catch(a){console.error("上传过程出错:",a),e.index.showToast({title:"上传失败",icon:"none"}),l.value=!1}},r=async()=>new Promise((a,o)=>{e.wx$1.cloud.callFunction({name:"get-upload-signature",success:e=>{a(e.result)},fail:o})}),s=async a=>new Promise((o,l)=>{e.wx$1.cloud.callFunction({name:"create-task",data:{fileId:a},success:o,fail:l})}),i=()=>{o.value=null,l.value=!1,t.value=0};return(a,r)=>{return e.e({a:e.o(n),b:o.value},o.value?{c:o.value.tempFilePath,d:o.value.thumbTempFilePath,e:e.t((c=o.value.size,c<1024?c+"B":c<1048576?(c/1024).toFixed(1)+"KB":(c/1048576).toFixed(1)+"MB")),f:e.t((s=o.value.duration,`${Math.floor(s/60)}:${Math.floor(s%60).toString().padStart(2,"0")}`))}:{},{g:l.value},l.value?{h:t.value+"%",i:e.t(t.value)}:{},{j:o.value&&!l.value},o.value&&!l.value?{k:e.o(u)}:{},{l:o.value&&!l.value},o.value&&!l.value?{m:e.o(i)}:{});var s,c}}}),o=e._export_sfc(a,[["__scopeId","data-v-8345fd40"]]);wx.createPage(o);
