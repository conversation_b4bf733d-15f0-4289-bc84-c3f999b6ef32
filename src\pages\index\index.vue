<template>
  <view class="home-container">
    <!-- 头部欢迎区域 -->
    <view class="header-section">
      <view class="welcome-text">
        <text class="app-title">视语翻译</text>
        <text class="app-subtitle">AI智能视频字幕生成</text>
      </view>
      <image class="logo" src="/static/logo.png"></image>
    </view>

    <!-- 功能介绍 -->
    <view class="features-section">
      <view class="feature-item">
        <text class="feature-icon">🎬</text>
        <text class="feature-title">视频上传</text>
        <text class="feature-desc">支持多种格式，快速上传</text>
      </view>
      <view class="feature-item">
        <text class="feature-icon">🎤</text>
        <text class="feature-title">语音识别</text>
        <text class="feature-desc">AI智能识别语音内容</text>
      </view>
      <view class="feature-item">
        <text class="feature-icon">📝</text>
        <text class="feature-title">字幕生成</text>
        <text class="feature-desc">自动生成精准字幕</text>
      </view>
    </view>

    <!-- 快速开始 -->
    <view class="quick-start">
      <button @click="startUpload" class="start-btn" type="primary">
        开始制作
      </button>
      <text class="start-tip">点击开始，让视频拥有字幕</text>
    </view>

    <!-- 最近处理 -->
    <view v-if="recentTasks.length > 0" class="recent-section">
      <view class="section-header">
        <text class="section-title">最近处理</text>
        <text class="more-link" @click="viewAllHistory">查看全部</text>
      </view>
      <view class="recent-list">
        <view
          v-for="task in recentTasks"
          :key="task._id"
          class="recent-item"
          @click="viewTaskDetail(task)"
        >
          <view class="task-info">
            <text class="task-name">{{ task.fileName || '未命名视频' }}</text>
            <text class="task-time">{{ formatTime(task.createTime) }}</text>
          </view>
          <view class="task-status" :class="task.status">
            <text>{{ getStatusText(task.status) }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 使用说明 -->
    <view class="help-section">
      <text class="help-title">使用说明</text>
      <view class="help-steps">
        <view class="help-step">
          <text class="step-number">1</text>
          <text class="step-text">选择并上传视频文件</text>
        </view>
        <view class="help-step">
          <text class="step-number">2</text>
          <text class="step-text">等待AI处理生成字幕</text>
        </view>
        <view class="help-step">
          <text class="step-number">3</text>
          <text class="step-text">预览和下载带字幕视频</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

// 响应式数据
const recentTasks = ref<any[]>([])

// 页面加载时获取最近任务
onMounted(() => {
  loadRecentTasks()
})

// 加载最近任务
const loadRecentTasks = async () => {
  try {
    const res = await wx.cloud.callFunction({
      name: 'get-recent-tasks',
      data: { limit: 3 }
    })
    recentTasks.value = res.result || []
  } catch (error) {
    console.error('加载最近任务失败:', error)
  }
}

// 开始上传
const startUpload = () => {
  uni.navigateTo({
    url: '/pages/upload/upload'
  })
}

// 查看所有历史
const viewAllHistory = () => {
  uni.switchTab({
    url: '/pages/history/history'
  })
}

// 查看任务详情
const viewTaskDetail = (task: any) => {
  if (task.status === 'processing') {
    uni.navigateTo({
      url: `/pages/process/process?fileId=${task.originalVideoFileId}`
    })
  } else if (task.status === 'completed') {
    uni.navigateTo({
      url: `/pages/result/result?taskId=${task._id}`
    })
  }
}

// 获取状态文本
const getStatusText = (status: string): string => {
  switch (status) {
    case 'processing': return '处理中'
    case 'completed': return '已完成'
    case 'failed': return '失败'
    default: return '未知'
  }
}

// 格式化时间
const formatTime = (timestamp: string): string => {
  if (!timestamp) return ''
  const date = new Date(timestamp)
  const now = new Date()
  const diff = now.getTime() - date.getTime()

  if (diff < 60000) return '刚刚'
  if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
  if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`

  return `${date.getMonth() + 1}-${date.getDate()}`
}
</script>

<style scoped>
.home-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 40rpx;
}

.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 60rpx;
  padding-top: 40rpx;
}

.welcome-text .app-title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 10rpx;
}

.welcome-text .app-subtitle {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

.logo {
  width: 120rpx;
  height: 120rpx;
  border-radius: 20rpx;
}

.features-section {
  display: flex;
  justify-content: space-between;
  margin-bottom: 60rpx;
}

.feature-item {
  flex: 1;
  text-align: center;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  padding: 40rpx 20rpx;
  margin: 0 10rpx;
}

.feature-icon {
  display: block;
  font-size: 60rpx;
  margin-bottom: 20rpx;
}

.feature-title {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 10rpx;
}

.feature-desc {
  display: block;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
}

.quick-start {
  text-align: center;
  margin-bottom: 60rpx;
}

.start-btn {
  width: 60%;
  height: 88rpx;
  background-color: #ff6b6b;
  border: none;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}

.start-tip {
  display: block;
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
}

.recent-section {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 40rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: white;
}

.more-link {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
}

.recent-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.recent-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15rpx;
  padding: 25rpx;
}

.task-info .task-name {
  display: block;
  font-size: 28rpx;
  color: white;
  margin-bottom: 8rpx;
}

.task-info .task-time {
  display: block;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.6);
}

.task-status {
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  color: white;
}

.task-status.processing {
  background-color: #1890ff;
}

.task-status.completed {
  background-color: #52c41a;
}

.task-status.failed {
  background-color: #ff4d4f;
}

.help-section {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  padding: 30rpx;
}

.help-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 30rpx;
  text-align: center;
}

.help-steps {
  display: flex;
  flex-direction: column;
  gap: 25rpx;
}

.help-step {
  display: flex;
  align-items: center;
}

.step-number {
  width: 60rpx;
  height: 60rpx;
  background-color: #ff6b6b;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: bold;
  margin-right: 20rpx;
}

.step-text {
  font-size: 28rpx;
  color: white;
}
</style>
