
.upload-container.data-v-0ba35d33 {
  padding: 40rpx;
  min-height: 100vh;
  background-color: #f5f5f5;
}
.upload-area.data-v-0ba35d33 {
  background: white;
  border: 2rpx dashed #007AFF;
  border-radius: 20rpx;
  padding: 80rpx 40rpx;
  text-align: center;
  margin-bottom: 40rpx;
}
.upload-icon.data-v-0ba35d33 {
  font-size: 120rpx;
  margin-bottom: 20rpx;
}
.upload-text .main-text.data-v-0ba35d33 {
  display: block;
  font-size: 32rpx;
  color: #333;
  margin-bottom: 10rpx;
}
.upload-text .sub-text.data-v-0ba35d33 {
  display: block;
  font-size: 24rpx;
  color: #999;
}
.video-preview.data-v-0ba35d33 {
  background: white;
  border-radius: 20rpx;
  padding: 20rpx;
  margin-bottom: 40rpx;
}
.preview-video.data-v-0ba35d33 {
  width: 100%;
  height: 400rpx;
  border-radius: 10rpx;
}
.video-info.data-v-0ba35d33 {
  display: flex;
  justify-content: space-between;
  margin-top: 20rpx;
  font-size: 24rpx;
  color: #666;
}
.upload-progress.data-v-0ba35d33 {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 40rpx;
}
.progress-bar.data-v-0ba35d33 {
  width: 100%;
  height: 8rpx;
  background-color: #f0f0f0;
  border-radius: 4rpx;
  overflow: hidden;
  margin-bottom: 20rpx;
}
.progress-fill.data-v-0ba35d33 {
  height: 100%;
  background-color: #007AFF;
  transition: width 0.3s ease;
}
.progress-text.data-v-0ba35d33 {
  font-size: 28rpx;
  color: #333;
  text-align: center;
  display: block;
}
.action-buttons.data-v-0ba35d33 {
  display: flex;
  gap: 20rpx;
}
.upload-btn.data-v-0ba35d33 {
  flex: 1;
  background-color: #007AFF;
  color: white;
  border: none;
  border-radius: 10rpx;
  height: 88rpx;
  font-size: 32rpx;
}
.reset-btn.data-v-0ba35d33 {
  flex: 1;
  background-color: white;
  color: #007AFF;
  border: 2rpx solid #007AFF;
  border-radius: 10rpx;
  height: 88rpx;
  font-size: 32rpx;
}
