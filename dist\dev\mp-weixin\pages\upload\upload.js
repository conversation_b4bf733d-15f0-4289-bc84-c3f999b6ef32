"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "upload",
  setup(__props) {
    const videoInfo = common_vendor.ref(null);
    const uploading = common_vendor.ref(false);
    const uploadPercent = common_vendor.ref(0);
    const chooseVideo = () => {
      common_vendor.index.chooseVideo({
        sourceType: ["camera", "album"],
        maxDuration: 300,
        // 最大5分钟
        success: (res) => {
          console.log("选择视频成功:", res);
          videoInfo.value = res;
        },
        fail: (err) => {
          console.error("选择视频失败:", err);
          common_vendor.index.showToast({
            title: "选择视频失败",
            icon: "none"
          });
        }
      });
    };
    const startUpload = async () => {
      if (!videoInfo.value)
        return;
      try {
        uploading.value = true;
        uploadPercent.value = 0;
        const signature = await getUploadSignature();
        const uploader = common_vendor.VodUploader.start({
          mediaFile: videoInfo.value,
          getSignature: () => signature,
          procedure: "SubtitleProcessing",
          // 任务流名称
          onProgress: (progress) => {
            uploadPercent.value = Math.round(progress.percent * 100);
          },
          onFinish: async (result) => {
            console.log("上传完成:", result);
            await createTask(result.fileId);
            common_vendor.index.navigateTo({
              url: `/pages/process/process?fileId=${result.fileId}`
            });
          },
          onError: (error) => {
            console.error("上传失败:", error);
            common_vendor.index.showToast({
              title: "上传失败",
              icon: "none"
            });
            uploading.value = false;
          }
        });
      } catch (error) {
        console.error("上传过程出错:", error);
        common_vendor.index.showToast({
          title: "上传失败",
          icon: "none"
        });
        uploading.value = false;
      }
    };
    const getUploadSignature = async () => {
      return new Promise((resolve, reject) => {
        common_vendor.wx$1.cloud.callFunction({
          name: "get-upload-signature",
          success: (res) => {
            resolve(res.result);
          },
          fail: reject
        });
      });
    };
    const createTask = async (fileId) => {
      return new Promise((resolve, reject) => {
        common_vendor.wx$1.cloud.callFunction({
          name: "create-task",
          data: { fileId },
          success: resolve,
          fail: reject
        });
      });
    };
    const resetVideo = () => {
      videoInfo.value = null;
      uploading.value = false;
      uploadPercent.value = 0;
    };
    const formatFileSize = (size) => {
      if (size < 1024)
        return size + "B";
      if (size < 1024 * 1024)
        return (size / 1024).toFixed(1) + "KB";
      return (size / (1024 * 1024)).toFixed(1) + "MB";
    };
    const formatDuration = (duration) => {
      const minutes = Math.floor(duration / 60);
      const seconds = Math.floor(duration % 60);
      return `${minutes}:${seconds.toString().padStart(2, "0")}`;
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.o(chooseVideo),
        b: videoInfo.value
      }, videoInfo.value ? {
        c: videoInfo.value.tempFilePath,
        d: videoInfo.value.thumbTempFilePath,
        e: common_vendor.t(formatFileSize(videoInfo.value.size)),
        f: common_vendor.t(formatDuration(videoInfo.value.duration))
      } : {}, {
        g: uploading.value
      }, uploading.value ? {
        h: uploadPercent.value + "%",
        i: common_vendor.t(uploadPercent.value)
      } : {}, {
        j: videoInfo.value && !uploading.value
      }, videoInfo.value && !uploading.value ? {
        k: common_vendor.o(startUpload)
      } : {}, {
        l: videoInfo.value && !uploading.value
      }, videoInfo.value && !uploading.value ? {
        m: common_vendor.o(resetVideo)
      } : {});
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-0ba35d33"]]);
wx.createPage(MiniProgramPage);
