import { type ImportSpecifier } from 'es-module-lexer';
import { type Import } from 'unimport';
import type { /*SourceMapInput, */ PluginContext } from 'rollup';
import type { Position } from '@vue/compiler-core';
export declare const UVUE_CLASS_NAME_PREFIX = "Gen";
export declare const DEFAULT_APPID = "__UNI__uniappx";
export declare const ENTRY_FILENAME: () => "main.uts.ts" | "main.uts";
export declare function wrapResolve(resolve: PluginContext['resolve']): PluginContext['resolve'];
export declare function createTryResolve(importer: string, resolve: PluginContext['resolve'], offsetStart?: Position, origCode?: string): (source: string, code: string, { ss, se }: ImportSpecifier) => Promise<boolean | void>;
export declare function parseImports(code: string, tryResolve?: ReturnType<typeof createTryResolve>): Promise<string>;
export declare function createResolveError(code: string, msg: string, start: Position, end: Position): import("rollup").RollupError;
export declare function kotlinOutDir(): string;
export declare function isVue(filename: string): boolean;
export declare function stringifyMap(obj: unknown, ts?: boolean): string;
export declare function parseUTSRelativeFilename(filename: string, root?: string): string;
export declare function parseUTSImportFilename(filename: string): string;
type UniCloudSpace = {
    provider: string;
    spaceName: string;
    spaceId: string;
    clientSecret?: string;
    endpoint?: string;
    workspaceFolder?: string;
};
export declare function getUniCloudSpaceList(): Array<UniCloudSpace>;
type UniCloudObjectInfo = {
    name: string;
    methodList: string[];
};
export declare function getUniCloudObjectInfo(uniCloudSpaceList: Array<UniCloudSpace>): Array<UniCloudObjectInfo>;
export declare function transformAutoImport(code: string, id: string, ignore?: string[]): Promise<{
    code: string;
}>;
export declare function genAutoImportsCode(imports: Import[]): string;
export declare function transformUniCloudMixinDataCom(code: string): string;
export declare function detectAutoImports(code: string, id: string, ignore?: string[]): Promise<{
    matchedImports: Import[];
}> | {
    matchedImports: never[];
};
export {};
