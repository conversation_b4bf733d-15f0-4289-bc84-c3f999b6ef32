uni-map {
  width: 300px;
  height: 225px;
  display: inline-block;
  line-height: 0;
  overflow: hidden;
  position: relative;
}

uni-map[hidden] {
  display: none;
}

.uni-map-container {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  overflow: hidden;
  background-color: transparent;
}

.uni-map-slot {
  position: absolute;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  pointer-events: none;
}

/* web */
uni-map.web {
  position: relative;
  width: 300px;
  height: 150px;
  display: block;
}

uni-map.web[hidden] {
  display: none;
}

/* 处理高德地图 marker label 默认样式 */
uni-map.web .amap-marker-label {
  padding: 0;
  border: none;
  background-color: transparent;
}

/* 处理高德地图 open-location icon 被遮挡问题 */
uni-map.web .amap-marker>.amap-icon>img {
  left: 0 !important;
  top: 0 !important;
}

uni-map.web .uni-map-control {
  position: absolute;
  width: 0;
  height: 0;
  top: 0;
  left: 0;
  z-index: 999;
}

uni-map.web .uni-map-control-icon {
  position: absolute;
  max-width: initial;
}