"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "result",
  setup(__props) {
    const videoUrl = common_vendor.ref("");
    const videoPoster = common_vendor.ref("");
    const taskInfo = common_vendor.ref({});
    const showSubtitleModal = common_vendor.ref(false);
    const subtitleList = common_vendor.ref([]);
    const taskId = common_vendor.ref("");
    common_vendor.onMounted(() => {
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      const options = currentPage.options || {};
      taskId.value = options.taskId || "";
      if (taskId.value) {
        loadTaskResult();
      } else {
        common_vendor.index.showToast({
          title: "参数错误",
          icon: "none"
        });
      }
    });
    const loadTaskResult = async () => {
      try {
        common_vendor.index.showLoading({ title: "加载中..." });
        const taskRes = await common_vendor.wx$1.cloud.callFunction({
          name: "get-task-detail",
          data: { taskId: taskId.value }
        });
        taskInfo.value = taskRes.result;
        const videoRes = await common_vendor.wx$1.cloud.callFunction({
          name: "get-video-url",
          data: { fileId: taskInfo.value.processedVideoFileId }
        });
        videoUrl.value = videoRes.result.url;
        videoPoster.value = videoRes.result.poster || "";
        common_vendor.index.hideLoading();
      } catch (error) {
        console.error("加载结果失败:", error);
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "加载失败",
          icon: "none"
        });
      }
    };
    const downloadVideo = () => {
      if (!videoUrl.value) {
        common_vendor.index.showToast({
          title: "视频未加载",
          icon: "none"
        });
        return;
      }
      common_vendor.index.showLoading({ title: "下载中..." });
      common_vendor.index.downloadFile({
        url: videoUrl.value,
        success: (res) => {
          if (res.statusCode === 200) {
            common_vendor.index.saveVideoToPhotosAlbum({
              filePath: res.tempFilePath,
              success: () => {
                common_vendor.index.hideLoading();
                common_vendor.index.showToast({
                  title: "保存成功",
                  icon: "success"
                });
              },
              fail: (err) => {
                console.error("保存失败:", err);
                common_vendor.index.hideLoading();
                common_vendor.index.showToast({
                  title: "保存失败",
                  icon: "none"
                });
              }
            });
          }
        },
        fail: (err) => {
          console.error("下载失败:", err);
          common_vendor.index.hideLoading();
          common_vendor.index.showToast({
            title: "下载失败",
            icon: "none"
          });
        }
      });
    };
    const shareVideo = () => {
      common_vendor.index.share({
        provider: "weixin",
        scene: "WXSceneSession",
        type: 5,
        videoPath: videoUrl.value,
        title: "视语翻译 - 带字幕视频",
        success: () => {
          common_vendor.index.showToast({
            title: "分享成功",
            icon: "success"
          });
        },
        fail: (err) => {
          console.error("分享失败:", err);
          common_vendor.index.showToast({
            title: "分享失败",
            icon: "none"
          });
        }
      });
    };
    const viewSubtitle = async () => {
      try {
        common_vendor.index.showLoading({ title: "加载字幕..." });
        const res = await common_vendor.wx$1.cloud.callFunction({
          name: "get-subtitle",
          data: { taskId: taskId.value }
        });
        subtitleList.value = res.result || [];
        showSubtitleModal.value = true;
        common_vendor.index.hideLoading();
      } catch (error) {
        console.error("加载字幕失败:", error);
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "加载字幕失败",
          icon: "none"
        });
      }
    };
    const closeSubtitleModal = () => {
      showSubtitleModal.value = false;
    };
    const copySubtitle = () => {
      const subtitleText = subtitleList.value.map((item) => `${item.startTime} --> ${item.endTime}
${item.text}`).join("\n\n");
      common_vendor.index.setClipboardData({
        data: subtitleText,
        success: () => {
          common_vendor.index.showToast({
            title: "复制成功",
            icon: "success"
          });
        }
      });
    };
    const downloadSubtitle = async () => {
      try {
        const res = await common_vendor.wx$1.cloud.callFunction({
          name: "get-subtitle-file",
          data: { taskId: taskId.value }
        });
        common_vendor.index.showToast({
          title: "字幕文件已生成",
          icon: "success"
        });
      } catch (error) {
        console.error("下载字幕失败:", error);
        common_vendor.index.showToast({
          title: "下载失败",
          icon: "none"
        });
      }
    };
    const formatTime = (timestamp) => {
      if (!timestamp)
        return "";
      const date = new Date(timestamp);
      return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, "0")}-${date.getDate().toString().padStart(2, "0")} ${date.getHours().toString().padStart(2, "0")}:${date.getMinutes().toString().padStart(2, "0")}`;
    };
    const formatDuration = (duration) => {
      if (!duration)
        return "";
      const minutes = Math.floor(duration / 60);
      const seconds = Math.floor(duration % 60);
      return `${minutes}:${seconds.toString().padStart(2, "0")}`;
    };
    const formatFileSize = (size) => {
      if (!size)
        return "";
      if (size < 1024)
        return size + "B";
      if (size < 1024 * 1024)
        return (size / 1024).toFixed(1) + "KB";
      return (size / (1024 * 1024)).toFixed(1) + "MB";
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: videoUrl.value
      }, videoUrl.value ? {
        b: videoUrl.value,
        c: videoPoster.value
      } : {}, {
        d: common_vendor.t(formatTime(taskInfo.value.finishTime)),
        e: common_vendor.t(formatDuration(taskInfo.value.duration)),
        f: common_vendor.t(formatFileSize(taskInfo.value.fileSize)),
        g: common_vendor.o(downloadVideo),
        h: common_vendor.o(shareVideo),
        i: common_vendor.o(viewSubtitle),
        j: showSubtitleModal.value
      }, showSubtitleModal.value ? {
        k: common_vendor.o(closeSubtitleModal),
        l: common_vendor.f(subtitleList.value, (item, index, i0) => {
          return {
            a: common_vendor.t(item.startTime),
            b: common_vendor.t(item.endTime),
            c: common_vendor.t(item.text),
            d: index
          };
        }),
        m: common_vendor.o(copySubtitle),
        n: common_vendor.o(downloadSubtitle),
        o: common_vendor.o(() => {
        }),
        p: common_vendor.o(closeSubtitleModal)
      } : {});
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-d38065ce"]]);
wx.createPage(MiniProgramPage);
