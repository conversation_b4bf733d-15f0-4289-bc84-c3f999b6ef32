"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.uniUniModulesExtApiPlugin = void 0;
const uts_1 = require("../uts");
const resolve_1 = require("../resolve");
function uniUniModulesExtApiPlugin() {
    return {
        name: 'uni:uni-modules_ext-api',
        apply: 'build',
        config() {
            const rollupOptions = {
                input: (0, resolve_1.resolveMainPathOnce)(process.env.UNI_INPUT_DIR),
                external: ['vue'],
                output: {
                    format: 'iife',
                    entryFileNames: 'components.js',
                    globals: {
                        vue: 'Vue',
                        uni: 'uni',
                    },
                },
            };
            const build = {};
            if (process.env.UNI_UTS_PLATFORM === 'app-ios') {
                build.rollupOptions = rollupOptions;
            }
            return {
                build,
            };
        },
        load(id) {
            if ((0, uts_1.isUTSProxy)(id)) {
                return '';
            }
        },
        // generateBundle(_, bundle) {
        //   Object.keys(bundle).forEach((fileName) => {
        //     console.log('fileName', fileName)
        //   })
        // },
    };
}
exports.uniUniModulesExtApiPlugin = uniUniModulesExtApiPlugin;
