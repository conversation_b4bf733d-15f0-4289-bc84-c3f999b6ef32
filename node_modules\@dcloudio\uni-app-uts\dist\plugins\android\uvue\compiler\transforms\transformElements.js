"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.transformElements = void 0;
const compiler_core_1 = require("@vue/compiler-core");
const uni_shared_1 = require("@dcloudio/uni-shared");
const uni_cli_shared_1 = require("@dcloudio/uni-cli-shared");
function transformElements(node, context) {
    if (node.type === compiler_core_1.NodeTypes.ELEMENT &&
        (node.tagType === compiler_core_1.ElementTypes.ELEMENT ||
            (node.tagType === compiler_core_1.ElementTypes.COMPONENT &&
                (0, uni_shared_1.isAppUVueBuiltInEasyComponent)(node.tag)))) {
        context.elements.add(node.tag);
        // 原生UTS组件
        const utsComponentOptions = context.parseUTSComponent(node.tag, 'kotlin');
        if (utsComponentOptions) {
            const className = `{ ${(0, uni_cli_shared_1.capitalize)((0, uni_cli_shared_1.camelize)(node.tag)) + 'Component'} }`;
            if (!context.imports.find((i) => i.path === utsComponentOptions.source && i.exp === className)) {
                context.imports.push({
                    exp: className,
                    path: utsComponentOptions.source,
                });
            }
        }
    }
}
exports.transformElements = transformElements;
