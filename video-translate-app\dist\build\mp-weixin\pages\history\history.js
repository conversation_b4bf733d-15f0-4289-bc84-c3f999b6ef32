"use strict";const e=require("../../common/vendor.js"),t=e.defineComponent({__name:"history",setup(t){const a=e.ref([]),l=e.ref(""),s=e.ref("all"),o=e.ref(!0),i=e.ref(!1),n=[{label:"全部",value:"all"},{label:"处理中",value:"processing"},{label:"已完成",value:"completed"},{label:"失败",value:"failed"}],r=e.computed(()=>{let e=a.value;if("all"!==s.value&&(e=e.filter(e=>e.status===s.value)),l.value){const t=l.value.toLowerCase();e=e.filter(e=>(e.fileName||"").toLowerCase().includes(t)||(e.errorMsg||"").toLowerCase().includes(t))}return e});e.onMounted(()=>{u()});const u=async(t=!1)=>{if(!i.value)try{i.value=!0;const l=(await e.wx$1.cloud.callFunction({name:"get-history-list",data:{skip:t?a.value.length:0,limit:20}})).result||[];a.value=t?[...a.value,...l]:l,o.value=20===l.length}catch(l){console.error("加载历史记录失败:",l),e.index.showToast({title:"加载失败",icon:"none"})}finally{i.value=!1}},c=()=>{},d=()=>{o.value&&!i.value&&u(!0)},f=e=>{switch(e){case"processing":return"处理中";case"completed":return"已完成";case"failed":return"失败";default:return"未知"}},v=e=>{if(!e)return"";const t=new Date(e),a=(new Date).getTime()-t.getTime();return a<6e4?"刚刚":a<36e5?`${Math.floor(a/6e4)}分钟前`:a<864e5?`${Math.floor(a/36e5)}小时前`:`${t.getMonth()+1}-${t.getDate()} ${t.getHours().toString().padStart(2,"0")}:${t.getMinutes().toString().padStart(2,"0")}`},g=e=>{if(!e.createTime||!e.finishTime)return"";const t=new Date(e.createTime).getTime(),a=new Date(e.finishTime).getTime(),l=Math.floor((a-t)/1e3);return l<60?`${l}秒`:`${Math.floor(l/60)}分${l%60}秒`};return(t,i)=>e.e({a:e.o([e=>l.value=e.detail.value,c]),b:l.value,c:e.f(n,(t,a,l)=>({a:e.t(t.label),b:t.value,c:s.value===t.value?1:"",d:e.o(e=>(e=>{s.value=e})(t.value),t.value)})),d:0===r.value.length},0===r.value.length?{}:{e:e.f(r.value,(t,l,s)=>{return e.e({a:e.t(f(t.status)),b:e.n(t.status),c:e.t(v(t.createTime)),d:e.t(t.fileName||"未命名视频"),e:e.t((o=t.fileSize,o?o<1024?o+"B":o<1048576?(o/1024).toFixed(1)+"KB":(o/1048576).toFixed(1)+"MB":"")),f:"completed"===t.status},"completed"===t.status?{g:e.t(g(t))}:{},{h:"failed"===t.status},"failed"===t.status?{i:e.t(t.errorMsg||"处理失败")}:{},{j:"completed"===t.status},"completed"===t.status?{k:e.o(a=>(t=>{e.index.navigateTo({url:`/pages/result/result?taskId=${t._id}`})})(t),t._id)}:{},{l:"failed"===t.status},"failed"===t.status?{m:e.o(a=>(t=>{e.index.showModal({title:"确认重新处理",content:"是否重新处理该视频？",success:async a=>{if(a.confirm)try{await e.wx$1.cloud.callFunction({name:"retry-task",data:{taskId:t._id}}),e.index.showToast({title:"已重新开始处理",icon:"success"}),u()}catch(l){console.error("重新处理失败:",l),e.index.showToast({title:"操作失败",icon:"none"})}}})})(t),t._id)}:{},{n:e.o(l=>(t=>{e.index.showModal({title:"确认删除",content:"删除后无法恢复，是否确认删除？",success:async l=>{if(l.confirm)try{await e.wx$1.cloud.callFunction({name:"delete-task",data:{taskId:t._id}});const l=a.value.findIndex(e=>e._id===t._id);l>-1&&a.value.splice(l,1),e.index.showToast({title:"删除成功",icon:"success"})}catch(s){console.error("删除失败:",s),e.index.showToast({title:"删除失败",icon:"none"})}}})})(t),t._id),o:t._id,p:e.o(a=>(t=>{"processing"===t.status?e.index.navigateTo({url:`/pages/process/process?fileId=${t.originalVideoFileId}`}):"completed"===t.status&&e.index.navigateTo({url:`/pages/result/result?taskId=${t._id}`})})(t),t._id)});var o})},{f:o.value},(o.value,{}),{g:e.o(d)})}}),a=e._export_sfc(t,[["__scopeId","data-v-608a991b"]]);wx.createPage(a);
