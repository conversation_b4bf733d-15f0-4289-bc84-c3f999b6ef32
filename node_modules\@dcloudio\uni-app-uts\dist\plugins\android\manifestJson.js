"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.uniAppManifestPlugin = exports.getOutputManifestJson = void 0;
const path_1 = __importDefault(require("path"));
const fs_extra_1 = __importDefault(require("fs-extra"));
const uni_cli_shared_1 = require("@dcloudio/uni-cli-shared");
const utils_1 = require("./utils");
const utils_2 = require("../utils");
let outputManifestJson = undefined;
function getOutputManifestJson() {
    return outputManifestJson;
}
exports.getOutputManifestJson = getOutputManifestJson;
function uniAppManifestPlugin() {
    const manifestJsonPath = path_1.default.resolve(process.env.UNI_INPUT_DIR, 'manifest.json');
    const manifestJsonUTSPath = path_1.default.resolve(process.env.UNI_INPUT_DIR, uni_cli_shared_1.MANIFEST_JSON_UTS);
    let manifestJson = {};
    return {
        name: 'uni:app-manifest',
        apply: 'build',
        resolveId(id) {
            if ((0, utils_2.isManifest)(id)) {
                return manifestJsonUTSPath;
            }
        },
        load(id) {
            if ((0, utils_2.isManifest)(id)) {
                return fs_extra_1.default.readFileSync(manifestJsonPath, 'utf8');
            }
        },
        transform(code, id) {
            if ((0, utils_2.isManifest)(id)) {
                this.addWatchFile(path_1.default.resolve(process.env.UNI_INPUT_DIR, 'manifest.json'));
                manifestJson = (0, uni_cli_shared_1.parseJson)(code);
                return `export default 'manifest.json'`;
            }
        },
        generateBundle(_, bundle) {
            if (bundle[(0, utils_1.ENTRY_FILENAME)()]) {
                const asset = bundle[(0, utils_1.ENTRY_FILENAME)()];
                const singleThreadCode = manifestJson?.['uni-app-x']?.['singleThread'] === false
                    ? `override singleThread = false`
                    : '';
                const flexDir = (0, uni_cli_shared_1.parseUniXFlexDirection)(manifestJson);
                const flexDirCode = flexDir !== 'column' ? `override flexDirection = "${flexDir}"` : '';
                const splashScreen = (0, uni_cli_shared_1.parseUniXSplashScreen)(manifestJson);
                const splashScreenCode = splashScreen && Object.keys(splashScreen).length > 0
                    ? `override splashScreen: Map<string, any> | null = ${(0, utils_1.stringifyMap)(splashScreen)}`
                    : '';
                const uniStatistics = (0, uni_cli_shared_1.parseUniXUniStatistics)(manifestJson);
                const uniStatisticsCode = uniStatistics && Object.keys(uniStatistics).length > 0
                    ? `override uniStatistics: UTSJSONObject | null = ${JSON.stringify(uniStatistics)}`
                    : '';
                const hasAppDefaultAppTheme = (0, uni_cli_shared_1.validateThemeValue)(manifestJson.app?.defaultAppTheme);
                const hasDefaultAppTheme = (0, uni_cli_shared_1.validateThemeValue)(manifestJson.defaultAppTheme);
                const defaultAppThemeCode = hasAppDefaultAppTheme
                    ? `override defaultAppTheme: string = "${manifestJson.app.defaultAppTheme}"`
                    : hasDefaultAppTheme
                        ? `override defaultAppTheme: string = "${manifestJson.defaultAppTheme}"`
                        : '';
                const codes = [
                    singleThreadCode,
                    flexDirCode,
                    splashScreenCode,
                    defaultAppThemeCode,
                    uniStatisticsCode,
                ]
                    .filter(Boolean)
                    .join('\n');
                asset.source =
                    asset.source +
                        `
export class UniAppConfig extends io.dcloud.uniapp.appframe.AppConfig {
    override name: string = "${manifestJson.name || ''}"
    override appid: string = "${manifestJson.appid || ''}"
    override versionName: string = "${manifestJson.versionName || ''}"
    override versionCode: string = "${manifestJson.versionCode || ''}"
    override uniCompilerVersion: string = "${process.env.UNI_COMPILER_VERSION || ''}"
    ${codes}
    constructor() { super() }
}
`;
            }
        },
        writeBundle() {
            outputManifestJson = (0, utils_2.normalizeManifestJson)(manifestJson);
            if (process.env.NODE_ENV !== 'production') {
                // 发行模式下，需要等解析ext-api模块
                fs_extra_1.default.outputFileSync(path_1.default.resolve(process.env.UNI_OUTPUT_DIR, 'manifest.json'), JSON.stringify(outputManifestJson, null, 2));
            }
        },
    };
}
exports.uniAppManifestPlugin = uniAppManifestPlugin;
