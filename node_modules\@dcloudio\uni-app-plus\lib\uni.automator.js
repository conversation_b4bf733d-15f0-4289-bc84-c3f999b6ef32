"use strict";var e=require("fs"),t=require("path"),s=require("debug"),i=require("jsonc-parser"),r=require("fs-extra"),a=require("postcss-selector-parser"),o=require("licia/dateFormat"),n=require("util"),l=require("child_process"),c=require("dns");function d(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var u=d(e),p=d(t),h=d(s),m=d(r),f=d(a),y=d(o),v=d(c);function _(e){e.walk((e=>{if("tag"===e.type){const t=e.value;e.value="page"===t?"body":"uni-"+t}}))}h.default("automator:devtool");const I=["Page.getElement","Page.getElements","Element.getElement","Element.getElements"];const P=/^win/.test(process.platform);function $(e){try{return require(e)}catch(t){return require(require.resolve(e,{paths:[process.cwd()]}))}}const g=h.default("automator:launcher"),E=n.promisify(u.default.readdir),w=n.promisify(u.default.stat);async function N(e){const s=await E(e);return(await Promise.all(s.map((async s=>{const i=t.resolve(e,s);return(await w(i)).isDirectory()?N(i):i})))).reduce(((e,t)=>e.concat(t)),[])}class S{constructor(e){this.isX=!1,"true"===process.env.UNI_APP_X&&(this.isX=!0),this.id=e.id,this.app=e.executablePath,this.appid=e.appid||process.env.UNI_APP_ID||(this.isX?"__UNI__uniappx":"HBuilder"),this.package=e.package||(this.isX?"io.dcloud.uniappx":"io.dcloud.HBuilder"),this.activity=e.activity||(this.isX?"io.dcloud.uniapp.UniAppActivity":"io.dcloud.PandoraEntry")}shouldPush(){return this.exists(this.FILE_APP_SERVICE).then((()=>(g(`${y.default("yyyy-mm-dd HH:MM:ss:l")} ${this.FILE_APP_SERVICE} exists`),!1))).catch((()=>(g(`${y.default("yyyy-mm-dd HH:MM:ss:l")} ${this.FILE_APP_SERVICE} not exists`),!0)))}push(e){return N(e).then((s=>{const i=s.map((s=>{const i=(e=>P?e.replace(/\\/g,"/"):e)(t.join(this.DIR_WWW,t.relative(e,s)));return g(`${y.default("yyyy-mm-dd HH:MM:ss:l")} push ${s} ${i}`),this.pushFile(s,i)}));return Promise.all(i)})).then((e=>!0))}get FILE_APP_SERVICE(){return`${this.DIR_WWW}/app-service.js`}}const A=h.default("automator:simctl");function D(e){const t=parseInt(e);return t>9?String(t):"0"+t}class U extends S{constructor(){super(...arguments),this.bundleVersion=""}async init(){const e=$("node-simctl").Simctl;this.tool=new e({udid:this.id});try{await this.tool.bootDevice()}catch(e){}await this.initSDCard(),A(`${y.default("yyyy-mm-dd HH:MM:ss:l")} init ${this.id}`)}async initSDCard(){const e=await this.tool.appInfo(this.package);A(`${y.default("yyyy-mm-dd HH:MM:ss:l")} appInfo ${e}`);const t=e.match(/DataContainer\s+=\s+"(.*)"/);if(!t)return Promise.resolve("");const s=e.match(/CFBundleVersion\s+=\s+(.*);/);if(!s)return Promise.resolve("");this.sdcard=t[1].replace("file:",""),this.bundleVersion=s[1],A(`${y.default("yyyy-mm-dd HH:MM:ss:l")} install ${this.sdcard}`)}async version(){return Promise.resolve(this.bundleVersion)}formatVersion(e){const t=e.split(".");return 3!==t.length?e:t[0]+D(t[1])+D(t[2])}async install(){return A(`${y.default("yyyy-mm-dd HH:MM:ss:l")} install ${this.app}`),await this.tool.installApp(this.app),await this.tool.grantPermission(this.package,"all"),await this.initSDCard(),Promise.resolve(!0)}async start(){A("ios simulator start");try{await this.tool.terminateApp(this.package)}catch(e){console.error("ios simulator start terminateApp fail",e)}try{await this.tool.launchApp(this.package)}catch(e){console.error("ios simulator start launchApp fail",e),console.error(e)}return Promise.resolve(!0)}async exit(){return await this.tool.terminateApp(this.package),await this.tool.shutdownDevice(),Promise.resolve(!0)}async captureScreenshot(e){const t=await Promise.resolve(await this.tool.getScreenshot());return new Promise(((s,i)=>{var r,a;if(void 0!==(null===(r=null==e?void 0:e.area)||void 0===r?void 0:r.x)&&void 0!==(null===(a=null==e?void 0:e.area)||void 0===a?void 0:a.y)){const r=require("jimp");r.read(Buffer.from(t,"base64")).then((t=>{const a=e.area.x,o=e.area.y;let n=t.bitmap.width-a;e.area.width&&(n=Math.min(n,e.area.width));let l=t.bitmap.height-o;e.area.height&&(l=Math.min(l,e.area.height)),t.crop(a,o,n,l).getBase64Async(r.MIME_PNG).then((e=>{s(e.replace("data:image/png;base64,",""))})).catch((e=>{i(e)}))})).catch((e=>{i(e)}))}else s(t)}))}exists(e){return m.default.existsSync(e)?Promise.resolve(!0):Promise.reject(Error(`${e} not exists`))}pushFile(e,t){return Promise.resolve(m.default.copySync(e,t))}adbCommand(e){return new Promise((e=>{e("adbCommand only for App Android!")}))}swipe(e){return new Promise((t=>{const{startPoint:s,endPoint:i,duration:r}=e;l.exec(`idb connect ${this.id} && idb ui swipe ${s.x} ${s.y} ${i.x} ${i.y} --duration ${r?r/1e3:.1} --udid ${this.id}`,(e=>{if(e)return console.error(`exec error: ${e}`),void t(`swipe fail: ${e}`);t("swipe success")}))}))}tap(e){return new Promise((t=>{const{x:s,y:i,duration:r}=e;l.exec(`idb connect ${this.id} && idb ui tap ${s} ${i} --duration ${r?r/1e3:0} --udid ${this.id}`,(e=>{if(e)return console.error(`exec error: ${e}`),void t(`tap fail: ${e}`);t("tap success")}))}))}keyboardInput(e){return new Promise((t=>{l.exec(`idb connect ${this.id} && idb ui text ${e} --udid ${this.id}`,(e=>{if(e)return console.error(`exec error: ${e}`),void t(`tap fail: ${e}`);t("keyboardInput success")})),t("keyboardInput success")}))}get DIR_WWW(){return"true"===process.env.UNI_APP_X?`${this.sdcard}/Documents/uni-app-x/apps/__UNI__uniappx/www/`:`${this.sdcard}/Documents/Pandora/apps/${this.appid}/www/`}}const b=$("adbkit"),x=h.default("automator:adb");class M extends S{constructor(){super(...arguments),this.needStart=!0}async init(){if(console.log("app-plus/launcher/android.ts init start"),void 0!==v.default.setDefaultResultOrder&&v.default.setDefaultResultOrder("ipv4first"),console.log("app-plus/launcher/android.ts init before adb.createClient"),this.tool=b.createClient(),console.log("app-plus/launcher/android.ts init after adb.createClient"),x(`${y.default("yyyy-mm-dd HH:MM:ss:l")} init ${await this.tool.version()}`),console.log("app-plus/launcher/android.ts init after debugClient init this.tool.version"),!this.id){console.log("app-plus/launcher/android.ts init before listDevices");const e=await this.tool.listDevices();if(!e.length)throw Error("Device not found");console.log("app-plus/launcher/android.ts init this.id",this.id),this.id=e[0].id}console.log("app-plus/launcher/android.ts init before echo ${$EXTERNAL_STORAGE}"),this.sdcard=(await this.shell(this.COMMAND_EXTERNAL)).trim(),console.log("app-plus/launcher/android.ts init after echo ${$EXTERNAL_STORAGE}",this.sdcard),x(`${y.default("yyyy-mm-dd HH:MM:ss:l")} init ${this.id} ${this.sdcard}`),console.log("app-plus/launcher/android.ts init after debugClient init this.id this.sdcard")}root(){return this.tool.root(this.id).then((()=>{x(`${y.default("yyyy-mm-dd HH:MM:ss:l")} root ${this.id} ${this.sdcard}`)})).catch((e=>{x(`${y.default("yyyy-mm-dd HH:MM:ss:l")} root ${this.id} ${e}`)}))}version(){return this.shell(this.COMMAND_VERSION).then((e=>{const t=e.match(/versionName=(.*)/);return t&&t.length>1?t[1]:""}))}formatVersion(e){return e}async install(){let e=!0;try{const t=(await this.tool.getProperties(this.id))["ro.build.version.release"].split(".")[0];parseInt(t)<6&&(e=!1)}catch(e){}if(x(`${y.default("yyyy-mm-dd HH:MM:ss:l")} install ${this.app} permission=${e}`),e){const e=$("adbkit/lib/adb/command.js"),t=e.prototype._send;e.prototype._send=function(e){return 0===e.indexOf("shell:pm install -r ")&&(e=e.replace("shell:pm install -r ","shell:pm install -r -g "),x(`${y.default("yyyy-mm-dd HH:MM:ss:l")} ${e} `)),t.call(this,e)}}return this.tool.install(this.id,this.app).then((()=>{x(`${y.default("yyyy-mm-dd HH:MM:ss:l")} installed`),this.init(),console.log("app-plus/launcher/android.ts this.tool.install after this.init")}))}start(){return this.needStart?this.exit().then((()=>this.shell(this.COMMAND_START))):Promise.resolve()}exit(){return this.shell(this.COMMAND_STOP)}captureScreenshot(e){return this.tool.screencap(this.id).then((t=>new Promise(((s,i)=>{const r=[];t.on("data",(function(e){r.push(e)})),t.on("end",(function(){var t,a;if(void 0!==(null===(t=e.area)||void 0===t?void 0:t.x)&&void 0!==(null===(a=e.area)||void 0===a?void 0:a.y)){const t=require("jimp");t.read(Buffer.concat(r)).then((r=>{var a,o,n,l;const c=e.area.x,d=e.area.y;let u=r.bitmap.width-c;(null===(a=e.area)||void 0===a?void 0:a.width)&&(u=Math.min(u,null===(o=e.area)||void 0===o?void 0:o.width));let p=r.bitmap.height-d;(null===(n=e.area)||void 0===n?void 0:n.height)&&(p=Math.min(p,null===(l=e.area)||void 0===l?void 0:l.height)),r.crop(c,d,u,p).getBase64Async(t.MIME_PNG).then((e=>{s(e.replace("data:image/png;base64,",""))})).catch((e=>{i(e)}))})).catch((e=>{i(e)}))}else s(Buffer.concat(r).toString("base64"))}))}))))}adbCommand(e){return new Promise((t=>{this.tool.shell(this.id,e).then((e=>{let s,i="";e.on("data",(e=>{i+=e.toString(),s&&clearTimeout(s),s=setTimeout((()=>{t(i)}),50)})),setTimeout((()=>{t(i)}),1500)}))}))}swipe(e){return new Promise((t=>{this.tool.shell(this.id,"wm density").then((s=>{let i,r="";s.on("data",(s=>{r+=s.toString(),i&&clearTimeout(i),i=setTimeout((()=>{var s;const i=parseInt(null===(s=r.split(":")[1])||void 0===s?void 0:s.trim()),a=i?i/160:1,o=e.startPoint.x*a,n=e.startPoint.y*a,l=e.endPoint.x*a,c=e.endPoint.y*a;this.tool.shell(this.id,`input swipe ${o} ${n} ${l} ${c} ${e.duration||100}`).then((e=>{let s,i="";e.on("data",(e=>{i+=e.toString(),s&&clearTimeout(s),s=setTimeout((()=>{t(i)}),50)})),setTimeout((()=>{t(i)}),1500)}))}),50)}))}))}))}keyboardInput(e){return new Promise((t=>{this.tool.shell(this.id,`input text ${e}`).then((e=>{let s,i="";e.on("data",(e=>{i+=e.toString(),s&&clearTimeout(s),s=setTimeout((()=>{t(i)}),50)})),setTimeout((()=>{t(i)}),1500)}))}))}tap(e){return new Promise((t=>{this.tool.shell(this.id,"wm density").then((s=>{let i,r="";s.on("data",(s=>{r+=s.toString(),i&&clearTimeout(i),i=setTimeout((()=>{var s;const i=parseInt(null===(s=r.split(":")[1])||void 0===s?void 0:s.trim()),a=i?i/160:1,o=e.x*a,n=e.y*a;this.tool.shell(this.id,`input swipe ${o} ${n} ${o} ${n} ${e.duration||0}`).then((e=>{let s,i="";e.on("data",(e=>{i+=e.toString(),s&&clearTimeout(s),s=setTimeout((()=>{t(i)}),50)})),setTimeout((()=>{t(i)}),1500)}))}),50)}))}))}))}exists(e){return this.tool.stat(this.id,e)}pushFile(e,t){return this.tool.push(this.id,e,t)}async push(e){if(!process.env.UNI_HBUILDERX_PLUGINS)return super.push(e);const t=p.default.join(process.env.UNI_HBUILDERX_PLUGINS,"launcher","out","export","pushResources.js"),s=process.env.HX_CONFIG_ADB_PATH||p.default.join(process.env.UNI_HBUILDERX_PLUGINS,"launcher-tools","tools","adbs","adb"),i=[t,s].map((e=>u.default.promises.access(e,u.default.constants.F_OK).then((()=>`${e} exists`)).catch((()=>`${e} not exists`))));return Promise.all(i).then((()=>{const{PushResources:i}=require(t);return new i({adbPath:s,appid:process.env.UNI_TEST_BASE_APPID||this.appid,uuid:this.id,packageName:process.env.UNI_TEST_BASE_PACKAGE_NAME||this.package,sourcePath:e}).start(),this.needStart=!1,!0})).catch((async t=>(console.log("pushResources or adb not exists: ",t),await super.push(e))))}shell(e){return x(`${y.default("yyyy-mm-dd HH:MM:ss:l")} SEND ► ${e}`),this.tool.shell(this.id,e).then(b.util.readAll).then((e=>{const t=e.toString();return x(`${y.default("yyyy-mm-dd HH:MM:ss:l")} ◀ RECV ${t}`),t}))}get DIR_WWW(){return`/storage/emulated/0/Android/data/${this.package}/apps/${this.appid}/www`}get COMMAND_EXTERNAL(){return"echo $EXTERNAL_STORAGE"}get COMMAND_VERSION(){return`dumpsys package ${this.package}`}get COMMAND_STOP(){return`am force-stop ${this.package}`}get COMMAND_START(){return`am start -n ${this.package}/${this.activity} --es appid ${this.appid} --ez needUpdateApp false --ez reload true --ez externalStorage true`}}const T=h.default("automator:devtool");let R,H=!1;const C={android:/android_version=(.*)/,ios:/iphone_version=(.*)/};const k={"Tool.close":{reflect:async()=>{}},"App.exit":{reflect:async()=>R.exit()},"App.enableLog":{reflect:()=>Promise.resolve()},"App.captureScreenshotWithDevice":{reflect:async(e,t)=>{const s=await R.captureScreenshot(t);return T(`App.captureScreenshot ${s.length}`),{data:s}}},"App.adbCommand":{reflect:async(e,t)=>{const s=await R.adbCommand(t);return T(`App.adbCommand ${s.length}`),{data:s}}},"App.swipe":{reflect:async(e,t)=>{const s=await R.swipe(t);return T(`App.swipe ${s.length}`),{data:s}}},"App.tap":{reflect:async(e,t)=>{const s=await R.tap(t);return T(`App.tap ${s.length}`),{data:s}}},"App.keyboardInput":{reflect:async(e,t)=>{const s=await R.keyboardInput(t);return T(`App.keyboardInput ${s.length}`),{data:s}}}};!function(e){I.forEach((t=>{e[t]=function(e){return{reflect:async(t,s)=>t(e,s,!1),params:e=>(e.selector&&(e.selector=f.default(_).processSync(e.selector)),e)}}(t)}))}(k);const O={devtools:{name:"App",paths:[],required:["manifest.json","app-service.js"],validate:async function(e,t){e.platform=(e.platform||process.env.UNI_OS_NAME).toLocaleLowerCase(),Object.assign(e,e[e.platform]),R=function(e,t){return"ios"===e?new U(t):new M(t)}(e.platform,e),await R.init();const s=await R.version();if(s){if(e.version){const t=R.formatVersion(function(e,t){if(e.endsWith(".txt"))try{const s=u.default.readFileSync(e).toString().match(C[t]);if(s)return s[1]}catch(e){console.error(e)}return e}(e.version,e.platform));T(`version: ${s}`),T(`newVersion: ${t}`),t!==s&&(H=!0)}}else H=!0;if(H){if(!e.executablePath)throw Error(`app-plus->${e.platform}->executablePath is not provided`);if(!u.default.existsSync(e.executablePath))throw Error(`${e.executablePath} not exists`)}return e},create:async function(e,t,s){H&&await R.install(),(H||s.compiled||await R.shouldPush())&&await R.push(e),await R.start()}},adapter:k,beforeCompile(e){if(process.env.UNI_INPUT_DIR&&"true"===process.env.UNI_AUTOMATOR_APP_WEBVIEW){const t=i.parse(r.readFileSync(p.default.resolve(process.env.UNI_INPUT_DIR,"manifest.json"),"utf8")),s=p.default.resolve(process.env.UNI_INPUT_DIR,"unpackage",".automator","app-webview");process.env.UNI_INPUT_DIR=p.default.resolve(s,"src"),process.env.UNI_OUTPUT_DIR=p.default.resolve(s,"unpackage","dist","dev"),r.existsSync(process.env.UNI_INPUT_DIR)&&r.emptyDirSync(process.env.UNI_INPUT_DIR),r.copySync(p.default.resolve(e,"lib","app-webview","project"),process.env.UNI_INPUT_DIR);const a=i.parse(r.readFileSync(p.default.resolve(process.env.UNI_INPUT_DIR,"manifest.json"),"utf8"));r.writeFileSync(p.default.resolve(process.env.UNI_INPUT_DIR,"manifest.json"),JSON.stringify(Object.assign(Object.assign({},a),{name:t.name||"",appid:t.appid||""}),null,2))}else if(process.env.UNI_INPUT_DIR&&"ios"===process.env.UNI_OS_NAME&&R.app&&R.app.endsWith(".app")){const e=p.default.resolve(process.env.UNI_INPUT_DIR,"uni_modules");if(!u.default.existsSync(e))return;if(!r.readdirSync(e).some((t=>r.existsSync(p.default.resolve(e,t,"utssdk")))))return;process.env.UNI_APP_X="true"===process.env.UNI_APP_X?"1":"",process.env.HX_DEPENDENCIES_DIR=p.default.join(process.env.UNI_OUTPUT_DIR,"../../../cache/.automator/uts_standard_simulator"),process.env.HX_RUN_DEVICE_TYPE="ios_simulator",R.app&&(process.env.UTS_BASE_INFO=u.default.readFileSync(p.default.resolve(R.app,"Frameworks/DCloudUTSFoundation.framework/uts-info.json"),"utf8")),!process.env.HX_Version&&process.env.UNI_HBUILDERX_PLUGINS&&(process.env.HX_Version=require(p.default.join(process.env.UNI_HBUILDERX_PLUGINS,"about","package.json")).version),T("HX_DEPENDENCIES_DIR",process.env.HX_DEPENDENCIES_DIR),T("UTS_BASE_INFO",process.env.UTS_BASE_INFO)}},afterCompile(){if("ios_simulator"===process.env.HX_RUN_DEVICE_TYPE&&R.app){process.env.UNI_APP_X="1"===process.env.UNI_APP_X?"true":"false";const t=p.default.resolve(process.env.HX_DEPENDENCIES_DIR,"Resources"),s=p.default.resolve(process.env.HX_DEPENDENCIES_DIR,"modules"),i=u.default.existsSync(t),a=u.default.existsSync(s),o=(e=p.default.basename(R.app),p.default.resolve(process.env.HX_DEPENDENCIES_DIR,".automator/"+e));if(!i&&!a)return;if(r.existsSync(o)&&r.emptyDirSync(o),r.copySync(R.app,o),R.app=o,i&&r.copySync(t,p.default.resolve(R.app)),a){const e=[];r.readdirSync(s).forEach((t=>{t.endsWith(".framework")&&(e.push(t),r.copySync(p.default.join(s,t),p.default.resolve(R.app,"Frameworks",t)))})),e.forEach((e=>{const t=`'${process.env.UNI_HBUILDERX_PLUGINS}/launcher-tools/tools/uts/optool' 'install' '-c' 'weak' '-p' '@rpath/${e}/${e.replace(".framework","")}' '-t' '${R.app}'`;try{const e=l.execSync(t);T(t,e.toString())}catch(e){console.error(e.message),console.error(e.stdout.toString())}}))}if(i||a){const e=l.execSync(`codesign -fs "-" "${R.app}"`);T("codesign success",e.toString())}}var e}};module.exports=O;
