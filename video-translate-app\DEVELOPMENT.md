# 开发环境配置指南

## 快速开始

### 1. 环境准备

确保你的开发环境满足以下要求：

- **Node.js**: >= 16.0.0
- **npm**: >= 8.0.0
- **微信开发者工具**: 最新稳定版
- **VSCode**: 推荐使用（已配置相关插件设置）

### 2. 项目初始化

```bash
# 进入项目目录
cd video-translate-app

# 安装依赖
npm install

# 启动微信小程序开发模式
npm run dev:mp-weixin
```

### 3. 微信开发者工具配置

1. 打开微信开发者工具
2. 选择"导入项目"
3. 项目目录选择：`video-translate-app/dist/dev/mp-weixin`
4. AppID：暂时可以选择"测试号"
5. 项目名称：视语翻译

### 4. VSCode开发配置

项目已预配置VSCode开发环境：

- **插件推荐**：
  - Vetur (Vue语法支持)
  - TypeScript Vue Plugin (Vue 3 + TS支持)
  - uni-app插件
  - ESLint
  - Prettier

- **调试配置**：
  - 按F5启动调试
  - 支持微信小程序和H5两种调试模式

## 开发流程

### 1. 启动开发服务器

```bash
# 微信小程序开发
npm run dev:mp-weixin

# H5开发调试
npm run dev:h5

# 其他平台
npm run dev:mp-alipay    # 支付宝小程序
npm run dev:mp-baidu     # 百度小程序
```

### 2. 实时预览

- **微信小程序**：在微信开发者工具中实时预览
- **H5**：浏览器访问 http://localhost:5173

### 3. 代码热更新

项目支持热更新，修改代码后会自动重新编译和刷新。

## 项目结构说明

```
video-translate-app/
├── src/                    # 源代码目录
│   ├── components/         # 公共组件
│   ├── pages/             # 页面文件
│   ├── utils/             # 工具函数
│   ├── static/            # 静态资源
│   └── ...
├── dist/                  # 编译输出目录
│   ├── dev/               # 开发模式输出
│   └── build/             # 生产模式输出
├── .vscode/               # VSCode配置
├── scripts/               # 构建脚本
└── ...
```

## 开发规范

### 1. 代码风格

- 使用TypeScript编写
- 遵循Vue 3 Composition API规范
- 使用2空格缩进
- 文件名使用kebab-case命名

### 2. 组件开发

```vue
<template>
  <view class="component-name">
    <!-- 模板内容 -->
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'

// 组件逻辑
</script>

<style scoped>
/* 样式使用scoped避免污染 */
</style>
```

### 3. API调用

使用封装的API工具函数：

```typescript
import { callCloudFunction } from '@/utils/api'

// 调用云函数
const result = await callCloudFunction('function-name', data)
```

## 调试技巧

### 1. 控制台调试

- 微信开发者工具：调试器 -> Console
- H5模式：浏览器开发者工具

### 2. 网络请求调试

- 微信开发者工具：调试器 -> Network
- 查看云函数调用情况

### 3. 真机调试

- 微信开发者工具：预览 -> 扫码真机调试
- 查看真机运行效果

## 常见问题

### 1. 编译错误

**问题**：`Cannot resolve module '@/utils/api'`
**解决**：检查tsconfig.json中的路径映射配置

### 2. 云函数调用失败

**问题**：`cloud function not found`
**解决**：
1. 确认云开发环境已开通
2. 检查云函数是否已部署
3. 确认函数名称正确

### 3. 视频上传失败

**问题**：上传SDK报错
**解决**：
1. 检查网络连接
2. 确认VOD服务配置
3. 查看控制台错误信息

### 4. 样式不生效

**问题**：CSS样式在小程序中不显示
**解决**：
1. 使用rpx单位而不是px
2. 避免使用不支持的CSS属性
3. 检查选择器是否正确

## 构建发布

### 1. 生产构建

```bash
# 微信小程序构建
npm run build:mp-weixin

# H5构建
npm run build:h5
```

### 2. 发布流程

1. 运行生产构建命令
2. 在微信开发者工具中上传代码
3. 在微信公众平台提交审核
4. 审核通过后发布

## 性能优化

### 1. 包体积优化

- 按需引入第三方库
- 压缩图片资源
- 移除未使用的代码

### 2. 运行时优化

- 合理使用缓存
- 避免频繁的数据更新
- 优化长列表渲染

## 技术支持

如遇到开发问题，可以：

1. 查看项目README.md
2. 检查uni-app官方文档
3. 查看微信小程序开发文档
4. 联系项目维护者

---

祝你开发愉快！🚀
