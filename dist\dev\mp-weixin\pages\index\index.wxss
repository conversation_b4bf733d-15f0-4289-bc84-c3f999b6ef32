
.home-container.data-v-83a5a03c {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 40rpx;
}
.header-section.data-v-83a5a03c {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 60rpx;
  padding-top: 40rpx;
}
.welcome-text .app-title.data-v-83a5a03c {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 10rpx;
}
.welcome-text .app-subtitle.data-v-83a5a03c {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}
.logo.data-v-83a5a03c {
  width: 120rpx;
  height: 120rpx;
  border-radius: 20rpx;
}
.features-section.data-v-83a5a03c {
  display: flex;
  justify-content: space-between;
  margin-bottom: 60rpx;
}
.feature-item.data-v-83a5a03c {
  flex: 1;
  text-align: center;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  padding: 40rpx 20rpx;
  margin: 0 10rpx;
}
.feature-icon.data-v-83a5a03c {
  display: block;
  font-size: 60rpx;
  margin-bottom: 20rpx;
}
.feature-title.data-v-83a5a03c {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 10rpx;
}
.feature-desc.data-v-83a5a03c {
  display: block;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
}
.quick-start.data-v-83a5a03c {
  text-align: center;
  margin-bottom: 60rpx;
}
.start-btn.data-v-83a5a03c {
  width: 60%;
  height: 88rpx;
  background-color: #ff6b6b;
  border: none;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}
.start-tip.data-v-83a5a03c {
  display: block;
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
}
.recent-section.data-v-83a5a03c {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 40rpx;
}
.section-header.data-v-83a5a03c {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}
.section-title.data-v-83a5a03c {
  font-size: 32rpx;
  font-weight: bold;
  color: white;
}
.more-link.data-v-83a5a03c {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
}
.recent-list.data-v-83a5a03c {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}
.recent-item.data-v-83a5a03c {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15rpx;
  padding: 25rpx;
}
.task-info .task-name.data-v-83a5a03c {
  display: block;
  font-size: 28rpx;
  color: white;
  margin-bottom: 8rpx;
}
.task-info .task-time.data-v-83a5a03c {
  display: block;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.6);
}
.task-status.data-v-83a5a03c {
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  color: white;
}
.task-status.processing.data-v-83a5a03c {
  background-color: #1890ff;
}
.task-status.completed.data-v-83a5a03c {
  background-color: #52c41a;
}
.task-status.failed.data-v-83a5a03c {
  background-color: #ff4d4f;
}
.help-section.data-v-83a5a03c {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  padding: 30rpx;
}
.help-title.data-v-83a5a03c {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 30rpx;
  text-align: center;
}
.help-steps.data-v-83a5a03c {
  display: flex;
  flex-direction: column;
  gap: 25rpx;
}
.help-step.data-v-83a5a03c {
  display: flex;
  align-items: center;
}
.step-number.data-v-83a5a03c {
  width: 60rpx;
  height: 60rpx;
  background-color: #ff6b6b;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: bold;
  margin-right: 20rpx;
}
.step-text.data-v-83a5a03c {
  font-size: 28rpx;
  color: white;
}
