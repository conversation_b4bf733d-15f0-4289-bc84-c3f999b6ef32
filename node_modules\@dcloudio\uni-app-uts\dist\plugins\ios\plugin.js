"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.uniAppIOSPlugin = exports.initUniAppIosCssPlugin = void 0;
const path_1 = __importDefault(require("path"));
const fs_extra_1 = __importDefault(require("fs-extra"));
const uni_cli_shared_1 = require("@dcloudio/uni-cli-shared");
const utils_1 = require("../utils");
const css_1 = require("./css");
function initUniAppIosCssPlugin(config) {
    (0, uni_cli_shared_1.injectCssPlugin)(config, process.env.UNI_COMPILE_TARGET === 'uni_modules'
        ? {
            createUrlReplacer: uni_cli_shared_1.createEncryptCssUrlReplacer,
        }
        : {});
    (0, uni_cli_shared_1.injectCssPostPlugin)(config, (0, css_1.uniAppCssPlugin)(config));
}
exports.initUniAppIosCssPlugin = initUniAppIosCssPlugin;
function uniAppIOSPlugin() {
    const inputDir = process.env.UNI_INPUT_DIR;
    const outputDir = process.env.UNI_OUTPUT_DIR;
    const uvueOutputDir = (0, uni_cli_shared_1.uvueOutDir)('app-ios');
    const tscOutputDir = (0, uni_cli_shared_1.tscOutDir)('app-ios');
    // 开始编译时，清空输出目录
    function emptyOutDir() {
        // ext-api 编译时，需要同时编译多个平台，并保留多个平台的输出目录
        if (process.env.UNI_COMPILE_TARGET === 'ext-api') {
            return;
        }
        if (fs_extra_1.default.existsSync(outputDir)) {
            (0, uni_cli_shared_1.emptyDir)(outputDir);
        }
    }
    emptyOutDir();
    function emptyUVueDir() {
        if (fs_extra_1.default.existsSync(uvueOutputDir)) {
            (0, uni_cli_shared_1.emptyDir)(uvueOutputDir);
        }
    }
    emptyUVueDir();
    function emptyTscDir() {
        if (fs_extra_1.default.existsSync(tscOutputDir)) {
            (0, uni_cli_shared_1.emptyDir)(tscOutputDir);
        }
    }
    emptyTscDir();
    return {
        name: 'uni:app-uts',
        apply: 'build',
        uni: (0, utils_1.createUniOptions)('ios'),
        config() {
            return {
                base: '/', // 强制 base
                build: {
                    sourcemap: (0, uni_cli_shared_1.enableSourceMap)(), //enableSourceMap() ? 'hidden' : false,
                    emptyOutDir: false,
                    assetsInlineLimit: 0,
                    rollupOptions: {
                        input: (0, uni_cli_shared_1.resolveMainPathOnce)(inputDir),
                        external: ['vue', '@vue/shared'],
                        output: {
                            name: 'AppService',
                            banner: ``,
                            format: 'iife',
                            entryFileNames: uni_cli_shared_1.APP_SERVICE_FILENAME,
                            globals: {
                                vue: 'Vue',
                                '@vue/shared': 'uni.VueShared',
                            },
                            sourcemapPathTransform: (relativeSourcePath, sourcemapPath) => {
                                return (0, uni_cli_shared_1.normalizePath)(path_1.default.relative(process.env.UNI_INPUT_DIR, path_1.default.resolve(path_1.default.dirname(sourcemapPath), relativeSourcePath)));
                            },
                        },
                    },
                },
            };
        },
        configResolved(config) {
            (0, utils_1.configResolved)(config);
            initUniAppIosCssPlugin(config);
        },
        generateBundle(_, bundle) {
            const APP_SERVICE_FILENAME_MAP = uni_cli_shared_1.APP_SERVICE_FILENAME + '.map';
            const appServiceMap = bundle[APP_SERVICE_FILENAME_MAP];
            if (appServiceMap && appServiceMap.type === 'asset') {
                const source = JSON.parse(appServiceMap.source);
                source.sourceRoot = (0, uni_cli_shared_1.normalizePath)(inputDir);
                const newSourceMapFileName = path_1.default.resolve(process.env.UNI_APP_X_CACHE_DIR, 'sourcemap', APP_SERVICE_FILENAME_MAP);
                fs_extra_1.default.outputFileSync(newSourceMapFileName, JSON.stringify(source));
                delete bundle[APP_SERVICE_FILENAME_MAP];
                const appService = bundle[uni_cli_shared_1.APP_SERVICE_FILENAME];
                if (appService && appService.type === 'chunk') {
                    appService.code = appService.code.replace(`//# sourceMappingURL=app-service.js.map`, `//# sourceMappingURL=` +
                        path_1.default.relative(process.env.UNI_OUTPUT_DIR, newSourceMapFileName));
                }
            }
        },
        async writeBundle() {
            // x 上暂时编译所有uni ext api，不管代码里是否调用了
            await (0, uni_cli_shared_1.buildUniExtApis)();
        },
    };
}
exports.uniAppIOSPlugin = uniAppIOSPlugin;
