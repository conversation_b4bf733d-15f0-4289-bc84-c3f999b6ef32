// @ts-expect-error
import type { Callback } from '../../index.uts'
// @ts-expect-error
import { parsePage } from '../util.uts'
// @ts-expect-error
import { send } from '../../index.uts'
import {
  type FirstSocketTaskEmitterParams,
  connectSocket,
  firstSocketTaskEmitter,
  // @ts-expect-error
} from './Socket.uts'

export const getPageStack = (callback: Callback): void => {
  callback(
    {
      // @ts-expect-error
      pageStack: getCurrentPages().map((page: UniPage): UTSJSONObject => {
        return parsePage(page.vm!)
      }),
    },
    null
  )
}
export type GetCurrentPageParams = {
  // @ts-expect-error
  callback: (result: UTSJSONObject | null, error: any | null) => void
}
function _getCurrentPage(): Page | null {
  const pages = getCurrentPages() as UniPage[]
  return pages.length > 0 ? pages[pages.length - 1].vm! : null
}

export const getCurrentPage = (params: GetCurrentPageParams): void => {
  const page = _getCurrentPage()
  const result = page != null ? parsePage(page) : null
  params.callback(result, null)
}

export type CallUniMethodParams = {
  method: string
  args: any[]
}

export const callUniMethod = (
  params: CallUniMethodParams,
  callback: Callback
): void => {
  const method = params.method
  const args = params.args
  const success = (result: any) => {
    const timeout = method == 'pageScrollTo' ? 350 : 0
    setTimeout(() => {
      callback({ result }, null)
    }, timeout)
  }
  const onApiCallback = (data: any | null, _: any | null) => {
    const id = args[0] as string
    send({ id, result: { method, data } })
  }
  switch (method) {
    case 'navigateTo':
      // @ts-expect-error
      const _arg = new UTSJSONObject(args[0])
      uni.navigateTo({
        url: _arg['url'] as string,
        // @ts-expect-error
        animationType:
          _arg['animationType'] != null
            ? (_arg['animationType'] as string)
            : 'pop-in',
        animationDuration:
          _arg['animationDuration'] != null
            ? (_arg['animationDuration'] as number)
            : 300,
        success,
        fail(error) {
          error.errMsg = error.errMsg.replace(`${method}: fail `, '')
          callback(null, error)
        },
      })
      break

    case 'redirectTo':
      uni.redirectTo({
        // @ts-expect-error
        url: new UTSJSONObject(args[0])['url'] as string,
        success,
        fail(error) {
          error.errMsg = error.errMsg.replace(`${method}: fail `, '')
          callback(null, error)
        },
      })
      break

    case 'reLaunch':
      uni.reLaunch({
        // @ts-expect-error
        url: new UTSJSONObject(args[0])['url'] as string,
        success,
        fail(error) {
          error.errMsg = error.errMsg.replace(`${method}: fail `, '')
          callback(null, error)
        },
      })
      break

    case 'navigateBack':
      // @ts-expect-error
      const _arg = new UTSJSONObject(args[0])
      uni.navigateBack({
        // @ts-expect-error
        animationType:
          _arg['animationType'] != null
            ? (_arg['animationType'] as string)
            : 'pop-out',
        animationDuration:
          _arg['animationDuration'] != null
            ? (_arg['animationDuration'] as number)
            : 300,
        success,
        fail(error) {
          error.errMsg = error.errMsg.replace(`${method}: fail `, '')
          callback(null, error)
        },
      })
      break
    case 'switchTab':
      uni.switchTab({
        // @ts-expect-error
        url: new UTSJSONObject(args[0])['url'] as string,
        success,
        fail(error) {
          error.errMsg = error.errMsg.replace(`${method}: fail `, '')
          callback(null, error)
        },
      })
      break
    case 'getStorage':
      uni.getStorage({
        // @ts-expect-error
        key: new UTSJSONObject(args[0])['key'] as string,
        success,
        fail(error) {
          error.errMsg = error.errMsg.replace(`${method}: fail `, '')
          callback(null, error)
        },
      })
      break
    case 'setStorage':
      // @ts-expect-error
      const _arg = new UTSJSONObject(args[0])
      uni.setStorage({
        key: _arg['key'] as string,
        data: _arg['data']!,
        success,
        fail(error) {
          error.errMsg = error.errMsg.replace(`${method}: fail `, '')
          callback(null, error)
        },
      })
      break
    case 'getStorageSync':
      callback({ result: uni.getStorageSync(args[0] as string) }, null)
      break
    case 'setStorageSync':
      callback({ result: uni.setStorageSync(args[0] as string, args[1]) }, null)
      break
    case 'getStorageInfo':
      uni.getStorageInfo({
        success,
        fail(error) {
          error.errMsg = error.errMsg.replace(`${method}: fail `, '')
          callback(null, error)
        },
      })
      break
    case 'getStorageInfoSync':
      callback({ result: uni.getStorageInfoSync() }, null)
      break
    case 'removeStorage':
      uni.removeStorage({
        // @ts-expect-error
        key: new UTSJSONObject(args[0])['key'] as string,
        success,
        fail(error) {
          error.errMsg = error.errMsg.replace(`${method}: fail `, '')
          callback(null, error)
        },
      })
      break
    case 'removeStorageSync':
      callback({ result: uni.removeStorageSync(args[0] as string) }, null)
      break
    case 'clearStorage':
      // @ts-expect-error
      uni.clearStorage({
        success,
        fail(error) {
          error.errMsg = error.errMsg.replace(`${method}: fail `, '')
          callback(null, error)
        },
      })
      break
    case 'clearStorageSync':
      callback({ result: uni.clearStorageSync() }, null)
      break
    case 'showToast':
      // @ts-expect-error
      const _arg = new UTSJSONObject(args[0])
      uni.showToast({
        title: _arg['title'] as string,
        // @ts-expect-error
        icon: _arg['icon'] != null ? (_arg['icon'] as string) : 'success',
        // @ts-expect-error
        image:
          _arg['image'] != null && _arg['image'] != ''
            ? (_arg['image'] as string)
            : null,
        mask: _arg['mask'] != null ? (_arg['mask'] as boolean) : false,
        duration:
          _arg['duration'] != null ? (_arg['duration'] as number) : 1500,
        // @ts-expect-error
        position:
          _arg['position'] != null ? (_arg['position'] as string) : null,
        success,
        fail(error) {
          error.errMsg = error.errMsg.replace(`${method}: fail `, '')
          callback(null, error)
        },
      })
      break
    case 'hideToast':
      uni.hideToast()
      break
    case 'showLoading':
      // @ts-expect-error
      const _arg = new UTSJSONObject(args[0])
      uni.showLoading({
        title: _arg['title'] as string,
        mask: _arg['mask'] != null ? (_arg['mask'] as boolean) : false,
        success,
        fail(error) {
          error.errMsg = error.errMsg.replace(`${method}: fail `, '')
          callback(null, error)
        },
      })
      break
    case 'hideLoading':
      uni.hideLoading()
      break
    case 'showModal':
      // @ts-expect-error
      const _arg = new UTSJSONObject(args[0])
      uni.showModal({
        // @ts-expect-error
        title: _arg['title'] != null ? (_arg['title'] as string) : null,
        // @ts-expect-error
        content: _arg['content'] != null ? (_arg['content'] as string) : null,
        showCancel:
          _arg['showCancel'] != null ? (_arg['showCancel'] as boolean) : true,
        // @ts-expect-error
        cancelText:
          _arg['cancelText'] != null ? (_arg['cancelText'] as string) : null,
        // @ts-expect-error
        cancelColor:
          _arg['cancelColor'] != null ? (_arg['cancelColor'] as string) : null,
        // @ts-expect-error
        confirmText:
          _arg['confirmText'] != null ? (_arg['confirmText'] as string) : null,
        // @ts-expect-error
        confirmColor:
          _arg['confirmColor'] != null
            ? (_arg['confirmColor'] as string)
            : null,
        editable:
          _arg['editable'] != null ? (_arg['editable'] as boolean) : false,
        // @ts-expect-error
        placeholderText:
          _arg['placeholderText'] != null
            ? (_arg['placeholderText'] as string)
            : null,
        success,
        fail(error) {
          error.errMsg = error.errMsg.replace(`${method}: fail `, '')
          callback(null, error)
        },
      })
      break
    case 'showActionSheet':
      // @ts-expect-error
      const _arg = new UTSJSONObject(args[0])
      uni.showActionSheet({
        // @ts-expect-error
        title: _arg['title'] != null ? (_arg['title'] as string) : null,
        // @ts-expect-error
        itemList: JSON.parse<string[]>(JSON.stringify(_arg['itemList']))!,
        // @ts-expect-error
        itemColor:
          _arg['itemColor'] != null ? (_arg['itemColor'] as string) : null,
        success,
        fail(error) {
          error.errMsg = error.errMsg.replace(`${method}: fail `, '')
          callback(null, error)
        },
      })
      break
    case 'connectSocket':
      // @ts-expect-error
      const _arg = new UTSJSONObject(args[0])
      connectSocket(_arg['id'] as string, _arg['url'] as string, callback)
      break
    case 'onSocketOpen':
      firstSocketTaskEmitter(
        { method: 'onOpen' } as FirstSocketTaskEmitterParams,
        onApiCallback
      )
      break
    case 'onSocketMessage':
      firstSocketTaskEmitter(
        { method: 'onMessage' } as FirstSocketTaskEmitterParams,
        onApiCallback
      )
      break
    case 'onSocketError':
      firstSocketTaskEmitter(
        { method: 'onError' } as FirstSocketTaskEmitterParams,
        onApiCallback
      )
      break
    case 'onSocketClose':
      firstSocketTaskEmitter(
        { method: 'onClose' } as FirstSocketTaskEmitterParams,
        onApiCallback
      )
      break
    case 'sendSocketMessage':
      firstSocketTaskEmitter(
        {
          method: 'send',
          // @ts-expect-error
          data: new UTSJSONObject(args[0])['data'],
        } as FirstSocketTaskEmitterParams,
        callback
      )
      break
    case 'closeSocket':
      // @ts-expect-error
      const _arg = new UTSJSONObject(args[0])
      firstSocketTaskEmitter(
        {
          method: 'close',
          code: _arg['code'] as number,
          reason: _arg['reason'] as string,
        } as FirstSocketTaskEmitterParams,
        callback
      )
      break
    case 'getSystemInfo':
      uni.getSystemInfo({
        success,
        fail(error) {
          error.errMsg = error.errMsg.replace(`${method}: fail `, '')
          callback(null, error)
        },
      })
      break
    case 'getSystemInfoSync':
      callback({ result: uni.getSystemInfoSync() }, null)
      break
    case 'getDeviceInfo':
      callback({ result: uni.getDeviceInfo() }, null)
      break
    case 'getSystemSetting':
      callback({ result: uni.getSystemSetting() }, null)
      break
    case 'getAppBaseInfo':
      callback({ result: uni.getAppBaseInfo() }, null)
      break
    case 'getAppAuthorizeSetting':
      callback({ result: uni.getAppAuthorizeSetting() }, null)
      break
    case 'openAppAuthorizeSetting':
      uni.openAppAuthorizeSetting({
        success,
        fail(error) {
          error.errMsg = error.errMsg.replace(`${method}: fail `, '')
          callback(null, error)
        },
      })
      break
    case 'pageScrollTo':
      // @ts-expect-error
      const _arg = new UTSJSONObject(args[0])
      uni.pageScrollTo({
        scrollTop: _arg['scrollTop'] as number,
        duration: _arg['duration'] as number,
        success,
        fail(error) {
          error.errMsg = error.errMsg.replace(`${method}: fail `, '')
          callback(null, error)
        },
      })
      break
    default:
      callback(null, { errMsg: 'uni.' + method + ' not exists.' })
      break
  }
}

export type CaptureScreenshotParams = {
  id?: string | null
  fullPage?: boolean | null
  path?: string | null
  offsetX?: string | null
  offsetY?: string | null
}
export const captureScreenshot = (
  params: CaptureScreenshotParams,
  callback: Callback
): void => {
  const currentPage = _getCurrentPage()
  if (currentPage != null) {
    // @ts-expect-error
    currentPage.$viewToTempFilePath({
      id: params.id,
      offsetX: params.offsetX !== null ? params.offsetX : '0',
      offsetY: params.offsetY !== null ? params.offsetY : '0',
      wholeContent: params.fullPage == true,
      path: params.path,
      success: (res) => {
        const fileManager = uni.getFileSystemManager()
        // @ts-expect-error
        fileManager.readFile({
          encoding: 'base64',
          filePath: res.tempFilePath,
          success(readFileRes) {
            callback(
              {
                errMsg: 'screenshot:ok',
                tempFilePath: res.tempFilePath,
                data: readFileRes.data,
              },
              null
            )
          },
          fail(error) {
            callback(null, error)
          },
        } as ReadFileOptions)
      },
      fail: (error) => {
        callback(null, error)
      },
    })
  } else {
    callback(null, { errMsg: `currentPage is not found.` })
  }
}
