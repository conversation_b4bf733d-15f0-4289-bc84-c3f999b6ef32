<template>
  <view class="history-container">
    <!-- 搜索栏 -->
    <view class="search-bar">
      <input 
        v-model="searchKeyword" 
        placeholder="搜索历史记录..." 
        class="search-input"
        @input="onSearchInput"
      />
    </view>
    
    <!-- 筛选选项 -->
    <view class="filter-bar">
      <view 
        v-for="filter in filterOptions" 
        :key="filter.value"
        class="filter-item"
        :class="{ active: currentFilter === filter.value }"
        @click="changeFilter(filter.value)"
      >
        <text>{{ filter.label }}</text>
      </view>
    </view>
    
    <!-- 历史记录列表 -->
    <scroll-view class="history-list" scroll-y @scrolltolower="loadMore">
      <view v-if="filteredList.length === 0" class="empty-state">
        <text class="empty-icon">📝</text>
        <text class="empty-text">暂无历史记录</text>
        <text class="empty-desc">上传视频后会在这里显示处理记录</text>
      </view>
      
      <view v-else>
        <view 
          v-for="item in filteredList" 
          :key="item._id" 
          class="history-item"
          @click="viewDetail(item)"
        >
          <view class="item-header">
            <view class="status-badge" :class="item.status">
              <text>{{ getStatusText(item.status) }}</text>
            </view>
            <text class="create-time">{{ formatTime(item.createTime) }}</text>
          </view>
          
          <view class="item-content">
            <view class="video-info">
              <text class="file-name">{{ item.fileName || '未命名视频' }}</text>
              <text class="file-size">{{ formatFileSize(item.fileSize) }}</text>
            </view>
            
            <view v-if="item.status === 'completed'" class="process-info">
              <text class="process-time">处理耗时: {{ getProcessDuration(item) }}</text>
            </view>
            
            <view v-if="item.status === 'failed'" class="error-info">
              <text class="error-text">{{ item.errorMsg || '处理失败' }}</text>
            </view>
          </view>
          
          <view class="item-actions">
            <button 
              v-if="item.status === 'completed'" 
              @click.stop="viewResult(item)"
              class="action-btn primary"
              size="mini"
            >
              查看结果
            </button>
            <button 
              v-if="item.status === 'failed'" 
              @click.stop="retryProcess(item)"
              class="action-btn primary"
              size="mini"
            >
              重新处理
            </button>
            <button 
              @click.stop="deleteItem(item)"
              class="action-btn danger"
              size="mini"
            >
              删除
            </button>
          </view>
        </view>
      </view>
      
      <!-- 加载更多 -->
      <view v-if="hasMore" class="load-more">
        <text>加载更多...</text>
      </view>
    </scroll-view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'

// 响应式数据
const historyList = ref<any[]>([])
const searchKeyword = ref('')
const currentFilter = ref('all')
const hasMore = ref(true)
const loading = ref(false)

// 筛选选项
const filterOptions = [
  { label: '全部', value: 'all' },
  { label: '处理中', value: 'processing' },
  { label: '已完成', value: 'completed' },
  { label: '失败', value: 'failed' }
]

// 计算属性：过滤后的列表
const filteredList = computed(() => {
  let list = historyList.value
  
  // 按状态筛选
  if (currentFilter.value !== 'all') {
    list = list.filter(item => item.status === currentFilter.value)
  }
  
  // 按关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    list = list.filter(item => 
      (item.fileName || '').toLowerCase().includes(keyword) ||
      (item.errorMsg || '').toLowerCase().includes(keyword)
    )
  }
  
  return list
})

// 页面加载时获取历史记录
onMounted(() => {
  loadHistoryList()
})

// 加载历史记录列表
const loadHistoryList = async (isLoadMore = false) => {
  if (loading.value) return
  
  try {
    loading.value = true
    
    const res = await wx.cloud.callFunction({
      name: 'get-history-list',
      data: {
        skip: isLoadMore ? historyList.value.length : 0,
        limit: 20
      }
    })
    
    const newList = res.result || []
    
    if (isLoadMore) {
      historyList.value = [...historyList.value, ...newList]
    } else {
      historyList.value = newList
    }
    
    hasMore.value = newList.length === 20
    
  } catch (error) {
    console.error('加载历史记录失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'none'
    })
  } finally {
    loading.value = false
  }
}

// 搜索输入
const onSearchInput = () => {
  // 可以添加防抖逻辑
}

// 切换筛选条件
const changeFilter = (filter: string) => {
  currentFilter.value = filter
}

// 加载更多
const loadMore = () => {
  if (hasMore.value && !loading.value) {
    loadHistoryList(true)
  }
}

// 查看详情
const viewDetail = (item: any) => {
  if (item.status === 'processing') {
    uni.navigateTo({
      url: `/pages/process/process?fileId=${item.originalVideoFileId}`
    })
  } else if (item.status === 'completed') {
    uni.navigateTo({
      url: `/pages/result/result?taskId=${item._id}`
    })
  }
}

// 查看结果
const viewResult = (item: any) => {
  uni.navigateTo({
    url: `/pages/result/result?taskId=${item._id}`
  })
}

// 重新处理
const retryProcess = (item: any) => {
  uni.showModal({
    title: '确认重新处理',
    content: '是否重新处理该视频？',
    success: async (res) => {
      if (res.confirm) {
        try {
          await wx.cloud.callFunction({
            name: 'retry-task',
            data: { taskId: item._id }
          })
          
          uni.showToast({
            title: '已重新开始处理',
            icon: 'success'
          })
          
          // 刷新列表
          loadHistoryList()
          
        } catch (error) {
          console.error('重新处理失败:', error)
          uni.showToast({
            title: '操作失败',
            icon: 'none'
          })
        }
      }
    }
  })
}

// 删除记录
const deleteItem = (item: any) => {
  uni.showModal({
    title: '确认删除',
    content: '删除后无法恢复，是否确认删除？',
    success: async (res) => {
      if (res.confirm) {
        try {
          await wx.cloud.callFunction({
            name: 'delete-task',
            data: { taskId: item._id }
          })
          
          // 从列表中移除
          const index = historyList.value.findIndex(i => i._id === item._id)
          if (index > -1) {
            historyList.value.splice(index, 1)
          }
          
          uni.showToast({
            title: '删除成功',
            icon: 'success'
          })
          
        } catch (error) {
          console.error('删除失败:', error)
          uni.showToast({
            title: '删除失败',
            icon: 'none'
          })
        }
      }
    }
  })
}

// 获取状态文本
const getStatusText = (status: string): string => {
  switch (status) {
    case 'processing': return '处理中'
    case 'completed': return '已完成'
    case 'failed': return '失败'
    default: return '未知'
  }
}

// 格式化时间
const formatTime = (timestamp: string): string => {
  if (!timestamp) return ''
  const date = new Date(timestamp)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  
  if (diff < 60000) return '刚刚'
  if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
  if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`
  
  return `${date.getMonth() + 1}-${date.getDate()} ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`
}

// 格式化文件大小
const formatFileSize = (size: number): string => {
  if (!size) return ''
  if (size < 1024) return size + 'B'
  if (size < 1024 * 1024) return (size / 1024).toFixed(1) + 'KB'
  return (size / (1024 * 1024)).toFixed(1) + 'MB'
}

// 获取处理耗时
const getProcessDuration = (item: any): string => {
  if (!item.createTime || !item.finishTime) return ''
  const start = new Date(item.createTime).getTime()
  const end = new Date(item.finishTime).getTime()
  const duration = Math.floor((end - start) / 1000)
  
  if (duration < 60) return `${duration}秒`
  return `${Math.floor(duration / 60)}分${duration % 60}秒`
}
</script>

<style scoped>
.history-container {
  height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
}

.search-bar {
  padding: 20rpx 40rpx;
  background: white;
}

.search-input {
  width: 100%;
  height: 70rpx;
  background-color: #f5f5f5;
  border-radius: 35rpx;
  padding: 0 30rpx;
  font-size: 28rpx;
}

.filter-bar {
  display: flex;
  padding: 20rpx 40rpx;
  background: white;
  border-bottom: 1rpx solid #f0f0f0;
}

.filter-item {
  padding: 10rpx 20rpx;
  margin-right: 20rpx;
  border-radius: 20rpx;
  background-color: #f5f5f5;
  font-size: 26rpx;
  color: #666;
}

.filter-item.active {
  background-color: #007AFF;
  color: white;
}

.history-list {
  flex: 1;
  padding: 20rpx 40rpx;
}

.empty-state {
  text-align: center;
  padding: 100rpx 40rpx;
}

.empty-icon {
  display: block;
  font-size: 120rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  display: block;
  font-size: 32rpx;
  color: #333;
  margin-bottom: 20rpx;
}

.empty-desc {
  display: block;
  font-size: 26rpx;
  color: #999;
}

.history-item {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.status-badge {
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  color: white;
}

.status-badge.processing {
  background-color: #1890ff;
}

.status-badge.completed {
  background-color: #52c41a;
}

.status-badge.failed {
  background-color: #ff4d4f;
}

.create-time {
  font-size: 24rpx;
  color: #999;
}

.item-content {
  margin-bottom: 20rpx;
}

.video-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.file-name {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}

.file-size {
  font-size: 24rpx;
  color: #666;
}

.process-info, .error-info {
  font-size: 24rpx;
  color: #666;
}

.error-info {
  color: #ff4d4f;
}

.item-actions {
  display: flex;
  gap: 20rpx;
}

.action-btn {
  border-radius: 8rpx;
  font-size: 24rpx;
}

.action-btn.primary {
  background-color: #007AFF;
  color: white;
  border: none;
}

.action-btn.danger {
  background-color: white;
  color: #ff4d4f;
  border: 1rpx solid #ff4d4f;
}

.load-more {
  text-align: center;
  padding: 40rpx;
  color: #999;
  font-size: 26rpx;
}
</style>
