<view class="process-container data-v-dfd768e4"><view class="status-card data-v-dfd768e4"><view class="status-icon data-v-dfd768e4"><text wx:if="{{a}}" class="processing-icon data-v-dfd768e4">⏳</text><text wx:elif="{{b}}" class="success-icon data-v-dfd768e4">✅</text><text wx:elif="{{c}}" class="error-icon data-v-dfd768e4">❌</text></view><view class="status-text data-v-dfd768e4"><text class="status-title data-v-dfd768e4">{{d}}</text><text class="status-desc data-v-dfd768e4">{{e}}</text></view></view><view class="steps-container data-v-dfd768e4"><view class="{{['step-item', 'data-v-dfd768e4', f && 'active', g && 'completed']}}"><view class="step-number data-v-dfd768e4">1</view><view class="step-content data-v-dfd768e4"><text class="step-title data-v-dfd768e4">视频上传</text><text class="step-desc data-v-dfd768e4">视频文件上传到云端</text></view></view><view class="{{['step-item', 'data-v-dfd768e4', h && 'active', i && 'completed']}}"><view class="step-number data-v-dfd768e4">2</view><view class="step-content data-v-dfd768e4"><text class="step-title data-v-dfd768e4">语音识别</text><text class="step-desc data-v-dfd768e4">AI识别视频中的语音内容</text></view></view><view class="{{['step-item', 'data-v-dfd768e4', j && 'active', k && 'completed']}}"><view class="step-number data-v-dfd768e4">3</view><view class="step-content data-v-dfd768e4"><text class="step-title data-v-dfd768e4">字幕生成</text><text class="step-desc data-v-dfd768e4">生成SRT字幕文件</text></view></view><view class="{{['step-item', 'data-v-dfd768e4', l && 'active', m && 'completed']}}"><view class="step-number data-v-dfd768e4">4</view><view class="step-content data-v-dfd768e4"><text class="step-title data-v-dfd768e4">视频合成</text><text class="step-desc data-v-dfd768e4">将字幕烧录到视频中</text></view></view></view><view class="action-buttons data-v-dfd768e4"><button wx:if="{{n}}" bindtap="{{o}}" class="result-btn data-v-dfd768e4" type="primary"> 查看结果 </button><button wx:if="{{p}}" bindtap="{{q}}" class="retry-btn data-v-dfd768e4" type="primary"> 重新处理 </button><button bindtap="{{r}}" class="back-btn data-v-dfd768e4">返回首页</button></view><view wx:if="{{s}}" class="error-message data-v-dfd768e4"><text class="data-v-dfd768e4">{{t}}</text></view></view>