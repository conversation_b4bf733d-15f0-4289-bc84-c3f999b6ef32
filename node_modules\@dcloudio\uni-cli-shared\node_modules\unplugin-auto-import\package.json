{"name": "unplugin-auto-import", "type": "module", "version": "0.18.6", "description": "Register global imports on demand for Vite and Webpack", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "funding": "https://github.com/sponsors/antfu", "homepage": "https://github.com/unplugin/unplugin-auto-import#readme", "repository": {"type": "git", "url": "git+https://github.com/unplugin/unplugin-auto-import.git"}, "bugs": {"url": "https://github.com/unplugin/unplugin-auto-import/issues"}, "keywords": ["unplugin", "vite", "astro", "webpack", "rollup", "rspack", "auto-import", "transform"], "sideEffects": false, "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}, "./nuxt": {"import": {"types": "./dist/nuxt.d.ts", "default": "./dist/nuxt.js"}, "require": {"types": "./dist/nuxt.d.cts", "default": "./dist/nuxt.cjs"}}, "./astro": {"import": {"types": "./dist/astro.d.ts", "default": "./dist/astro.js"}, "require": {"types": "./dist/astro.d.cts", "default": "./dist/astro.cjs"}}, "./rollup": {"import": {"types": "./dist/rollup.d.ts", "default": "./dist/rollup.js"}, "require": {"types": "./dist/rollup.d.cts", "default": "./dist/rollup.cjs"}}, "./types": {"import": {"types": "./dist/types.d.ts", "default": "./dist/types.js"}, "require": {"types": "./dist/types.d.cts", "default": "./dist/types.cjs"}}, "./vite": {"import": {"types": "./dist/vite.d.ts", "default": "./dist/vite.js"}, "require": {"types": "./dist/vite.d.cts", "default": "./dist/vite.cjs"}}, "./webpack": {"import": {"types": "./dist/webpack.d.ts", "default": "./dist/webpack.js"}, "require": {"types": "./dist/webpack.d.cts", "default": "./dist/webpack.cjs"}}, "./rspack": {"import": {"types": "./dist/rspack.d.ts", "default": "./dist/rspack.js"}, "require": {"types": "./dist/rspack.d.cts", "default": "./dist/rspack.cjs"}}, "./esbuild": {"import": {"types": "./dist/esbuild.d.ts", "default": "./dist/esbuild.js"}, "require": {"types": "./dist/esbuild.d.cts", "default": "./dist/esbuild.cjs"}}, "./*": "./*"}, "main": "dist/index.cjs", "module": "dist/index.js", "types": "dist/index.d.ts", "typesVersions": {"*": {"*": ["./dist/*", "./*"]}}, "files": ["*.d.ts", "dist"], "engines": {"node": ">=14"}, "peerDependencies": {"@nuxt/kit": "^3.2.2", "@vueuse/core": "*"}, "peerDependenciesMeta": {"@nuxt/kit": {"optional": true}, "@vueuse/core": {"optional": true}}, "dependencies": {"@antfu/utils": "^0.7.10", "@rollup/pluginutils": "^5.1.3", "fast-glob": "^3.3.2", "local-pkg": "^0.5.1", "magic-string": "^0.30.14", "minimatch": "^9.0.5", "unimport": "^3.13.4", "unplugin": "^1.16.0"}, "devDependencies": {"@antfu/eslint-config": "^3.10.0", "@antfu/ni": "^0.23.1", "@nuxt/kit": "^3.14.1592", "@svgr/plugin-jsx": "^8.1.0", "@types/node": "^22.10.0", "@types/resolve": "^1.20.6", "@vueuse/metadata": "^12.0.0", "bumpp": "^9.8.1", "eslint": "^9.15.0", "esno": "^4.8.0", "publint": "^0.2.12", "rollup": "^4.27.4", "tsup": "^8.3.5", "typescript": "^5.7.2", "vite": "^6.0.1", "vitest": "^2.1.6", "webpack": "^5.96.1"}, "scripts": {"build": "tsup src/*.ts --format cjs,esm --dts --splitting --clean", "dev": "tsup src/*.ts --watch src", "lint": "eslint .", "lint:fix": "nr lint --fix", "typecheck": "tsc", "play": "npm -C playground run dev", "release": "bumpp && pnpm publish", "start": "esno src/index.ts", "test": "vitest", "test:run": "vitest run"}}