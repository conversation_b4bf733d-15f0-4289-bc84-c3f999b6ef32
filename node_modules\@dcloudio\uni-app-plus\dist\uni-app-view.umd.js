!function(e){"function"==typeof define&&define.amd?define(e):e()}((function(){"use strict";function e(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var t={exports:{}},n={exports:{}},r={exports:{}},i=r.exports={version:"2.6.12"};"number"==typeof __e&&(__e=i);var a=r.exports,o={exports:{}},s=o.exports="undefined"!=typeof window&&window.Math==Math?window:"undefined"!=typeof self&&self.Math==Math?self:Function("return this")();"number"==typeof __g&&(__g=s);var l=o.exports,u=a,c=l,d="__core-js_shared__",h=c[d]||(c[d]={});(n.exports=function(e,t){return h[e]||(h[e]=void 0!==t?t:{})})("versions",[]).push({version:u.version,mode:"global",copyright:"© 2020 <PERSON> (zloirock.ru)"});var f=n.exports,p=0,v=Math.random(),g=function(e){return"Symbol(".concat(void 0===e?"":e,")_",(++p+v).toString(36))},m=f("wks"),_=g,y=l.Symbol,b="function"==typeof y;(t.exports=function(e){return m[e]||(m[e]=b&&y[e]||(b?y:_)("Symbol."+e))}).store=m;var w,x,S=t.exports,k={},C=function(e){return"object"==typeof e?null!==e:"function"==typeof e},T=C,A=function(e){if(!T(e))throw TypeError(e+" is not an object!");return e},M=function(e){try{return!!e()}catch(t){return!0}},E=!M((function(){return 7!=Object.defineProperty({},"a",{get:function(){return 7}}).a}));function O(){if(x)return w;x=1;var e=C,t=l.document,n=e(t)&&e(t.createElement);return w=function(e){return n?t.createElement(e):{}}}var L=!E&&!M((function(){return 7!=Object.defineProperty(O()("div"),"a",{get:function(){return 7}}).a})),z=C,N=A,I=L,P=function(e,t){if(!z(e))return e;var n,r;if(t&&"function"==typeof(n=e.toString)&&!z(r=n.call(e)))return r;if("function"==typeof(n=e.valueOf)&&!z(r=n.call(e)))return r;if(!t&&"function"==typeof(n=e.toString)&&!z(r=n.call(e)))return r;throw TypeError("Can't convert object to primitive value")},D=Object.defineProperty;k.f=E?Object.defineProperty:function(e,t,n){if(N(e),t=P(t,!0),N(n),I)try{return D(e,t,n)}catch(r){}if("get"in n||"set"in n)throw TypeError("Accessors not supported!");return"value"in n&&(e[t]=n.value),e};var B=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}},R=k,F=B,q=E?function(e,t,n){return R.f(e,t,F(1,n))}:function(e,t,n){return e[t]=n,e},j=S("unscopables"),V=Array.prototype;null==V[j]&&q(V,j,{});var $={},H={}.toString,W=function(e){return H.call(e).slice(8,-1)},U=function(e){if(null==e)throw TypeError("Can't call method on  "+e);return e},Y=Object("z").propertyIsEnumerable(0)?Object:function(e){return"String"==W(e)?e.split(""):Object(e)},X=U,Z=function(e){return Y(X(e))},G={exports:{}},K={}.hasOwnProperty,J=function(e,t){return K.call(e,t)},Q=f("native-function-to-string",Function.toString),ee=l,te=q,ne=J,re=g("src"),ie=Q,ae="toString",oe=(""+ie).split(ae);a.inspectSource=function(e){return ie.call(e)},(G.exports=function(e,t,n,r){var i="function"==typeof n;i&&(ne(n,"name")||te(n,"name",t)),e[t]!==n&&(i&&(ne(n,re)||te(n,re,e[t]?""+e[t]:oe.join(String(t)))),e===ee?e[t]=n:r?e[t]?e[t]=n:te(e,t,n):(delete e[t],te(e,t,n)))})(Function.prototype,ae,(function(){return"function"==typeof this&&this[re]||ie.call(this)}));var se=G.exports,le=function(e){if("function"!=typeof e)throw TypeError(e+" is not a function!");return e},ue=le,ce=l,de=a,he=q,fe=se,pe=function(e,t,n){if(ue(e),void 0===t)return e;switch(n){case 1:return function(n){return e.call(t,n)};case 2:return function(n,r){return e.call(t,n,r)};case 3:return function(n,r,i){return e.call(t,n,r,i)}}return function(){return e.apply(t,arguments)}},ve="prototype",ge=function(e,t,n){var r,i,a,o,s=e&ge.F,l=e&ge.G,u=e&ge.S,c=e&ge.P,d=e&ge.B,h=l?ce:u?ce[t]||(ce[t]={}):(ce[t]||{})[ve],f=l?de:de[t]||(de[t]={}),p=f[ve]||(f[ve]={});for(r in l&&(n=t),n)a=((i=!s&&h&&void 0!==h[r])?h:n)[r],o=d&&i?pe(a,ce):c&&"function"==typeof a?pe(Function.call,a):a,h&&fe(h,r,a,e&ge.U),f[r]!=a&&he(f,r,o),c&&p[r]!=a&&(p[r]=a)};ce.core=de,ge.F=1,ge.G=2,ge.S=4,ge.P=8,ge.B=16,ge.W=32,ge.U=64,ge.R=128;var me,_e,ye,be=ge,we=Math.ceil,xe=Math.floor,Se=function(e){return isNaN(e=+e)?0:(e>0?xe:we)(e)},ke=Se,Ce=Math.min,Te=Se,Ae=Math.max,Me=Math.min,Ee=Z,Oe=function(e){return e>0?Ce(ke(e),9007199254740991):0},Le=function(e,t){return(e=Te(e))<0?Ae(e+t,0):Me(e,t)},ze=f("keys"),Ne=g,Ie=function(e){return ze[e]||(ze[e]=Ne(e))},Pe=J,De=Z,Be=(me=!1,function(e,t,n){var r,i=Ee(e),a=Oe(i.length),o=Le(n,a);if(me&&t!=t){for(;a>o;)if((r=i[o++])!=r)return!0}else for(;a>o;o++)if((me||o in i)&&i[o]===t)return me||o||0;return!me&&-1}),Re=Ie("IE_PROTO"),Fe="constructor,hasOwnProperty,isPrototypeOf,propertyIsEnumerable,toLocaleString,toString,valueOf".split(","),qe=function(e,t){var n,r=De(e),i=0,a=[];for(n in r)n!=Re&&Pe(r,n)&&a.push(n);for(;t.length>i;)Pe(r,n=t[i++])&&(~Be(a,n)||a.push(n));return a},je=Fe,Ve=Object.keys||function(e){return qe(e,je)},$e=k,He=A,We=Ve,Ue=E?Object.defineProperties:function(e,t){He(e);for(var n,r=We(t),i=r.length,a=0;i>a;)$e.f(e,n=r[a++],t[n]);return e};var Ye=A,Xe=Ue,Ze=Fe,Ge=Ie("IE_PROTO"),Ke=function(){},Je="prototype",Qe=function(){var e,t=O()("iframe"),n=Ze.length;for(t.style.display="none",function(){if(ye)return _e;ye=1;var e=l.document;return _e=e&&e.documentElement}().appendChild(t),t.src="javascript:",(e=t.contentWindow.document).open(),e.write("<script>document.F=Object<\/script>"),e.close(),Qe=e.F;n--;)delete Qe[Je][Ze[n]];return Qe()},et=Object.create||function(e,t){var n;return null!==e?(Ke[Je]=Ye(e),n=new Ke,Ke[Je]=null,n[Ge]=e):n=Qe(),void 0===t?n:Xe(n,t)},tt=k.f,nt=J,rt=S("toStringTag"),it=function(e,t,n){e&&!nt(e=n?e:e.prototype,rt)&&tt(e,rt,{configurable:!0,value:t})},at=et,ot=B,st=it,lt={};q(lt,S("iterator"),(function(){return this}));var ut=U,ct=function(e){return Object(ut(e))},dt=J,ht=ct,ft=Ie("IE_PROTO"),pt=Object.prototype,vt=Object.getPrototypeOf||function(e){return e=ht(e),dt(e,ft)?e[ft]:"function"==typeof e.constructor&&e instanceof e.constructor?e.constructor.prototype:e instanceof Object?pt:null},gt=be,mt=se,_t=q,yt=$,bt=function(e,t,n){e.prototype=at(lt,{next:ot(1,n)}),st(e,t+" Iterator")},wt=it,xt=vt,St=S("iterator"),kt=!([].keys&&"next"in[].keys()),Ct="keys",Tt="values",At=function(){return this},Mt=function(e){V[j][e]=!0},Et=function(e,t){return{value:t,done:!!e}},Ot=$,Lt=Z,zt=function(e,t,n,r,i,a,o){bt(n,t,r);var s,l,u,c=function(e){if(!kt&&e in p)return p[e];switch(e){case Ct:case Tt:return function(){return new n(this,e)}}return function(){return new n(this,e)}},d=t+" Iterator",h=i==Tt,f=!1,p=e.prototype,v=p[St]||p["@@iterator"]||i&&p[i],g=v||c(i),m=i?h?c("entries"):g:void 0,_="Array"==t&&p.entries||v;if(_&&(u=xt(_.call(new e)))!==Object.prototype&&u.next&&(wt(u,d,!0),"function"!=typeof u[St]&&_t(u,St,At)),h&&v&&v.name!==Tt&&(f=!0,g=function(){return v.call(this)}),(kt||f||!p[St])&&_t(p,St,g),yt[t]=g,yt[d]=At,i)if(s={values:h?g:c(Tt),keys:a?g:c(Ct),entries:m},o)for(l in s)l in p||mt(p,l,s[l]);else gt(gt.P+gt.F*(kt||f),t,s);return s}(Array,"Array",(function(e,t){this._t=Lt(e),this._i=0,this._k=t}),(function(){var e=this._t,t=this._k,n=this._i++;return!e||n>=e.length?(this._t=void 0,Et(1)):Et(0,"keys"==t?n:"values"==t?e[n]:[n,e[n]])}),"values");Ot.Arguments=Ot.Array,Mt("keys"),Mt("values"),Mt("entries");for(var Nt=zt,It=Ve,Pt=se,Dt=l,Bt=q,Rt=$,Ft=S,qt=Ft("iterator"),jt=Ft("toStringTag"),Vt=Rt.Array,$t={CSSRuleList:!0,CSSStyleDeclaration:!1,CSSValueList:!1,ClientRectList:!1,DOMRectList:!1,DOMStringList:!1,DOMTokenList:!0,DataTransferItemList:!1,FileList:!1,HTMLAllCollection:!1,HTMLCollection:!1,HTMLFormElement:!1,HTMLSelectElement:!1,MediaList:!0,MimeTypeArray:!1,NamedNodeMap:!1,NodeList:!0,PaintRequestList:!1,Plugin:!1,PluginArray:!1,SVGLengthList:!1,SVGNumberList:!1,SVGPathSegList:!1,SVGPointList:!1,SVGStringList:!1,SVGTransformList:!1,SourceBufferList:!1,StyleSheetList:!0,TextTrackCueList:!1,TextTrackList:!1,TouchList:!1},Ht=It($t),Wt=0;Wt<Ht.length;Wt++){var Ut,Yt=Ht[Wt],Xt=$t[Yt],Zt=Dt[Yt],Gt=Zt&&Zt.prototype;if(Gt&&(Gt[qt]||Bt(Gt,qt,Vt),Gt[jt]||Bt(Gt,jt,Yt),Rt[Yt]=Vt,Xt))for(Ut in Nt)Gt[Ut]||Pt(Gt,Ut,Nt[Ut],!0)}
/**
  * @vue/shared v3.4.21
  * (c) 2018-present Yuxi (Evan) You and Vue contributors
  * @license MIT
  **/function Kt(e,t){var n=new Set(e.split(","));return e=>n.has(e)}var Jt,Qt={},en=[],tn=()=>{},nn=()=>!1,rn=e=>111===e.charCodeAt(0)&&110===e.charCodeAt(1)&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),an=e=>e.startsWith("onUpdate:"),on=Object.assign,sn=(e,t)=>{var n=e.indexOf(t);n>-1&&e.splice(n,1)},ln=Object.prototype.hasOwnProperty,un=(e,t)=>ln.call(e,t),cn=Array.isArray,dn=e=>"[object Map]"===yn(e),hn=e=>"[object Set]"===yn(e),fn=e=>"function"==typeof e,pn=e=>"string"==typeof e,vn=e=>"symbol"==typeof e,gn=e=>null!==e&&"object"==typeof e,mn=e=>(gn(e)||fn(e))&&fn(e.then)&&fn(e.catch),_n=Object.prototype.toString,yn=e=>_n.call(e),bn=e=>yn(e).slice(8,-1),wn=e=>"[object Object]"===yn(e),xn=e=>pn(e)&&"NaN"!==e&&"-"!==e[0]&&""+parseInt(e,10)===e,Sn=Kt(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),kn=e=>{var t=Object.create(null);return n=>t[n]||(t[n]=e(n))},Cn=/-(\w)/g,Tn=kn((e=>e.replace(Cn,((e,t)=>t?t.toUpperCase():"")))),An=/\B([A-Z])/g,Mn=kn((e=>e.replace(An,"-$1").toLowerCase())),En=kn((e=>e.charAt(0).toUpperCase()+e.slice(1))),On=kn((e=>e?"on".concat(En(e)):"")),Ln=(e,t)=>!Object.is(e,t),zn=(e,t)=>{for(var n=0;n<e.length;n++)e[n](t)},Nn=(e,t,n)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,value:n})},In=e=>{var t=parseFloat(e);return isNaN(t)?e:t},Pn=()=>Jt||(Jt="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window||"undefined"!=typeof window?window:{});function Dn(e){if(cn(e)){for(var t={},n=0;n<e.length;n++){var r=e[n],i=pn(r)?qn(r):Dn(r);if(i)for(var a in i)t[a]=i[a]}return t}if(pn(e)||gn(e))return e}var Bn=/;(?![^(]*\))/g,Rn=/:([^]+)/,Fn=/\/\*[^]*?\*\//g;function qn(e){var t={};return e.replace(Fn,"").split(Bn).forEach((e=>{if(e){var n=e.split(Rn);n.length>1&&(t[n[0].trim()]=n[1].trim())}})),t}function jn(e){var t="";if(pn(e))t=e;else if(cn(e))for(var n=0;n<e.length;n++){var r=jn(e[n]);r&&(t+=r+" ")}else if(gn(e))for(var i in e)e[i]&&(t+=i+" ");return t.trim()}var Vn,$n,Hn=Kt("itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly");function Wn(e){return!!e||""===e}var Un=be,Yn=le,Xn=ct,Zn=M,Gn=[].sort,Kn=[1,2,3];Un(Un.P+Un.F*(Zn((function(){Kn.sort(void 0)}))||!Zn((function(){Kn.sort(null)}))||!function(){if($n)return Vn;$n=1;var e=M;return Vn=function(t,n){return!!t&&e((function(){n?t.call(null,(function(){}),1):t.call(null)}))}}()(Gn)),"Array",{sort:function(e){return void 0===e?Gn.call(Xn(this)):Gn.call(Xn(this),Yn(e))}});var Jn="\n",Qn=44,er="#007aff",tr=/^([a-z-]+:)?\/\//i,nr=/^data:.*,.*/,rr="wxs://",ir="json://",ar="wxsModules",or="renderjsModules",sr="onThemeChange",lr="onPageScroll",ur="onReachBottom";function cr(e){return function(e){return 0===e.indexOf("/")}(e)?e:"/"+e}function dr(e){var t,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return function(){if(e){for(var r=arguments.length,i=new Array(r),a=0;a<r;a++)i[a]=arguments[a];t=e.apply(n,i),e=null}return t}}function hr(e,t){if(pn(t)){var n=(t=t.replace(/\[(\d+)\]/g,".$1")).split("."),r=n[0];return e||(e={}),1===n.length?e[r]:hr(e[r],n.slice(1).join("."))}}var fr=0;function pr(e){var t=Date.now(),n=fr?t-fr:0;fr=t;for(var r=arguments.length,i=new Array(r>1?r-1:0),a=1;a<r;a++)i[a-1]=arguments[a];return"[".concat(t,"][").concat(n,"ms][").concat(e,"]：").concat(i.map((e=>JSON.stringify(e))).join(" "))}function vr(e){return Tn(e.substring(5))}var gr=dr((()=>{var e=HTMLElement.prototype,t=e.setAttribute;e.setAttribute=function(e,n){e.startsWith("data-")&&this.tagName.startsWith("UNI-")&&((this.__uniDataset||(this.__uniDataset={}))[vr(e)]=n);t.call(this,e,n)};var n=e.removeAttribute;e.removeAttribute=function(e){this.__uniDataset&&e.startsWith("data-")&&this.tagName.startsWith("UNI-")&&delete this.__uniDataset[vr(e)],n.call(this,e)}}));function mr(e){return on({},e.dataset,e.__uniDataset)}var _r=new RegExp("\"[^\"]+\"|'[^']+'|url\\([^)]+\\)|(\\d*\\.?\\d+)[r|u]px","g");function yr(e){return{passive:e}}function br(e){var{id:t,offsetTop:n,offsetLeft:r}=e;return{id:t,dataset:mr(e),offsetTop:n,offsetLeft:r}}function wr(e){if(fn(e))return window.plus?e():void document.addEventListener("plusready",e)}var xr=/(?:Once|Passive|Capture)$/;function Sr(e){var t,n;if(xr.test(e))for(t={};n=e.match(xr);)e=e.slice(0,e.length-n[0].length),t[n[0].toLowerCase()]=!0;return[Mn(e.slice(2)),t]}var kr=(()=>({stop:1,prevent:2,self:4}))(),Cr="class",Tr="style",Ar=".vShow",Mr=".vOwnerId",Er=".vRenderjs",Or="change:",Lr=1,zr=2,Nr=3,Ir=4,Pr=5,Dr=6,Br=7,Rr=8,Fr=9,qr=10,jr=12,Vr=15,$r=20;function Hr(e,t,n){var r,{clearTimeout:i,setTimeout:a}=n,o=function(){i(r);r=a((()=>e.apply(this,arguments)),t)};return o.cancel=function(){i(r)},o}var Wr=function(){};Wr.prototype={_id:1,on:function(e,t,n){var r=this.e||(this.e={});return(r[e]||(r[e]=[])).push({fn:t,ctx:n,_id:this._id}),this._id++},once:function(e,t,n){var r=this;function i(){r.off(e,i),t.apply(n,arguments)}return i._=t,this.on(e,i,n)},emit:function(e){for(var t=[].slice.call(arguments,1),n=((this.e||(this.e={}))[e]||[]).slice(),r=0,i=n.length;r<i;r++)n[r].fn.apply(n[r].ctx,t);return this},off:function(e,t){var n=this.e||(this.e={}),r=n[e],i=[];if(r&&t){for(var a=r.length-1;a>=0;a--)if(r[a].fn===t||r[a].fn._===t||r[a]._id===t){r.splice(a,1);break}i=r}return i.length?n[e]=i:delete n[e],this}};var Ur=Wr,Yr=["{","}"];var Xr=/^(?:\d)+/,Zr=/^(?:\w)+/;var Gr="zh-Hans",Kr="zh-Hant",Jr="en",Qr="fr",ei="es",ti=Object.prototype.hasOwnProperty,ni=(e,t)=>ti.call(e,t),ri=new class{constructor(){this._caches=Object.create(null)}interpolate(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:Yr;if(!t)return[e];var r=this._caches[e];return r||(r=function(e,t){var[n,r]=t,i=[],a=0,o="";for(;a<e.length;){var s=e[a++];if(s===n){o&&i.push({type:"text",value:o}),o="";var l="";for(s=e[a++];void 0!==s&&s!==r;)l+=s,s=e[a++];var u=s===r,c=Xr.test(l)?"list":u&&Zr.test(l)?"named":"unknown";i.push({value:l,type:c})}else o+=s}return o&&i.push({type:"text",value:o}),i}(e,n),this._caches[e]=r),function(e,t){var n=[],r=0,i=Array.isArray(t)?"list":(a=t,null!==a&&"object"==typeof a?"named":"unknown");var a;if("unknown"===i)return n;for(;r<e.length;){var o=e[r];switch(o.type){case"text":n.push(o.value);break;case"list":n.push(t[parseInt(o.value,10)]);break;case"named":"named"===i&&n.push(t[o.value])}r++}return n}(r,t)}};function ii(e,t){if(e){if(e=e.trim().replace(/_/g,"-"),t&&t[e])return e;if("chinese"===(e=e.toLowerCase()))return Gr;if(0===e.indexOf("zh"))return e.indexOf("-hans")>-1?Gr:e.indexOf("-hant")>-1?Kr:(n=e,["-tw","-hk","-mo","-cht"].find((e=>-1!==n.indexOf(e)))?Kr:Gr);var n,r=[Jr,Qr,ei];t&&Object.keys(t).length>0&&(r=Object.keys(t));var i=function(e,t){return t.find((t=>0===e.indexOf(t)))}(e,r);return i||void 0}}class ai{constructor(e){var{locale:t,fallbackLocale:n,messages:r,watcher:i,formater:a}=e;this.locale=Jr,this.fallbackLocale=Jr,this.message={},this.messages={},this.watchers=[],n&&(this.fallbackLocale=n),this.formater=a||ri,this.messages=r||{},this.setLocale(t||Jr),i&&this.watchLocale(i)}setLocale(e){var t=this.locale;this.locale=ii(e,this.messages)||this.fallbackLocale,this.messages[this.locale]||(this.messages[this.locale]={}),this.message=this.messages[this.locale],t!==this.locale&&this.watchers.forEach((e=>{e(this.locale,t)}))}getLocale(){return this.locale}watchLocale(e){var t=this.watchers.push(e)-1;return()=>{this.watchers.splice(t,1)}}add(e,t){var n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],r=this.messages[e];r?n?Object.assign(r,t):Object.keys(t).forEach((e=>{ni(r,e)||(r[e]=t[e])})):this.messages[e]=t}f(e,t,n){return this.formater.interpolate(e,t,n).join("")}t(e,t,n){var r=this.message;return"string"==typeof t?(t=ii(t,this.messages))&&(r=this.messages[t]):n=t,ni(r,e)?this.formater.interpolate(r[e],n).join(""):(console.warn("Cannot translate the value of keypath ".concat(e,". Use the value of keypath as default.")),e)}}function oi(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2?arguments[2]:void 0,r=arguments.length>3?arguments[3]:void 0;if("string"!=typeof e){var i=[t,e];e=i[0],t=i[1]}"string"!=typeof e&&(e="undefined"!=typeof uni&&uni.getLocale?uni.getLocale():"undefined"!=typeof window&&window.getLocale?window.getLocale():Jr),"string"!=typeof n&&(n="undefined"!=typeof __uniConfig&&__uniConfig.fallbackLocale||Jr);var a=new ai({locale:e,fallbackLocale:n,messages:t,watcher:r}),o=(e,t)=>{if("function"!=typeof getApp)o=function(e,t){return a.t(e,t)};else{var n=!1;o=function(e,t){var r=getApp().$vm;return r&&(r.$locale,n||(n=!0,function(e,t){e.$watchLocale?e.$watchLocale((e=>{t.setLocale(e)})):e.$watch((()=>e.$locale),(e=>{t.setLocale(e)}))}(r,a))),a.t(e,t)}}return o(e,t)};return{i18n:a,f:(e,t,n)=>a.f(e,t,n),t:(e,t)=>o(e,t),add(e,t){var n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];return a.add(e,t,n)},watch:e=>a.watchLocale(e),getLocale:()=>a.getLocale(),setLocale:e=>a.setLocale(e)}}var si,li=dr((()=>"undefined"!=typeof __uniConfig&&__uniConfig.locales&&!!Object.keys(__uniConfig.locales).length));function ui(){var e;if(!si&&(e="function"==typeof getApp?weex.requireModule("plus").getLanguage():plus.webview.currentWebview().getStyle().locale,si=oi(e),li())){var t=Object.keys(__uniConfig.locales||{});t.length&&t.forEach((e=>si.add(e,__uniConfig.locales[e]))),si.setLocale(e)}return si}function ci(e,t,n){return t.reduce(((t,r,i)=>(t[e+r]=n[i],t)),{})}var di=dr((()=>{var e="uni.picker.",t=["done","cancel"];ui().add(Jr,ci(e,t,["Done","Cancel"]),!1),ui().add(ei,ci(e,t,["OK","Cancelar"]),!1),ui().add(Qr,ci(e,t,["OK","Annuler"]),!1),ui().add(Gr,ci(e,t,["完成","取消"]),!1),ui().add(Kr,ci(e,t,["完成","取消"]),!1)})),hi=dr((()=>{var e="uni.button.",t=["feedback.title","feedback.send"];ui().add(Jr,ci(e,t,["feedback","send"]),!1),ui().add(ei,ci(e,t,["realimentación","enviar"]),!1),ui().add(Qr,ci(e,t,["retour d'information","envoyer"]),!1),ui().add(Gr,ci(e,t,["问题反馈","发送"]),!1),ui().add(Kr,ci(e,t,["問題反饋","發送"]),!1)})),fi=dr((()=>{var e="uni.chooseLocation.",t=["search","cancel"];ui().add(Jr,ci(e,t,["Find Place","Cancel"]),!1),ui().add(ei,ci(e,t,["Encontrar","Cancelar"]),!1),ui().add(Qr,ci(e,t,["Trouve","Annuler"]),!1),ui().add(Gr,ci(e,t,["搜索地点","取消"]),!1),ui().add(Kr,ci(e,t,["搜索地點","取消"]),!1)}));function pi(e){var t=new Ur;return{on:(e,n)=>t.on(e,n),once:(e,n)=>t.once(e,n),off:(e,n)=>t.off(e,n),emit(e){for(var n=arguments.length,r=new Array(n>1?n-1:0),i=1;i<n;i++)r[i-1]=arguments[i];return t.emit(e,...r)},subscribe(n,r){t[arguments.length>2&&void 0!==arguments[2]&&arguments[2]?"once":"on"]("".concat(e,".").concat(n),r)},unsubscribe(n,r){t.off("".concat(e,".").concat(n),r)},subscribeHandler(n,r,i){t.emit("".concat(e,".").concat(n),r,i)}}}var vi="invokeViewApi",gi="invokeServiceApi",mi=1,_i=Object.create(null);function yi(e,t){return e+"."+t}function bi(e,t,n){t=yi(e,t),_i[t]||(_i[t]=n)}function wi(e,t){var{id:n,name:r,args:i}=e;r=yi(t,r);var a=e=>{n&&UniViewJSBridge.publishHandler(vi+"."+n,e)},o=_i[r];o?o(i,a):a({})}var xi,Si=on(pi("service"),{invokeServiceMethod:(e,t,n)=>{var{subscribe:r,publishHandler:i}=UniViewJSBridge,a=n?mi++:0;n&&r(gi+"."+a,n,!0),i(gi,{id:a,name:e,args:t})}}),ki=350,Ci=10,Ti=yr(!0);function Ai(){xi&&(clearTimeout(xi),xi=null)}var Mi,Ei,Oi=0,Li=0;function zi(e){if(Ai(),1===e.touches.length){var{pageX:t,pageY:n}=e.touches[0];Oi=t,Li=n,xi=setTimeout((function(){var t=new CustomEvent("longpress",{bubbles:!0,cancelable:!0,target:e.target,currentTarget:e.currentTarget});t.touches=e.touches,t.changedTouches=e.changedTouches,e.target.dispatchEvent(t)}),ki)}}function Ni(e){if(xi){if(1!==e.touches.length)return Ai();var{pageX:t,pageY:n}=e.touches[0];return Math.abs(t-Oi)>Ci||Math.abs(n-Li)>Ci?Ai():void 0}}function Ii(e,t){var n=Number(e);return isNaN(n)?t:n}function Pi(){var e=__uniConfig.globalStyle||{},t=Ii(e.rpxCalcMaxDeviceWidth,960),n=Ii(e.rpxCalcBaseDeviceWidth,375);function r(){var e,r,i,a=(e=/^Apple/.test(navigator.vendor)&&"number"==typeof window.orientation,r=e&&90===Math.abs(window.orientation),i=e?Math[r?"max":"min"](screen.width,screen.height):screen.width,Math.min(window.innerWidth,document.documentElement.clientWidth,i)||i);a=a<=t?a:n,document.documentElement.style.fontSize=a/23.4375+"px"}r(),document.addEventListener("DOMContentLoaded",r),window.addEventListener("load",r),window.addEventListener("resize",r)}function Di(){Pi(),gr(),window.addEventListener("touchstart",zi,Ti),window.addEventListener("touchmove",Ni,Ti),window.addEventListener("touchend",Ai,Ti),window.addEventListener("touchcancel",Ai,Ti)}class Bi{constructor(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.detached=e,this._active=!0,this.effects=[],this.cleanups=[],this.parent=Mi,!e&&Mi&&(this.index=(Mi.scopes||(Mi.scopes=[])).push(this)-1)}get active(){return this._active}run(e){if(this._active){var t=Mi;try{return Mi=this,e()}finally{Mi=t}}}on(){Mi=this}off(){Mi=this.parent}stop(e){if(this._active){var t,n;for(t=0,n=this.effects.length;t<n;t++)this.effects[t].stop();for(t=0,n=this.cleanups.length;t<n;t++)this.cleanups[t]();if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].stop(!0);if(!this.detached&&this.parent&&!e){var r=this.parent.scopes.pop();r&&r!==this&&(this.parent.scopes[this.index]=r,r.index=this.index)}this.parent=void 0,this._active=!1}}}class Ri{constructor(e,t,n,r){this.fn=e,this.trigger=t,this.scheduler=n,this.active=!0,this.deps=[],this._dirtyLevel=4,this._trackId=0,this._runnings=0,this._shouldSchedule=!1,this._depsLength=0,function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Mi;t&&t.active&&t.effects.push(e)}(this,r)}get dirty(){if(2===this._dirtyLevel||3===this._dirtyLevel){this._dirtyLevel=1,Wi();for(var e=0;e<this._depsLength;e++){var t=this.deps[e];if(t.computed&&(t.computed.value,this._dirtyLevel>=4))break}1===this._dirtyLevel&&(this._dirtyLevel=0),Ui()}return this._dirtyLevel>=4}set dirty(e){this._dirtyLevel=e?4:0}run(){if(this._dirtyLevel=0,!this.active)return this.fn();var e=Vi,t=Ei;try{return Vi=!0,Ei=this,this._runnings++,Fi(this),this.fn()}finally{qi(this),this._runnings--,Ei=t,Vi=e}}stop(){var e;this.active&&(Fi(this),qi(this),null==(e=this.onStop)||e.call(this),this.active=!1)}}function Fi(e){e._trackId++,e._depsLength=0}function qi(e){if(e.deps.length>e._depsLength){for(var t=e._depsLength;t<e.deps.length;t++)ji(e.deps[t],e);e.deps.length=e._depsLength}}function ji(e,t){var n=e.get(t);void 0!==n&&t._trackId!==n&&(e.delete(t),0===e.size&&e.cleanup())}var Vi=!0,$i=0,Hi=[];function Wi(){Hi.push(Vi),Vi=!1}function Ui(){var e=Hi.pop();Vi=void 0===e||e}function Yi(){$i++}function Xi(){for($i--;!$i&&Gi.length;)Gi.shift()()}function Zi(e,t,n){if(t.get(e)!==e._trackId){t.set(e,e._trackId);var r=e.deps[e._depsLength];r!==t?(r&&ji(r,e),e.deps[e._depsLength++]=t):e._depsLength++}}var Gi=[];function Ki(e,t,n){for(var r of(Yi(),e.keys())){var i=void 0;r._dirtyLevel<t&&(null!=i?i:i=e.get(r)===r._trackId)&&(r._shouldSchedule||(r._shouldSchedule=0===r._dirtyLevel),r._dirtyLevel=t),r._shouldSchedule&&(null!=i?i:i=e.get(r)===r._trackId)&&(r.trigger(),r._runnings&&!r.allowRecurse||2===r._dirtyLevel||(r._shouldSchedule=!1,r.scheduler&&Gi.push(r.scheduler)))}Xi()}var Ji=(e,t)=>{var n=new Map;return n.cleanup=e,n.computed=t,n},Qi=new WeakMap,ea=Symbol(""),ta=Symbol("");function na(e,t,n){if(Vi&&Ei){var r=Qi.get(e);r||Qi.set(e,r=new Map);var i=r.get(n);i||r.set(n,i=Ji((()=>r.delete(n)))),Zi(Ei,i)}}function ra(e,t,n,r,i,a){var o=Qi.get(e);if(o){var s=[];if("clear"===t)s=[...o.values()];else if("length"===n&&cn(e)){var l=Number(r);o.forEach(((e,t)=>{("length"===t||!vn(t)&&t>=l)&&s.push(e)}))}else switch(void 0!==n&&s.push(o.get(n)),t){case"add":cn(e)?xn(n)&&s.push(o.get("length")):(s.push(o.get(ea)),dn(e)&&s.push(o.get(ta)));break;case"delete":cn(e)||(s.push(o.get(ea)),dn(e)&&s.push(o.get(ta)));break;case"set":dn(e)&&s.push(o.get(ea))}for(var u of(Yi(),s))u&&Ki(u,4);Xi()}}var ia=Kt("__proto__,__v_isRef,__isVue"),aa=new Set(Object.getOwnPropertyNames(Symbol).filter((e=>"arguments"!==e&&"caller"!==e)).map((e=>Symbol[e])).filter(vn)),oa=sa();function sa(){var e={};return["includes","indexOf","lastIndexOf"].forEach((t=>{e[t]=function(){for(var e=Ya(this),n=0,r=this.length;n<r;n++)na(e,0,n+"");for(var i=arguments.length,a=new Array(i),o=0;o<i;o++)a[o]=arguments[o];var s=e[t](...a);return-1===s||!1===s?e[t](...a.map(Ya)):s}})),["push","pop","shift","unshift","splice"].forEach((t=>{e[t]=function(){Wi(),Yi();for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];var i=Ya(this)[t].apply(this,n);return Xi(),Ui(),i}})),e}function la(e){var t=Ya(this);return na(t,0,e),t.hasOwnProperty(e)}class ua{constructor(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];this._isReadonly=e,this._isShallow=t}get(e,t,n){var r=this._isReadonly,i=this._isShallow;if("__v_isReactive"===t)return!r;if("__v_isReadonly"===t)return r;if("__v_isShallow"===t)return i;if("__v_raw"===t)return n===(r?i?Fa:Ra:i?Ba:Da).get(e)||Object.getPrototypeOf(e)===Object.getPrototypeOf(n)?e:void 0;var a=cn(e);if(!r){if(a&&un(oa,t))return Reflect.get(oa,t,n);if("hasOwnProperty"===t)return la}var o=Reflect.get(e,t,n);return(vn(t)?aa.has(t):ia(t))?o:(r||na(e,0,t),i?o:eo(o)?a&&xn(t)?o:o.value:gn(o)?r?ja(o):qa(o):o)}}class ca extends ua{constructor(){super(!1,arguments.length>0&&void 0!==arguments[0]&&arguments[0])}set(e,t,n,r){var i=e[t];if(!this._isShallow){var a=Ha(i);if(Wa(n)||Ha(n)||(i=Ya(i),n=Ya(n)),!cn(e)&&eo(i)&&!eo(n))return!a&&(i.value=n,!0)}var o=cn(e)&&xn(t)?Number(t)<e.length:un(e,t),s=Reflect.set(e,t,n,r);return e===Ya(r)&&(o?Ln(n,i)&&ra(e,"set",t,n):ra(e,"add",t,n)),s}deleteProperty(e,t){var n=un(e,t);e[t];var r=Reflect.deleteProperty(e,t);return r&&n&&ra(e,"delete",t,void 0),r}has(e,t){var n=Reflect.has(e,t);return vn(t)&&aa.has(t)||na(e,0,t),n}ownKeys(e){return na(e,0,cn(e)?"length":ea),Reflect.ownKeys(e)}}class da extends ua{constructor(){super(!0,arguments.length>0&&void 0!==arguments[0]&&arguments[0])}set(e,t){return!0}deleteProperty(e,t){return!0}}var ha=new ca,fa=new da,pa=new ca(!0),va=e=>e,ga=e=>Reflect.getPrototypeOf(e);function ma(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=arguments.length>3&&void 0!==arguments[3]&&arguments[3],i=Ya(e=e.__v_raw),a=Ya(t);n||(Ln(t,a)&&na(i,0,t),na(i,0,a));var{has:o}=ga(i),s=r?va:n?Ga:Za;return o.call(i,t)?s(e.get(t)):o.call(i,a)?s(e.get(a)):void(e!==i&&e.get(t))}function _a(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=this.__v_raw,r=Ya(n),i=Ya(e);return t||(Ln(e,i)&&na(r,0,e),na(r,0,i)),e===i?n.has(e):n.has(e)||n.has(i)}function ya(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return e=e.__v_raw,!t&&na(Ya(e),0,ea),Reflect.get(e,"size",e)}function ba(e){e=Ya(e);var t=Ya(this);return ga(t).has.call(t,e)||(t.add(e),ra(t,"add",e,e)),this}function wa(e,t){t=Ya(t);var n=Ya(this),{has:r,get:i}=ga(n),a=r.call(n,e);a||(e=Ya(e),a=r.call(n,e));var o=i.call(n,e);return n.set(e,t),a?Ln(t,o)&&ra(n,"set",e,t):ra(n,"add",e,t),this}function xa(e){var t=Ya(this),{has:n,get:r}=ga(t),i=n.call(t,e);i||(e=Ya(e),i=n.call(t,e)),r&&r.call(t,e);var a=t.delete(e);return i&&ra(t,"delete",e,void 0),a}function Sa(){var e=Ya(this),t=0!==e.size,n=e.clear();return t&&ra(e,"clear",void 0,void 0),n}function ka(e,t){return function(n,r){var i=this,a=i.__v_raw,o=Ya(a),s=t?va:e?Ga:Za;return!e&&na(o,0,ea),a.forEach(((e,t)=>n.call(r,s(e),s(t),i)))}}function Ca(e,t,n){return function(){var r=this.__v_raw,i=Ya(r),a=dn(i),o="entries"===e||e===Symbol.iterator&&a,s="keys"===e&&a,l=r[e](...arguments),u=n?va:t?Ga:Za;return!t&&na(i,0,s?ta:ea),{next(){var{value:e,done:t}=l.next();return t?{value:e,done:t}:{value:o?[u(e[0]),u(e[1])]:u(e),done:t}},[Symbol.iterator](){return this}}}}function Ta(e){return function(){return"delete"!==e&&("clear"===e?void 0:this)}}function Aa(){var e={get(e){return ma(this,e)},get size(){return ya(this)},has:_a,add:ba,set:wa,delete:xa,clear:Sa,forEach:ka(!1,!1)},t={get(e){return ma(this,e,!1,!0)},get size(){return ya(this)},has:_a,add:ba,set:wa,delete:xa,clear:Sa,forEach:ka(!1,!0)},n={get(e){return ma(this,e,!0)},get size(){return ya(this,!0)},has(e){return _a.call(this,e,!0)},add:Ta("add"),set:Ta("set"),delete:Ta("delete"),clear:Ta("clear"),forEach:ka(!0,!1)},r={get(e){return ma(this,e,!0,!0)},get size(){return ya(this,!0)},has(e){return _a.call(this,e,!0)},add:Ta("add"),set:Ta("set"),delete:Ta("delete"),clear:Ta("clear"),forEach:ka(!0,!0)};return["keys","values","entries",Symbol.iterator].forEach((i=>{e[i]=Ca(i,!1,!1),n[i]=Ca(i,!0,!1),t[i]=Ca(i,!1,!0),r[i]=Ca(i,!0,!0)})),[e,n,t,r]}var[Ma,Ea,Oa,La]=Aa();function za(e,t){var n=t?e?La:Oa:e?Ea:Ma;return(t,r,i)=>"__v_isReactive"===r?!e:"__v_isReadonly"===r?e:"__v_raw"===r?t:Reflect.get(un(n,r)&&r in t?n:t,r,i)}var Na={get:za(!1,!1)},Ia={get:za(!1,!0)},Pa={get:za(!0,!1)},Da=new WeakMap,Ba=new WeakMap,Ra=new WeakMap,Fa=new WeakMap;function qa(e){return Ha(e)?e:Va(e,!1,ha,Na,Da)}function ja(e){return Va(e,!0,fa,Pa,Ra)}function Va(e,t,n,r,i){if(!gn(e))return e;if(e.__v_raw&&(!t||!e.__v_isReactive))return e;var a=i.get(e);if(a)return a;var o,s=(o=e).__v_skip||!Object.isExtensible(o)?0:function(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}(bn(o));if(0===s)return e;var l=new Proxy(e,2===s?r:n);return i.set(e,l),l}function $a(e){return Ha(e)?$a(e.__v_raw):!(!e||!e.__v_isReactive)}function Ha(e){return!(!e||!e.__v_isReadonly)}function Wa(e){return!(!e||!e.__v_isShallow)}function Ua(e){return $a(e)||Ha(e)}function Ya(e){var t=e&&e.__v_raw;return t?Ya(t):e}function Xa(e){return Object.isExtensible(e)&&Nn(e,"__v_skip",!0),e}var Za=e=>gn(e)?qa(e):e,Ga=e=>gn(e)?ja(e):e;class Ka{constructor(e,t,n,r){this.getter=e,this._setter=t,this.dep=void 0,this.__v_isRef=!0,this.__v_isReadonly=!1,this.effect=new Ri((()=>e(this._value)),(()=>Qa(this,2===this.effect._dirtyLevel?2:3))),this.effect.computed=this,this.effect.active=this._cacheable=!r,this.__v_isReadonly=n}get value(){var e=Ya(this);return e._cacheable&&!e.effect.dirty||!Ln(e._value,e._value=e.effect.run())||Qa(e,4),Ja(e),e.effect._dirtyLevel>=2&&Qa(e,2),e._value}set value(e){this._setter(e)}get _dirty(){return this.effect.dirty}set _dirty(e){this.effect.dirty=e}}function Ja(e){var t;Vi&&Ei&&(e=Ya(e),Zi(Ei,null!=(t=e.dep)?t:e.dep=Ji((()=>e.dep=void 0),e instanceof Ka?e:void 0)))}function Qa(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:4,n=(e=Ya(e)).dep;n&&Ki(n,t)}function eo(e){return!(!e||!0!==e.__v_isRef)}function to(e){return ro(e,!1)}function no(e){return ro(e,!0)}function ro(e,t){return eo(e)?e:new io(e,t)}class io{constructor(e,t){this.__v_isShallow=t,this.dep=void 0,this.__v_isRef=!0,this._rawValue=t?e:Ya(e),this._value=t?e:Za(e)}get value(){return Ja(this),this._value}set value(e){var t=this.__v_isShallow||Wa(e)||Ha(e);e=t?e:Ya(e),Ln(e,this._rawValue)&&(this._rawValue=e,this._value=t?e:Za(e),Qa(this,4,e))}}var ao={get:(e,t,n)=>{return eo(r=Reflect.get(e,t,n))?r.value:r;var r},set:(e,t,n,r)=>{var i=e[t];return eo(i)&&!eo(n)?(i.value=n,!0):Reflect.set(e,t,n,r)}};function oo(e){return $a(e)?e:new Proxy(e,ao)}function so(e,t,n,r){try{return r?e(...r):e()}catch(i){uo(i,t,n)}}function lo(e,t,n,r){if(fn(e)){var i=so(e,t,n,r);return i&&mn(i)&&i.catch((e=>{uo(e,t,n)})),i}for(var a=[],o=0;o<e.length;o++)a.push(lo(e[o],t,n,r));return a}function uo(e,t,n){if(t&&t.vnode,t){for(var r=t.parent,i=t.proxy,a="https://vuejs.org/error-reference/#runtime-".concat(n);r;){var o=r.ec;if(o)for(var s=0;s<o.length;s++)if(!1===o[s](e,i,a))return;r=r.parent}var l=t.appContext.config.errorHandler;if(l)return void so(l,null,10,[e,i,a])}!function(e){e instanceof Error?console.error(e.message+"\n"+e.stack):console.error(e)}(e)}var co=!1,ho=!1,fo=[],po=0,vo=[],go=null,mo=0,_o=Promise.resolve(),yo=null;function bo(e){var t=yo||_o;return e?t.then(this?e.bind(this):e):t}function wo(e){fo.length&&fo.includes(e,co&&e.allowRecurse?po+1:po)||(null==e.id?fo.push(e):fo.splice(function(e){for(var t=po+1,n=fo.length;t<n;){var r=t+n>>>1,i=fo[r],a=Co(i);a<e||a===e&&i.pre?t=r+1:n=r}return t}(e.id),0,e),xo())}function xo(){co||ho||(ho=!0,yo=_o.then(Ao))}function So(e,t){for(var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:co?po+1:0;n<fo.length;n++){var r=fo[n];if(r&&r.pre){if(e&&r.id!==e.uid)continue;fo.splice(n,1),n--,r()}}}function ko(e){if(vo.length){var t=[...new Set(vo)].sort(((e,t)=>Co(e)-Co(t)));if(vo.length=0,go)return void go.push(...t);for(go=t,mo=0;mo<go.length;mo++)go[mo]();go=null,mo=0}}var Co=e=>null==e.id?1/0:e.id,To=(e,t)=>{var n=Co(e)-Co(t);if(0===n){if(e.pre&&!t.pre)return-1;if(t.pre&&!e.pre)return 1}return n};function Ao(e){ho=!1,co=!0,fo.sort(To);try{for(po=0;po<fo.length;po++){var t=fo[po];t&&!1!==t.active&&so(t,null,14)}}finally{po=0,fo.length=0,ko(),co=!1,yo=null,(fo.length||vo.length)&&Ao()}}function Mo(e,t){if(!e.isUnmounted){for(var n=e.vnode.props||Qt,r=arguments.length,i=new Array(r>2?r-2:0),a=2;a<r;a++)i[a-2]=arguments[a];var o,s=i,l=t.startsWith("update:"),u=l&&t.slice(7);if(u&&u in n){var c="".concat("modelValue"===u?"model":u,"Modifiers"),{number:d,trim:h}=n[c]||Qt;h&&(s=i.map((e=>pn(e)?e.trim():e))),d&&(s=i.map(In))}var f=n[o=On(t)]||n[o=On(Tn(t))];!f&&l&&(f=n[o=On(Mn(t))]),f&&lo(f,e,6,s);var p=n[o+"Once"];if(p){if(e.emitted){if(e.emitted[o])return}else e.emitted={};e.emitted[o]=!0,lo(p,e,6,s)}}}function Eo(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=t.emitsCache,i=r.get(e);if(void 0!==i)return i;var a=e.emits,o={},s=!1;if(!fn(e)){var l=e=>{var n=Eo(e,t,!0);n&&(s=!0,on(o,n))};!n&&t.mixins.length&&t.mixins.forEach(l),e.extends&&l(e.extends),e.mixins&&e.mixins.forEach(l)}return a||s?(cn(a)?a.forEach((e=>o[e]=null)):on(o,a),gn(e)&&r.set(e,o),o):(gn(e)&&r.set(e,null),null)}function Oo(e,t){return!(!e||!rn(t))&&(t=t.slice(2).replace(/Once$/,""),un(e,t[0].toLowerCase()+t.slice(1))||un(e,Mn(t))||un(e,t))}var Lo=null,zo=null;function No(e){var t=Lo;return Lo=e,zo=e&&e.type.__scopeId||null,t}function Io(e){var t,n,{type:r,vnode:i,proxy:a,withProxy:o,props:s,propsOptions:[l],slots:u,attrs:c,emit:d,render:h,renderCache:f,data:p,setupState:v,ctx:g,inheritAttrs:m}=e,_=No(e);try{if(4&i.shapeFlag){var y=o||a,b=y;t=bl(h.call(b,y,f,s,v,p,g)),n=c}else{var w=r;0,t=bl(w.length>1?w(s,{attrs:c,slots:u,emit:d}):w(s,null)),n=r.props?c:Po(c)}}catch(C){uo(C,e,1),t=ml(sl)}var x=t;if(n&&!1!==m){var S=Object.keys(n),{shapeFlag:k}=x;S.length&&7&k&&(l&&S.some(an)&&(n=Do(n,l)),x=_l(x,n))}return i.dirs&&((x=_l(x)).dirs=x.dirs?x.dirs.concat(i.dirs):i.dirs),i.transition&&(x.transition=i.transition),t=x,No(_),t}var Po=e=>{var t;for(var n in e)("class"===n||"style"===n||rn(n))&&((t||(t={}))[n]=e[n]);return t},Do=(e,t)=>{var n={};for(var r in e)an(r)&&r.slice(9)in t||(n[r]=e[r]);return n};function Bo(e,t,n){var r=Object.keys(t);if(r.length!==Object.keys(e).length)return!0;for(var i=0;i<r.length;i++){var a=r[i];if(t[a]!==e[a]&&!Oo(n,a))return!0}return!1}var Ro=Symbol.for("v-ndc");var Fo=Symbol.for("v-scx"),qo=()=>Ds(Fo);var jo={};function Vo(e,t,n){return $o(e,t,n)}function $o(e,t){var{immediate:n,deep:r,flush:i,once:a,onTrack:o,onTrigger:s}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:Qt;if(t&&a){var l=t;t=function(){l(...arguments),k()}}var u,c,d=El,h=e=>!0===r?e:Uo(e,!1===r?1:void 0),f=!1,p=!1;if(eo(e)?(u=()=>e.value,f=Wa(e)):$a(e)?(u=()=>h(e),f=!0):cn(e)?(p=!0,f=e.some((e=>$a(e)||Wa(e))),u=()=>e.map((e=>eo(e)?e.value:$a(e)?h(e):fn(e)?so(e,d,2):void 0))):u=fn(e)?t?()=>so(e,d,2):()=>(c&&c(),lo(e,d,3,[m])):tn,t&&r){var v=u;u=()=>Uo(v())}var g,m=e=>{c=x.onStop=()=>{so(e,d,4),c=x.onStop=void 0}};if(Bl){if(m=tn,t?n&&lo(t,d,3,[u(),p?[]:void 0,m]):u(),"sync"!==i)return tn;var _=qo();g=_.__watcherHandles||(_.__watcherHandles=[])}var y,b=p?new Array(e.length).fill(jo):jo,w=()=>{if(x.active&&x.dirty)if(t){var e=x.run();(r||f||(p?e.some(((e,t)=>Ln(e,b[t]))):Ln(e,b)))&&(c&&c(),lo(t,d,3,[e,b===jo?void 0:p&&b[0]===jo?[]:b,m]),b=e)}else x.run()};w.allowRecurse=!!t,"sync"===i?y=w:"post"===i?y=()=>Qs(w,d&&d.suspense):(w.pre=!0,d&&(w.id=d.uid),y=()=>wo(w));var x=new Ri(u,tn,y),S=Mi,k=()=>{x.stop(),S&&sn(S.effects,x)};return t?n?w():b=x.run():"post"===i?Qs(x.run.bind(x),d&&d.suspense):x.run(),g&&g.push(k),k}function Ho(e,t,n){var r,i=this.proxy,a=pn(e)?e.includes(".")?Wo(i,e):()=>i[e]:e.bind(i,i);fn(t)?r=t:(r=t.handler,n=t);var o=Nl(this),s=$o(a,r.bind(i),n);return o(),s}function Wo(e,t){var n=t.split(".");return()=>{for(var t=e,r=0;r<n.length&&t;r++)t=t[n[r]];return t}}function Uo(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,r=arguments.length>3?arguments[3]:void 0;if(!gn(e)||e.__v_skip)return e;if(t&&t>0){if(n>=t)return e;n++}if((r=r||new Set).has(e))return e;if(r.add(e),eo(e))Uo(e.value,t,n,r);else if(cn(e))for(var i=0;i<e.length;i++)Uo(e[i],t,n,r);else if(hn(e)||dn(e))e.forEach((e=>{Uo(e,t,n,r)}));else if(wn(e))for(var a in e)Uo(e[a],t,n,r);return e}function Yo(e,t){if(null===Lo)return e;for(var n=ql(Lo)||Lo.proxy,r=e.dirs||(e.dirs=[]),i=0;i<t.length;i++){var[a,o,s,l=Qt]=t[i];a&&(fn(a)&&(a={mounted:a,updated:a}),a.deep&&Uo(o),r.push({dir:a,instance:n,value:o,oldValue:void 0,arg:s,modifiers:l}))}return e}function Xo(e,t,n,r){for(var i=e.dirs,a=t&&t.dirs,o=0;o<i.length;o++){var s=i[o];a&&(s.oldValue=a[o].value);var l=s.dir[r];l&&(Wi(),lo(l,n,8,[e.el,s,e,t]),Ui())}}
/*! #__NO_SIDE_EFFECTS__ */function Zo(e,t){return fn(e)?(()=>on({name:e.name},t,{setup:e}))():e}var Go=e=>!!e.type.__asyncLoader,Ko=e=>e.type.__isKeepAlive;function Jo(e,t){es(e,"a",t)}function Qo(e,t){es(e,"da",t)}function es(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:El,r=e.__wdc||(e.__wdc=()=>{for(var t=n;t;){if(t.isDeactivated)return;t=t.parent}return e()});if(ns(t,r,n),n)for(var i=n.parent;i&&i.parent;)Ko(i.parent.vnode)&&ts(r,t,n,i),i=i.parent}function ts(e,t,n,r){var i=ns(t,e,r,!0);us((()=>{sn(r[t],i)}),n)}function ns(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:El,r=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(n){var i=n[e]||(n[e]=[]),a=t.__weh||(t.__weh=function(){if(!n.isUnmounted){Wi();for(var r=Nl(n),i=arguments.length,a=new Array(i),o=0;o<i;o++)a[o]=arguments[o];var s=lo(t,n,e,a);return r(),Ui(),s}});return r?i.unshift(a):i.push(a),a}}var rs=e=>function(t){return(!Bl||"sp"===e)&&ns(e,(function(){return t(...arguments)}),arguments.length>1&&void 0!==arguments[1]?arguments[1]:El)},is=rs("bm"),as=rs("m"),os=rs("bu"),ss=rs("u"),ls=rs("bum"),us=rs("um"),cs=rs("sp"),ds=rs("rtg"),hs=rs("rtc");function fs(e){ns("ec",e,arguments.length>1&&void 0!==arguments[1]?arguments[1]:El)}var ps=e=>e?Pl(e)?ql(e)||e.proxy:ps(e.parent):null,vs=on(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>ps(e.parent),$root:e=>ps(e.root),$emit:e=>e.emit,$options:e=>Ss(e),$forceUpdate:e=>e.f||(e.f=()=>{e.effect.dirty=!0,wo(e.update)}),$nextTick:e=>e.n||(e.n=bo.bind(e.proxy)),$watch:e=>Ho.bind(e)}),gs=(e,t)=>e!==Qt&&!e.__isScriptSetup&&un(e,t),ms={get(e,t){var n,{_:r}=e,{ctx:i,setupState:a,data:o,props:s,accessCache:l,type:u,appContext:c}=r;if("$"!==t[0]){var d=l[t];if(void 0!==d)switch(d){case 1:return a[t];case 2:return o[t];case 4:return i[t];case 3:return s[t]}else{if(gs(a,t))return l[t]=1,a[t];if(o!==Qt&&un(o,t))return l[t]=2,o[t];if((n=r.propsOptions[0])&&un(n,t))return l[t]=3,s[t];if(i!==Qt&&un(i,t))return l[t]=4,i[t];ys&&(l[t]=0)}}var h,f,p=vs[t];return p?("$attrs"===t&&na(r,0,t),p(r)):(h=u.__cssModules)&&(h=h[t])?h:i!==Qt&&un(i,t)?(l[t]=4,i[t]):(f=c.config.globalProperties,un(f,t)?f[t]:void 0)},set(e,t,n){var{_:r}=e,{data:i,setupState:a,ctx:o}=r;return gs(a,t)?(a[t]=n,!0):i!==Qt&&un(i,t)?(i[t]=n,!0):!un(r.props,t)&&(("$"!==t[0]||!(t.slice(1)in r))&&(o[t]=n,!0))},has(e,t){var n,{_:{data:r,setupState:i,accessCache:a,ctx:o,appContext:s,propsOptions:l}}=e;return!!a[t]||r!==Qt&&un(r,t)||gs(i,t)||(n=l[0])&&un(n,t)||un(o,t)||un(vs,t)||un(s.config.globalProperties,t)},defineProperty(e,t,n){return null!=n.get?e._.accessCache[t]=0:un(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function _s(e){return cn(e)?e.reduce(((e,t)=>(e[t]=null,e)),{}):e}var ys=!0;function bs(e){var t=Ss(e),n=e.proxy,r=e.ctx;ys=!1,t.beforeCreate&&ws(t.beforeCreate,e,"bc");var i,{data:a,computed:o,methods:s,watch:l,provide:u,inject:c,created:d,beforeMount:h,mounted:f,beforeUpdate:p,updated:v,activated:g,deactivated:m,beforeDestroy:_,beforeUnmount:y,destroyed:b,unmounted:w,render:x,renderTracked:S,renderTriggered:k,errorCaptured:C,serverPrefetch:T,expose:A,inheritAttrs:M,components:E,directives:O,filters:L}=t;if(c&&function(e,t){cn(e)&&(e=As(e));var n=function(n){var r=e[n],i=void 0;eo(i=gn(r)?"default"in r?Ds(r.from||n,r.default,!0):Ds(r.from||n):Ds(r))?Object.defineProperty(t,n,{enumerable:!0,configurable:!0,get:()=>i.value,set:e=>i.value=e}):t[n]=i};for(var r in e)n(r)}(c,r),s)for(var z in s){var N=s[z];fn(N)&&(r[z]=N.bind(n))}if(a&&(i=a.call(n,n),gn(i)&&(e.data=qa(i))),ys=!0,o){var I=function(e){var t=o[e],i=fn(t)?t.bind(n,n):fn(t.get)?t.get.bind(n,n):tn,a=!fn(t)&&fn(t.set)?t.set.bind(n):tn,s=jl({get:i,set:a});Object.defineProperty(r,e,{enumerable:!0,configurable:!0,get:()=>s.value,set:e=>s.value=e})};for(var P in o)I(P)}if(l)for(var D in l)xs(l[D],r,n,D);if(u){var B=fn(u)?u.call(n):u;Reflect.ownKeys(B).forEach((e=>{Ps(e,B[e])}))}function R(e,t){cn(t)?t.forEach((t=>e(t.bind(n)))):t&&e(t.bind(n))}if(d&&ws(d,e,"c"),R(is,h),R(as,f),R(os,p),R(ss,v),R(Jo,g),R(Qo,m),R(fs,C),R(hs,S),R(ds,k),R(ls,y),R(us,w),R(cs,T),cn(A))if(A.length){var F=e.exposed||(e.exposed={});A.forEach((e=>{Object.defineProperty(F,e,{get:()=>n[e],set:t=>n[e]=t})}))}else e.exposed||(e.exposed={});x&&e.render===tn&&(e.render=x),null!=M&&(e.inheritAttrs=M),E&&(e.components=E),O&&(e.directives=O)}function ws(e,t,n){lo(cn(e)?e.map((e=>e.bind(t.proxy))):e.bind(t.proxy),t,n)}function xs(e,t,n,r){var i=r.includes(".")?Wo(n,r):()=>n[r];if(pn(e)){var a=t[e];fn(a)&&Vo(i,a)}else if(fn(e))Vo(i,e.bind(n));else if(gn(e))if(cn(e))e.forEach((e=>xs(e,t,n,r)));else{var o=fn(e.handler)?e.handler.bind(n):t[e.handler];fn(o)&&Vo(i,o,e)}}function Ss(e){var t,n=e.type,{mixins:r,extends:i}=n,{mixins:a,optionsCache:o,config:{optionMergeStrategies:s}}=e.appContext,l=o.get(n);return l?t=l:a.length||r||i?(t={},a.length&&a.forEach((e=>ks(t,e,s,!0))),ks(t,n,s)):t=n,gn(n)&&o.set(n,t),t}function ks(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]&&arguments[3],{mixins:i,extends:a}=t;for(var o in a&&ks(e,a,n,!0),i&&i.forEach((t=>ks(e,t,n,!0))),t)if(r&&"expose"===o);else{var s=Cs[o]||n&&n[o];e[o]=s?s(e[o],t[o]):t[o]}return e}var Cs={data:Ts,props:Os,emits:Os,methods:Es,computed:Es,beforeCreate:Ms,created:Ms,beforeMount:Ms,mounted:Ms,beforeUpdate:Ms,updated:Ms,beforeDestroy:Ms,beforeUnmount:Ms,destroyed:Ms,unmounted:Ms,activated:Ms,deactivated:Ms,errorCaptured:Ms,serverPrefetch:Ms,components:Es,directives:Es,watch:function(e,t){if(!e)return t;if(!t)return e;var n=on(Object.create(null),e);for(var r in t)n[r]=Ms(e[r],t[r]);return n},provide:Ts,inject:function(e,t){return Es(As(e),As(t))}};function Ts(e,t){return t?e?function(){return on(fn(e)?e.call(this,this):e,fn(t)?t.call(this,this):t)}:t:e}function As(e){if(cn(e)){for(var t={},n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function Ms(e,t){return e?[...new Set([].concat(e,t))]:t}function Es(e,t){return e?on(Object.create(null),e,t):t}function Os(e,t){return e?cn(e)&&cn(t)?[...new Set([...e,...t])]:on(Object.create(null),_s(e),_s(null!=t?t:{})):t}function Ls(){return{app:null,config:{isNativeTag:nn,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}var zs=0;function Ns(e,t){return function(n){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;fn(n)||(n=on({},n)),null==r||gn(r)||(r=null);var i=Ls(),a=new WeakSet,o=!1,s=i.app={_uid:zs++,_component:n,_props:r,_container:null,_context:i,_instance:null,version:$l,get config(){return i.config},set config(e){},use(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];return a.has(e)||(e&&fn(e.install)?(a.add(e),e.install(s,...n)):fn(e)&&(a.add(e),e(s,...n))),s},mixin:e=>(i.mixins.includes(e)||i.mixins.push(e),s),component:(e,t)=>t?(i.components[e]=t,s):i.components[e],directive:(e,t)=>t?(i.directives[e]=t,s):i.directives[e],mount(a,l,u){if(!o){var c=ml(n,r);return c.appContext=i,!0===u?u="svg":!1===u&&(u=void 0),l&&t?t(c,a):e(c,a,u),o=!0,s._container=a,a.__vue_app__=s,ql(c.component)||c.component.proxy}},unmount(){o&&(e(null,s._container),delete s._container.__vue_app__)},provide:(e,t)=>(i.provides[e]=t,s),runWithContext(e){var t=Is;Is=s;try{return e()}finally{Is=t}}};return s}}var Is=null;function Ps(e,t){if(El){var n=El.provides,r=El.parent&&El.parent.provides;r===n&&(n=El.provides=Object.create(r)),n[e]=t}else;}function Ds(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=El||Lo;if(r||Is){var i=r?null==r.parent?r.vnode.appContext&&r.vnode.appContext.provides:r.parent.provides:Is._context.provides;if(i&&e in i)return i[e];if(arguments.length>1)return n&&fn(t)?t.call(r&&r.proxy):t}}function Bs(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]&&arguments[3],i={},a={};for(var o in Nn(a,pl,1),e.propsDefaults=Object.create(null),Rs(e,t,i,a),e.propsOptions[0])o in i||(i[o]=void 0);n?e.props=r?i:Va(i,!1,pa,Ia,Ba):e.type.props?e.props=i:e.props=a,e.attrs=a}function Rs(e,t,n,r){var i,[a,o]=e.propsOptions,s=!1;if(t)for(var l in t)if(!Sn(l)){var u=t[l],c=void 0;a&&un(a,c=Tn(l))?o&&o.includes(c)?(i||(i={}))[c]=u:n[c]=u:Oo(e.emitsOptions,l)||l in r&&u===r[l]||(r[l]=u,s=!0)}if(o)for(var d=Ya(n),h=i||Qt,f=0;f<o.length;f++){var p=o[f];n[p]=Fs(a,d,p,h[p],e,!un(h,p))}return s}function Fs(e,t,n,r,i,a){var o=e[n];if(null!=o){var s=un(o,"default");if(s&&void 0===r){var l=o.default;if(o.type!==Function&&!o.skipFactory&&fn(l)){var{propsDefaults:u}=i;if(n in u)r=u[n];else{var c=Nl(i);r=u[n]=l.call(null,t),c()}}else r=l}o[0]&&(a&&!s?r=!1:!o[1]||""!==r&&r!==Mn(n)||(r=!0))}return r}function qs(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=t.propsCache,i=r.get(e);if(i)return i;var a=e.props,o={},s=[],l=!1;if(!fn(e)){var u=e=>{l=!0;var[n,r]=qs(e,t,!0);on(o,n),r&&s.push(...r)};!n&&t.mixins.length&&t.mixins.forEach(u),e.extends&&u(e.extends),e.mixins&&e.mixins.forEach(u)}if(!a&&!l)return gn(e)&&r.set(e,en),en;if(cn(a))for(var c=0;c<a.length;c++){var d=Tn(a[c]);js(d)&&(o[d]=Qt)}else if(a)for(var h in a){var f=Tn(h);if(js(f)){var p=a[h],v=o[f]=cn(p)||fn(p)?{type:p}:on({},p);if(v){var g=Hs(Boolean,v.type),m=Hs(String,v.type);v[0]=g>-1,v[1]=m<0||g<m,(g>-1||un(v,"default"))&&s.push(f)}}}var _=[o,s];return gn(e)&&r.set(e,_),_}function js(e){return"$"!==e[0]&&!Sn(e)}function Vs(e){return null===e?"null":"function"==typeof e?e.name||"":"object"==typeof e&&e.constructor&&e.constructor.name||""}function $s(e,t){return Vs(e)===Vs(t)}function Hs(e,t){return cn(t)?t.findIndex((t=>$s(t,e))):fn(t)&&$s(t,e)?0:-1}var Ws=e=>"_"===e[0]||"$stable"===e,Us=e=>cn(e)?e.map(bl):[bl(e)],Ys=(e,t,n)=>{if(t._n)return t;var r=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Lo;if(!t)return e;if(e._n)return e;var n=function(){n._d&&dl(-1);var r,i=No(t);try{r=e(...arguments)}finally{No(i),n._d&&dl(1)}return r};return n._n=!0,n._c=!0,n._d=!0,n}((function(){return Us(t(...arguments))}),n);return r._c=!1,r},Xs=(e,t,n)=>{var r=e._ctx;for(var i in e)if(!Ws(i)){var a=e[i];fn(a)?t[i]=Ys(0,a,r):null!=a&&function(){var e=Us(a);t[i]=()=>e}()}},Zs=(e,t)=>{var n=Us(t);e.slots.default=()=>n},Gs=(e,t)=>{if(32&e.vnode.shapeFlag){var n=t._;n?(e.slots=Ya(t),Nn(t,"_",n)):Xs(t,e.slots={})}else e.slots={},t&&Zs(e,t);Nn(e.slots,pl,1)},Ks=(e,t,n)=>{var{vnode:r,slots:i}=e,a=!0,o=Qt;if(32&r.shapeFlag){var s=t._;s?n&&1===s?a=!1:(on(i,t),n||1!==s||delete i._):(a=!t.$stable,Xs(t,i)),o=t}else t&&(Zs(e,t),o={default:1});if(a)for(var l in i)Ws(l)||null!=o[l]||delete i[l]};function Js(e,t,n,r){var i=arguments.length>4&&void 0!==arguments[4]&&arguments[4];if(cn(e))e.forEach(((e,a)=>Js(e,t&&(cn(t)?t[a]:t),n,r,i)));else if(!Go(r)||i){var a=4&r.shapeFlag?ql(r.component)||r.component.proxy:r.el,o=i?null:a,{i:s,r:l}=e,u=t&&t.r,c=s.refs===Qt?s.refs={}:s.refs,d=s.setupState;if(null!=u&&u!==l&&(pn(u)?(c[u]=null,un(d,u)&&(d[u]=null)):eo(u)&&(u.value=null)),fn(l))so(l,s,12,[o,c]);else{var h=pn(l),f=eo(l);if(h||f){var p=()=>{if(e.f){var t=h?un(d,l)?d[l]:c[l]:l.value;i?cn(t)&&sn(t,a):cn(t)?t.includes(a)||t.push(a):h?(c[l]=[a],un(d,l)&&(d[l]=c[l])):(l.value=[a],e.k&&(c[e.k]=l.value))}else h?(c[l]=o,un(d,l)&&(d[l]=o)):f&&(l.value=o,e.k&&(c[e.k]=o))};o?(p.id=-1,Qs(p,n)):p()}}}}var Qs=function(e,t){var n;t&&t.pendingBranch?cn(e)?t.effects.push(...e):t.effects.push(e):(cn(n=e)?vo.push(...n):go&&go.includes(n,n.allowRecurse?mo+1:mo)||vo.push(n),xo())};function el(e){return function(e){Pn().__VUE__=!0;var t,n,{insert:r,remove:i,patchProp:a,createElement:o,createText:s,createComment:l,setText:u,setElementText:c,parentNode:d,nextSibling:h,setScopeId:f=tn,insertStaticContent:p}=e,v=function(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null,i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:null,a=arguments.length>5&&void 0!==arguments[5]?arguments[5]:null,o=arguments.length>6&&void 0!==arguments[6]?arguments[6]:void 0,s=arguments.length>7&&void 0!==arguments[7]?arguments[7]:null,l=arguments.length>8&&void 0!==arguments[8]?arguments[8]:!!t.dynamicChildren;if(e!==t){e&&!fl(e,t)&&(r=$(e),R(e,i,a,!0),e=null),-2===t.patchFlag&&(l=!1,t.dynamicChildren=null);var{type:u,ref:c,shapeFlag:d}=t;switch(u){case ol:g(e,t,n,r);break;case sl:m(e,t,n,r);break;case ll:null==e&&_(t,n,r,o);break;case al:M(e,t,n,r,i,a,o,s,l);break;default:1&d?w(e,t,n,r,i,a,o,s,l):6&d?E(e,t,n,r,i,a,o,s,l):(64&d||128&d)&&u.process(e,t,n,r,i,a,o,s,l,U)}null!=c&&i&&Js(c,e&&e.ref,a,t||e,!t)}},g=(e,t,n,i)=>{if(null==e)r(t.el=s(t.children),n,i);else{var a=t.el=e.el;t.children!==e.children&&u(a,t.children)}},m=(e,t,n,i)=>{null==e?r(t.el=l(t.children||""),n,i):t.el=e.el},_=(e,t,n,r)=>{[e.el,e.anchor]=p(e.children,t,n,r,e.el,e.anchor)},y=(e,t,n)=>{for(var i,{el:a,anchor:o}=e;a&&a!==o;)i=h(a),r(a,t,n),a=i;r(o,t,n)},b=e=>{for(var t,{el:n,anchor:r}=e;n&&n!==r;)t=h(n),i(n),n=t;i(r)},w=(e,t,n,r,i,a,o,s,l)=>{"svg"===t.type?o="svg":"math"===t.type&&(o="mathml"),null==e?x(t,n,r,i,a,o,s,l):C(e,t,i,a,o,s,l)},x=(e,t,n,i,s,l,u,d)=>{var h,f,{props:p,shapeFlag:v,transition:g,dirs:m}=e;if(h=e.el=o(e.type,l,p&&p.is,p),8&v?c(h,e.children):16&v&&k(e.children,h,null,i,s,tl(e,l),u,d),m&&Xo(e,null,i,"created"),S(h,e,e.scopeId,u,i),p){for(var _ in p)"value"===_||Sn(_)||a(h,_,null,p[_],l,e.children,i,s,V);"value"in p&&a(h,"value",null,p.value,l),(f=p.onVnodeBeforeMount)&&kl(f,i,e)}Object.defineProperty(h,"__vueParentComponent",{value:i,enumerable:!1}),m&&Xo(e,null,i,"beforeMount");var y=function(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}(s,g);y&&g.beforeEnter(h),r(h,t,n),((f=p&&p.onVnodeMounted)||y||m)&&Qs((()=>{f&&kl(f,i,e),y&&g.enter(h),m&&Xo(e,null,i,"mounted")}),s)},S=(e,t,n,r,i)=>{if(n&&f(e,n),r)for(var a=0;a<r.length;a++)f(e,r[a]);if(i&&t===i.subTree){var o=i.vnode;S(e,o,o.scopeId,o.slotScopeIds,i.parent)}},k=function(e,t,n,r,i,a,o,s){for(var l=arguments.length>8&&void 0!==arguments[8]?arguments[8]:0;l<e.length;l++){var u=e[l]=s?wl(e[l]):bl(e[l]);v(null,u,t,n,r,i,a,o,s)}},C=(e,t,n,r,i,o,s)=>{var l=t.el=e.el,{patchFlag:u,dynamicChildren:d,dirs:h}=t;u|=16&e.patchFlag;var f,p=e.props||Qt,v=t.props||Qt;if(n&&nl(n,!1),(f=v.onVnodeBeforeUpdate)&&kl(f,n,t,e),h&&Xo(t,e,n,"beforeUpdate"),n&&nl(n,!0),d?T(e.dynamicChildren,d,l,n,r,tl(t,i),o):s||I(e,t,l,null,n,r,tl(t,i),o,!1),u>0){if(16&u)A(l,t,p,v,n,r,i);else if(2&u&&p.class!==v.class&&a(l,"class",null,v.class,i),4&u&&a(l,"style",p.style,v.style,i),8&u)for(var g=t.dynamicProps,m=0;m<g.length;m++){var _=g[m],y=p[_],b=v[_];b===y&&"value"!==_||a(l,_,y,b,i,e.children,n,r,V)}1&u&&e.children!==t.children&&c(l,t.children)}else s||null!=d||A(l,t,p,v,n,r,i);((f=v.onVnodeUpdated)||h)&&Qs((()=>{f&&kl(f,n,t,e),h&&Xo(t,e,n,"updated")}),r)},T=(e,t,n,r,i,a,o)=>{for(var s=0;s<t.length;s++){var l=e[s],u=t[s],c=l.el&&(l.type===al||!fl(l,u)||70&l.shapeFlag)?d(l.el):n;v(l,u,c,null,r,i,a,o,!0)}},A=(e,t,n,r,i,o,s)=>{if(n!==r){if(n!==Qt)for(var l in n)Sn(l)||l in r||a(e,l,n[l],null,s,t.children,i,o,V);for(var u in r)if(!Sn(u)){var c=r[u],d=n[u];c!==d&&"value"!==u&&a(e,u,d,c,s,t.children,i,o,V)}"value"in r&&a(e,"value",n.value,r.value,s)}},M=(e,t,n,i,a,o,l,u,c)=>{var d=t.el=e?e.el:s(""),h=t.anchor=e?e.anchor:s(""),{patchFlag:f,dynamicChildren:p,slotScopeIds:v}=t;v&&(u=u?u.concat(v):v),null==e?(r(d,n,i),r(h,n,i),k(t.children||[],n,h,a,o,l,u,c)):f>0&&64&f&&p&&e.dynamicChildren?(T(e.dynamicChildren,p,n,a,o,l,u),(null!=t.key||a&&t===a.subTree)&&rl(e,t,!0)):I(e,t,n,h,a,o,l,u,c)},E=(e,t,n,r,i,a,o,s,l)=>{t.slotScopeIds=s,null==e?512&t.shapeFlag?i.ctx.activate(t,n,r,o,l):O(t,n,r,i,a,o,l):L(e,t,l)},O=(e,t,n,r,i,a,o)=>{var s=e.component=function(e,t,n){var r=e.type,i=(t?t.appContext:e.appContext)||Cl,a={uid:Tl++,vnode:e,type:r,parent:t,appContext:i,root:null,next:null,subTree:null,effect:null,update:null,scope:new Bi(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(i.provides),accessCache:null,renderCache:[],components:null,directives:null,propsOptions:qs(r,i),emitsOptions:Eo(r,i),emit:null,emitted:null,propsDefaults:Qt,inheritAttrs:r.inheritAttrs,ctx:Qt,data:Qt,props:Qt,attrs:Qt,slots:Qt,refs:Qt,setupState:Qt,setupContext:null,attrsProxy:null,slotsProxy:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};a.ctx={_:a},a.root=t?t.root:a,a.emit=Mo.bind(null,a),e.ce&&e.ce(a);return a}(e,r,i);if(Ko(e)&&(s.ctx.renderer=U),function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];t&&Ml(t);var{props:n,children:r}=e.vnode,i=Pl(e);Bs(e,n,i,t),Gs(e,r);var a=i?function(e,t){var n=e.type;e.accessCache=Object.create(null),e.proxy=Xa(new Proxy(e.ctx,ms));var{setup:r}=n;if(r){var i=e.setupContext=r.length>1?function(e){var t=t=>{e.exposed=t||{}};return{get attrs(){return function(e){return e.attrsProxy||(e.attrsProxy=new Proxy(e.attrs,{get:(t,n)=>(na(e,0,"$attrs"),t[n])}))}(e)},slots:e.slots,emit:e.emit,expose:t}}(e):null,a=Nl(e);Wi();var o=so(r,e,0,[e.props,i]);if(Ui(),a(),mn(o)){if(o.then(Il,Il),t)return o.then((n=>{Rl(e,n,t)})).catch((t=>{uo(t,e,0)}));e.asyncDep=o}else Rl(e,o,t)}else Fl(e,t)}(e,t):void 0;t&&Ml(!1)}(s),s.asyncDep){if(i&&i.registerDep(s,z),!e.el){var l=s.subTree=ml(sl);m(null,l,t,n)}}else z(s,e,t,n,i,a,o)},L=(e,t,n)=>{var r,i,a=t.component=e.component;if(function(e,t,n){var{props:r,children:i,component:a}=e,{props:o,children:s,patchFlag:l}=t,u=a.emitsOptions;if(t.dirs||t.transition)return!0;if(!(n&&l>=0))return!(!i&&!s||s&&s.$stable)||r!==o&&(r?!o||Bo(r,o,u):!!o);if(1024&l)return!0;if(16&l)return r?Bo(r,o,u):!!o;if(8&l)for(var c=t.dynamicProps,d=0;d<c.length;d++){var h=c[d];if(o[h]!==r[h]&&!Oo(u,h))return!0}return!1}(e,t,n)){if(a.asyncDep&&!a.asyncResolved)return void N(a,t,n);a.next=t,r=a.update,(i=fo.indexOf(r))>po&&fo.splice(i,1),a.effect.dirty=!0,a.update()}else t.el=e.el,a.vnode=t},z=(e,t,r,i,a,o,s)=>{var l=()=>{if(e.isMounted){var{next:u,bu:c,u:h,parent:f,vnode:p}=e,g=il(e);if(g)return u&&(u.el=p.el,N(e,u,s)),void g.asyncDep.then((()=>{e.isUnmounted||l()}));var m,_=u;nl(e,!1),u?(u.el=p.el,N(e,u,s)):u=p,c&&zn(c),(m=u.props&&u.props.onVnodeBeforeUpdate)&&kl(m,f,u,p),nl(e,!0);var y=Io(e),b=e.subTree;e.subTree=y,v(b,y,d(b.el),$(b),e,a,o),u.el=y.el,null===_&&function(e,t){for(var{vnode:n,parent:r}=e;r;){var i=r.subTree;if(i.suspense&&i.suspense.activeBranch===n&&(i.el=n.el),i!==n)break;(n=r.vnode).el=t,r=r.parent}}(e,y.el),h&&Qs(h,a),(m=u.props&&u.props.onVnodeUpdated)&&Qs((()=>kl(m,f,u,p)),a)}else{var w,{el:x,props:S}=t,{bm:k,m:C,parent:T}=e,A=Go(t);if(nl(e,!1),k&&zn(k),!A&&(w=S&&S.onVnodeBeforeMount)&&kl(w,T,t),nl(e,!0),x&&n){var M=()=>{e.subTree=Io(e),n(x,e.subTree,e,a,null)};A?t.type.__asyncLoader().then((()=>!e.isUnmounted&&M())):M()}else{var E=e.subTree=Io(e);v(null,E,r,i,e,a,o),t.el=E.el}if(C&&Qs(C,a),!A&&(w=S&&S.onVnodeMounted)){var O=t;Qs((()=>kl(w,T,O)),a)}(256&t.shapeFlag||T&&Go(T.vnode)&&256&T.vnode.shapeFlag)&&e.a&&Qs(e.a,a),e.isMounted=!0,t=r=i=null}},u=e.effect=new Ri(l,tn,(()=>wo(c)),e.scope),c=e.update=()=>{u.dirty&&u.run()};c.id=e.uid,nl(e,!0),c()},N=(e,t,n)=>{t.component=e;var r=e.vnode.props;e.vnode=t,e.next=null,function(e,t,n,r){var{props:i,attrs:a,vnode:{patchFlag:o}}=e,s=Ya(i),[l]=e.propsOptions,u=!1;if(!(r||o>0)||16&o){var c;for(var d in Rs(e,t,i,a)&&(u=!0),s)t&&(un(t,d)||(c=Mn(d))!==d&&un(t,c))||(l?!n||void 0===n[d]&&void 0===n[c]||(i[d]=Fs(l,s,d,void 0,e,!0)):delete i[d]);if(a!==s)for(var h in a)t&&un(t,h)||(delete a[h],u=!0)}else if(8&o)for(var f=e.vnode.dynamicProps,p=0;p<f.length;p++){var v=f[p];if(!Oo(e.emitsOptions,v)){var g=t[v];if(l)if(un(a,v))g!==a[v]&&(a[v]=g,u=!0);else{var m=Tn(v);i[m]=Fs(l,s,m,g,e,!1)}else g!==a[v]&&(a[v]=g,u=!0)}}u&&ra(e,"set","$attrs")}(e,t.props,r,n),Ks(e,t.children,n),Wi(),So(e),Ui()},I=function(e,t,n,r,i,a,o,s){var l=arguments.length>8&&void 0!==arguments[8]&&arguments[8],u=e&&e.children,d=e?e.shapeFlag:0,h=t.children,{patchFlag:f,shapeFlag:p}=t;if(f>0){if(128&f)return void D(u,h,n,r,i,a,o,s,l);if(256&f)return void P(u,h,n,r,i,a,o,s,l)}8&p?(16&d&&V(u,i,a),h!==u&&c(n,h)):16&d?16&p?D(u,h,n,r,i,a,o,s,l):V(u,i,a,!0):(8&d&&c(n,""),16&p&&k(h,n,r,i,a,o,s,l))},P=(e,t,n,r,i,a,o,s,l)=>{t=t||en;var u,c=(e=e||en).length,d=t.length,h=Math.min(c,d);for(u=0;u<h;u++){var f=t[u]=l?wl(t[u]):bl(t[u]);v(e[u],f,n,null,i,a,o,s,l)}c>d?V(e,i,a,!0,!1,h):k(t,n,r,i,a,o,s,l,h)},D=(e,t,n,r,i,a,o,s,l)=>{for(var u=0,c=t.length,d=e.length-1,h=c-1;u<=d&&u<=h;){var f=e[u],p=t[u]=l?wl(t[u]):bl(t[u]);if(!fl(f,p))break;v(f,p,n,null,i,a,o,s,l),u++}for(;u<=d&&u<=h;){var g=e[d],m=t[h]=l?wl(t[h]):bl(t[h]);if(!fl(g,m))break;v(g,m,n,null,i,a,o,s,l),d--,h--}if(u>d){if(u<=h)for(var _=h+1,y=_<c?t[_].el:r;u<=h;)v(null,t[u]=l?wl(t[u]):bl(t[u]),n,y,i,a,o,s,l),u++}else if(u>h)for(;u<=d;)R(e[u],i,a,!0),u++;else{var b,w=u,x=u,S=new Map;for(u=x;u<=h;u++){var k=t[u]=l?wl(t[u]):bl(t[u]);null!=k.key&&S.set(k.key,u)}var C=0,T=h-x+1,A=!1,M=0,E=new Array(T);for(u=0;u<T;u++)E[u]=0;for(u=w;u<=d;u++){var O=e[u];if(C>=T)R(O,i,a,!0);else{var L=void 0;if(null!=O.key)L=S.get(O.key);else for(b=x;b<=h;b++)if(0===E[b-x]&&fl(O,t[b])){L=b;break}void 0===L?R(O,i,a,!0):(E[L-x]=u+1,L>=M?M=L:A=!0,v(O,t[L],n,null,i,a,o,s,l),C++)}}var z=A?function(e){var t,n,r,i,a,o=e.slice(),s=[0],l=e.length;for(t=0;t<l;t++){var u=e[t];if(0!==u){if(e[n=s[s.length-1]]<u){o[t]=n,s.push(t);continue}for(r=0,i=s.length-1;r<i;)e[s[a=r+i>>1]]<u?r=a+1:i=a;u<e[s[r]]&&(r>0&&(o[t]=s[r-1]),s[r]=t)}}r=s.length,i=s[r-1];for(;r-- >0;)s[r]=i,i=o[i];return s}(E):en;for(b=z.length-1,u=T-1;u>=0;u--){var N=x+u,I=t[N],P=N+1<c?t[N+1].el:r;0===E[u]?v(null,I,n,P,i,a,o,s,l):A&&(b<0||u!==z[b]?B(I,n,P,2):b--)}}},B=function(e,t,n,i){var a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:null,{el:o,type:s,transition:l,children:u,shapeFlag:c}=e;if(6&c)B(e.component.subTree,t,n,i);else if(128&c)e.suspense.move(t,n,i);else if(64&c)s.move(e,t,n,U);else if(s!==al){if(s!==ll)if(2!==i&&1&c&&l)if(0===i)l.beforeEnter(o),r(o,t,n),Qs((()=>l.enter(o)),a);else{var{leave:d,delayLeave:h,afterLeave:f}=l,p=()=>r(o,t,n),v=()=>{d(o,(()=>{p(),f&&f()}))};h?h(o,p,v):v()}else r(o,t,n);else y(e,t,n)}else{r(o,t,n);for(var g=0;g<u.length;g++)B(u[g],t,n,i);r(e.anchor,t,n)}},R=function(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]&&arguments[3],i=arguments.length>4&&void 0!==arguments[4]&&arguments[4],{type:a,props:o,ref:s,children:l,dynamicChildren:u,shapeFlag:c,patchFlag:d,dirs:h}=e;if(null!=s&&Js(s,null,n,e,!0),256&c)t.ctx.deactivate(e);else{var f,p=1&c&&h,v=!Go(e);if(v&&(f=o&&o.onVnodeBeforeUnmount)&&kl(f,t,e),6&c)j(e.component,n,r);else{if(128&c)return void e.suspense.unmount(n,r);p&&Xo(e,null,t,"beforeUnmount"),64&c?e.type.remove(e,t,n,i,U,r):u&&(a!==al||d>0&&64&d)?V(u,t,n,!1,!0):(a===al&&384&d||!i&&16&c)&&V(l,t,n),r&&F(e)}(v&&(f=o&&o.onVnodeUnmounted)||p)&&Qs((()=>{f&&kl(f,t,e),p&&Xo(e,null,t,"unmounted")}),n)}},F=e=>{var{type:t,el:n,anchor:r,transition:a}=e;if(t!==al)if(t!==ll){var o=()=>{i(n),a&&!a.persisted&&a.afterLeave&&a.afterLeave()};if(1&e.shapeFlag&&a&&!a.persisted){var{leave:s,delayLeave:l}=a,u=()=>s(n,o);l?l(e.el,o,u):u()}else o()}else b(e);else q(n,r)},q=(e,t)=>{for(var n;e!==t;)n=h(e),i(e),e=n;i(t)},j=(e,t,n)=>{var{bum:r,scope:i,update:a,subTree:o,um:s}=e;r&&zn(r),i.stop(),a&&(a.active=!1,R(o,e,t,n)),s&&Qs(s,t),Qs((()=>{e.isUnmounted=!0}),t),t&&t.pendingBranch&&!t.isUnmounted&&e.asyncDep&&!e.asyncResolved&&e.suspenseId===t.pendingId&&(t.deps--,0===t.deps&&t.resolve())},V=function(e,t,n){for(var r=arguments.length>3&&void 0!==arguments[3]&&arguments[3],i=arguments.length>4&&void 0!==arguments[4]&&arguments[4],a=arguments.length>5&&void 0!==arguments[5]?arguments[5]:0;a<e.length;a++)R(e[a],t,n,r,i)},$=e=>6&e.shapeFlag?$(e.component.subTree):128&e.shapeFlag?e.suspense.next():h(e.anchor||e.el),H=!1,W=(e,t,n)=>{if(null==e)t._vnode&&R(t._vnode,null,null,!0);else{var r=t.__vueParent;v(t._vnode||null,e,t,null,r,null,n)}H||(H=!0,So(),H=!1),t._vnode=e},U={p:v,um:R,m:B,r:F,mt:O,mc:k,pc:I,pbc:T,n:$,o:e};return{render:W,hydrate:t,createApp:Ns(W,t)}}(e)}function tl(e,t){var{type:n,props:r}=e;return"svg"===t&&"foreignObject"===n||"mathml"===t&&"annotation-xml"===n&&r&&r.encoding&&r.encoding.includes("html")?void 0:t}function nl(e,t){var{effect:n,update:r}=e;n.allowRecurse=r.allowRecurse=t}function rl(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=e.children,i=t.children;if(cn(r)&&cn(i))for(var a=0;a<r.length;a++){var o=r[a],s=i[a];1&s.shapeFlag&&!s.dynamicChildren&&((s.patchFlag<=0||32===s.patchFlag)&&((s=i[a]=wl(i[a])).el=o.el),n||rl(o,s)),s.type===ol&&(s.el=o.el)}}function il(e){var t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:il(t)}var al=Symbol.for("v-fgt"),ol=Symbol.for("v-txt"),sl=Symbol.for("v-cmt"),ll=Symbol.for("v-stc"),ul=null,cl=1;function dl(e){cl+=e}function hl(e){return!!e&&!0===e.__v_isVNode}function fl(e,t){return e.type===t.type&&e.key===t.key}var pl="__vInternal",vl=e=>{var{key:t}=e;return null!=t?t:null},gl=e=>{var{ref:t,ref_key:n,ref_for:r}=e;return"number"==typeof t&&(t=""+t),null!=t?pn(t)||eo(t)||fn(t)?{i:Lo,r:t,k:n,f:!!r}:t:null};var ml=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:null,a=arguments.length>5&&void 0!==arguments[5]&&arguments[5];e&&e!==Ro||(e=sl);if(hl(e)){var o=_l(e,t,!0);return n&&xl(o,n),cl>0&&!a&&ul&&(6&o.shapeFlag?ul[ul.indexOf(e)]=o:ul.push(o)),o.patchFlag|=-2,o}s=e,fn(s)&&"__vccOpts"in s&&(e=e.__vccOpts);var s;if(t){t=function(e){return e?Ua(e)||pl in e?on({},e):e:null}(t);var{class:l,style:u}=t;l&&!pn(l)&&(t.class=jn(l)),gn(u)&&(Ua(u)&&!cn(u)&&(u=on({},u)),t.style=Dn(u))}var c=pn(e)?1:(e=>e.__isSuspense)(e)?128:(e=>e.__isTeleport)(e)?64:gn(e)?4:fn(e)?2:0;return function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:null,a=arguments.length>5&&void 0!==arguments[5]?arguments[5]:e===al?0:1,o=arguments.length>6&&void 0!==arguments[6]&&arguments[6],s=arguments.length>7&&void 0!==arguments[7]&&arguments[7],l={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&vl(t),ref:t&&gl(t),scopeId:zo,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetAnchor:null,staticCount:0,shapeFlag:a,patchFlag:r,dynamicProps:i,dynamicChildren:null,appContext:null,ctx:Lo};return s?(xl(l,n),128&a&&e.normalize(l)):n&&(l.shapeFlag|=pn(n)?8:16),cl>0&&!o&&ul&&(l.patchFlag>0||6&a)&&32!==l.patchFlag&&ul.push(l),l}(e,t,n,r,i,c,a,!0)};function _l(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],{props:r,ref:i,patchFlag:a,children:o}=e,s=t?Sl(r||{},t):r;return{__v_isVNode:!0,__v_skip:!0,type:e.type,props:s,key:s&&vl(s),ref:t&&t.ref?n&&i?cn(i)?i.concat(gl(t)):[i,gl(t)]:gl(t):i,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:o,target:e.target,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==al?-1===a?16:16|a:a,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:e.transition,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&_l(e.ssContent),ssFallback:e.ssFallback&&_l(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce}}function yl(){return ml(ol,null,arguments.length>0&&void 0!==arguments[0]?arguments[0]:" ",arguments.length>1&&void 0!==arguments[1]?arguments[1]:0)}function bl(e){return null==e||"boolean"==typeof e?ml(sl):cn(e)?ml(al,null,e.slice()):"object"==typeof e?wl(e):ml(ol,null,String(e))}function wl(e){return null===e.el&&-1!==e.patchFlag||e.memo?e:_l(e)}function xl(e,t){var n=0,{shapeFlag:r}=e;if(null==t)t=null;else if(cn(t))n=16;else if("object"==typeof t){if(65&r){var i=t.default;return void(i&&(i._c&&(i._d=!1),xl(e,i()),i._c&&(i._d=!0)))}n=32;var a=t._;a||pl in t?3===a&&Lo&&(1===Lo.slots._?t._=1:(t._=2,e.patchFlag|=1024)):t._ctx=Lo}else fn(t)?(t={default:t,_ctx:Lo},n=32):(t=String(t),64&r?(n=16,t=[yl(t)]):n=8);e.children=t,e.shapeFlag|=n}function Sl(){for(var e={},t=0;t<arguments.length;t++){var n=t<0||arguments.length<=t?void 0:arguments[t];for(var r in n)if("class"===r)e.class!==n.class&&(e.class=jn([e.class,n.class]));else if("style"===r)e.style=Dn([e.style,n.style]);else if(rn(r)){var i=e[r],a=n[r];!a||i===a||cn(i)&&i.includes(a)||(e[r]=i?[].concat(i,a):a)}else""!==r&&(e[r]=n[r])}return e}function kl(e,t,n){lo(e,t,7,[n,arguments.length>3&&void 0!==arguments[3]?arguments[3]:null])}var Cl=Ls(),Tl=0;var Al,Ml,El=null,Ol=()=>El||Lo,Ll=Pn(),zl=(e,t)=>{var n;return(n=Ll[e])||(n=Ll[e]=[]),n.push(t),e=>{n.length>1?n.forEach((t=>t(e))):n[0](e)}};Al=zl("__VUE_INSTANCE_SETTERS__",(e=>El=e)),Ml=zl("__VUE_SSR_SETTERS__",(e=>Bl=e));var Nl=e=>{var t=El;return Al(e),e.scope.on(),()=>{e.scope.off(),Al(t)}},Il=()=>{El&&El.scope.off(),Al(null)};function Pl(e){return 4&e.vnode.shapeFlag}var Dl,Bl=!1;function Rl(e,t,n){fn(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:gn(t)&&(e.setupState=oo(t)),Fl(e,n)}function Fl(e,t,n){var r=e.type;if(!e.render){if(!t&&Dl&&!r.render){var i=r.template||Ss(e).template;if(i){var{isCustomElement:a,compilerOptions:o}=e.appContext.config,{delimiters:s,compilerOptions:l}=r,u=on(on({isCustomElement:a,delimiters:s},o),l);r.render=Dl(i,u)}}e.render=r.render||tn}var c=Nl(e);Wi();try{bs(e)}finally{Ui(),c()}}function ql(e){if(e.exposed)return e.exposeProxy||(e.exposeProxy=new Proxy(oo(Xa(e.exposed)),{get:(t,n)=>n in t?t[n]:n in vs?vs[n](e):void 0,has:(e,t)=>t in e||t in vs}))}var jl=(e,t)=>{var n=function(e,t){var n,r,i=arguments.length>2&&void 0!==arguments[2]&&arguments[2],a=fn(e);return a?(n=e,r=tn):(n=e.get,r=e.set),new Ka(n,r,a||!r,i)}(e,t,Bl);return n};function Vl(e,t,n){var r=arguments.length;return 2===r?gn(t)&&!cn(t)?hl(t)?ml(e,null,[t]):ml(e,t):ml(e,null,t):(r>3?n=Array.prototype.slice.call(arguments,2):3===r&&hl(n)&&(n=[n]),ml(e,t,n))}var $l="3.4.21",Hl="undefined"!=typeof document?document:null,Wl=Hl&&Hl.createElement("template"),Ul={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{var t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,r)=>{var i="svg"===t?Hl.createElementNS("http://www.w3.org/2000/svg",e):"mathml"===t?Hl.createElementNS("http://www.w3.org/1998/Math/MathML",e):Hl.createElement(e,n?{is:n}:void 0);return"select"===e&&r&&null!=r.multiple&&i.setAttribute("multiple",r.multiple),i},createText:e=>Hl.createTextNode(e),createComment:e=>Hl.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>Hl.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,r,i,a){var o=n?n.previousSibling:t.lastChild;if(i&&(i===a||i.nextSibling))for(;t.insertBefore(i.cloneNode(!0),n),i!==a&&(i=i.nextSibling););else{Wl.innerHTML="svg"===r?"<svg>".concat(e,"</svg>"):"mathml"===r?"<math>".concat(e,"</math>"):e;var s=Wl.content;if("svg"===r||"mathml"===r){for(var l=s.firstChild;l.firstChild;)s.appendChild(l.firstChild);s.removeChild(l)}t.insertBefore(s,n)}return[o?o.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},Yl=Symbol("_vtc");var Xl=Symbol("_vod"),Zl=Symbol("_vsh"),Gl={beforeMount(e,t,n){var{value:r}=t,{transition:i}=n;e[Xl]="none"===e.style.display?"":e.style.display,i&&r?i.beforeEnter(e):Kl(e,r)},mounted(e,t,n){var{value:r}=t,{transition:i}=n;i&&r&&i.enter(e)},updated(e,t,n){var{value:r,oldValue:i}=t,{transition:a}=n;!r!=!i&&(a?r?(a.beforeEnter(e),Kl(e,!0),a.enter(e)):a.leave(e,(()=>{Kl(e,!1)})):Kl(e,r))},beforeUnmount(e,t){var{value:n}=t;Kl(e,n)}};function Kl(e,t){e.style.display=t?e[Xl]:"none",e[Zl]=!t}var Jl=Symbol(""),Ql=/(^|;)\s*display\s*:/;var eu=/\s*!important$/;function tu(e,t,n){if(cn(n))n.forEach((n=>tu(e,t,n)));else if(null==n&&(n=""),n=normalizeStyleValue(n),t.startsWith("--"))e.setProperty(t,n);else{var r=normalizeStyleName(e,t);eu.test(n)?e.setProperty(Mn(r),n.replace(eu,""),"important"):e[r]=n}}var nu="http://www.w3.org/1999/xlink";var ru=Symbol("_vei");function iu(e,t,n,r){var i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:null,a=e[ru]||(e[ru]={}),o=a[t];if(r&&o)o.value=r;else{var[s,l]=function(e){var t;if(au.test(e)){var n;for(t={};n=e.match(au);)e=e.slice(0,e.length-n[0].length),t[n[0].toLowerCase()]=!0}var r=":"===e[2]?e.slice(3):Mn(e.slice(2));return[r,t]}(t);if(r){var u=a[t]=function(e,t){var n=e=>{if(e._vts){if(e._vts<=n.attached)return}else e._vts=Date.now();lo(function(e,t){if(cn(t)){var n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map((e=>t=>!t._stopped&&e&&e(t)))}return t}(e,n.value),t,5,[e])};return n.value=e,n.attached=lu(),n}(r,i);!function(e,t,n,r){e.addEventListener(t,n,r)}(e,s,u,l)}else o&&(!function(e,t,n,r){e.removeEventListener(t,n,r)}(e,s,o,l),a[t]=void 0)}}var au=/(?:Once|Passive|Capture)$/;var ou=0,su=Promise.resolve(),lu=()=>ou||(su.then((()=>ou=0)),ou=Date.now());var uu=e=>111===e.charCodeAt(0)&&110===e.charCodeAt(1)&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123;var cu,du=["ctrl","shift","alt","meta"],hu={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&0!==e.button,middle:e=>"button"in e&&1!==e.button,right:e=>"button"in e&&2!==e.button,exact:(e,t)=>du.some((n=>e["".concat(n,"Key")]&&!t.includes(n)))},fu=(e,t)=>{var n=e._withMods||(e._withMods={}),r=t.join(".");return n[r]||(n[r]=function(n){for(var r=0;r<t.length;r++){var i=hu[t[r]];if(i&&i(n,t))return}for(var a=arguments.length,o=new Array(a>1?a-1:0),s=1;s<a;s++)o[s-1]=arguments[s];return e(n,...o)})},pu=on({patchProp:(e,t,n,r,i,a,o,s,l)=>{var u="svg"===i;"class"===t?function(e,t,n){var r=e[Yl];r&&(t=(t?[t,...r]:[...r]).join(" ")),null==t?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}(e,r,u):"style"===t?function(e,t,n){var r=e.style,i=pn(n),a=!1;if(n&&!i){if(t)if(pn(t))for(var o of t.split(";")){var s=o.slice(0,o.indexOf(":")).trim();null==n[s]&&tu(r,s,"")}else for(var l in t)null==n[l]&&tu(r,l,"");for(var u in n)"display"===u&&(a=!0),tu(r,u,n[u])}else if(i){if(t!==n){var c=r[Jl];c&&(n+=";"+c),r.cssText=normalizeStyleValue(n),a=Ql.test(n)}}else t&&e.removeAttribute("style");Xl in e&&(e[Xl]=a?r.display:"",e[Zl]&&(r.display="none"))}(e,n,r):rn(t)?an(t)||iu(e,t,n,r,o):("."===t[0]?(t=t.slice(1),1):"^"===t[0]?(t=t.slice(1),0):function(e,t,n,r){if(r)return"innerHTML"===t||"textContent"===t||!!(t in e&&uu(t)&&fn(n));if("spellcheck"===t||"draggable"===t||"translate"===t)return!1;if("form"===t)return!1;if("list"===t&&"INPUT"===e.tagName)return!1;if("type"===t&&"TEXTAREA"===e.tagName)return!1;if("width"===t||"height"===t){var i=e.tagName;if("IMG"===i||"VIDEO"===i||"CANVAS"===i||"SOURCE"===i)return!1}if(uu(t)&&pn(n))return!1;return t in e}(e,t,r,u))?function(e,t,n,r,i,a,o){if("innerHTML"===t||"textContent"===t)return r&&o(r,i,a),void(e[t]=null==n?"":n);var s=e.tagName;if("value"===t&&"PROGRESS"!==s&&!s.includes("-")){var l=null==n?"":n;return("OPTION"===s?e.getAttribute("value")||"":e.value)===l&&"_value"in e||(e.value=l),null==n&&e.removeAttribute(t),void(e._value=n)}var u=!1;if(""===n||null==n){var c=typeof e[t];"boolean"===c?n=Wn(n):null==n&&"string"===c?(n="",u=!0):"number"===c&&(n=0,u=!0)}try{e[t]=n}catch(d){}u&&e.removeAttribute(t)}(e,t,r,a,o,s,l):("true-value"===t?e._trueValue=r:"false-value"===t&&(e._falseValue=r),function(e,t,n,r){if(r&&t.startsWith("xlink:"))null==n?e.removeAttributeNS(nu,t.slice(6,t.length)):e.setAttributeNS(nu,t,n);else{var i=Hn(t);null==n||i&&!Wn(n)?e.removeAttribute(t):e.setAttribute(t,i?"":n)}}(e,t,r,u))}},Ul);var vu=function(){var e=(cu||(cu=el(pu))).createApp(...arguments),{mount:t}=e;return e.mount=n=>{var r=function(e){if(pn(e)){return document.querySelector(e)}return e}(n);if(r){var i=e._component;fn(i)||i.render||i.template||(i.template=r.innerHTML),r.innerHTML="";var a=t(r,!1,function(e){if(e instanceof SVGElement)return"svg";if("function"==typeof MathMLElement&&e instanceof MathMLElement)return"mathml"}(r));return r instanceof Element&&(r.removeAttribute("v-cloak"),r.setAttribute("data-v-app","")),a}},e};var gu,mu,_u=["top","left","right","bottom"],yu={};function bu(){return mu="CSS"in window&&"function"==typeof CSS.supports?CSS.supports("top: env(safe-area-inset-top)")?"env":CSS.supports("top: constant(safe-area-inset-top)")?"constant":"":""}function wu(){if(mu="string"==typeof mu?mu:bu()){var e=[],t=!1;try{var n=Object.defineProperty({},"passive",{get:function(){t={passive:!0}}});window.addEventListener("test",null,n)}catch(s){}var r=document.createElement("div");i(r,{position:"absolute",left:"0",top:"0",width:"0",height:"0",zIndex:"-1",overflow:"hidden",visibility:"hidden"}),_u.forEach((function(e){o(r,e)})),document.body.appendChild(r),a(),gu=!0}else _u.forEach((function(e){yu[e]=0}));function i(e,t){var n=e.style;Object.keys(t).forEach((function(e){var r=t[e];n[e]=r}))}function a(t){t?e.push(t):e.forEach((function(e){e()}))}function o(e,n){var r=document.createElement("div"),o=document.createElement("div"),s=document.createElement("div"),l=document.createElement("div"),u={position:"absolute",width:"100px",height:"200px",boxSizing:"border-box",overflow:"hidden",paddingBottom:mu+"(safe-area-inset-"+n+")"};i(r,u),i(o,u),i(s,{transition:"0s",animation:"none",width:"400px",height:"400px"}),i(l,{transition:"0s",animation:"none",width:"250%",height:"250%"}),r.appendChild(s),o.appendChild(l),e.appendChild(r),e.appendChild(o),a((function(){r.scrollTop=o.scrollTop=1e4;var e=r.scrollTop,i=o.scrollTop;function a(){this.scrollTop!==(this===r?e:i)&&(r.scrollTop=o.scrollTop=1e4,e=r.scrollTop,i=o.scrollTop,function(e){Su.length||setTimeout((function(){var e={};Su.forEach((function(t){e[t]=yu[t]})),Su.length=0,ku.forEach((function(t){t(e)}))}),0);Su.push(e)}(n))}r.addEventListener("scroll",a,t),o.addEventListener("scroll",a,t)}));var c=getComputedStyle(r);Object.defineProperty(yu,n,{configurable:!0,get:function(){return parseFloat(c.paddingBottom)}})}}function xu(e){return gu||wu(),yu[e]}var Su=[];var ku=[];const Cu=e({get support(){return 0!=("string"==typeof mu?mu:bu()).length},get top(){return xu("top")},get left(){return xu("left")},get right(){return xu("right")},get bottom(){return xu("bottom")},onChange:function(e){bu()&&(gu||wu(),"function"==typeof e&&ku.push(e))},offChange:function(e){var t=ku.indexOf(e);t>=0&&ku.splice(t,1)}});var Tu=fu((()=>{}),["prevent"]);function Au(e,t){return parseInt((e.getPropertyValue(t).match(/\d+/)||["0"])[0])}function Mu(){var e=Au(document.documentElement.style,"--window-top");return e?e+Cu.top:0}function Eu(e){return Symbol(e)}function Ou(e){return-1!==(e+="").indexOf("rpx")||-1!==e.indexOf("upx")}function Lu(e){if(arguments.length>1&&void 0!==arguments[1]&&arguments[1])return function(e){if(!Ou(e))return e;return e.replace(/(\d+(\.\d+)?)[ru]px/g,((e,t)=>uni.upx2px(parseFloat(t))+"px"))}(e);if(pn(e)){var t=parseInt(e)||0;return Ou(e)?uni.upx2px(t):t}return e}var zu,Nu="M1.952 18.080q-0.32-0.352-0.416-0.88t0.128-0.976l0.16-0.352q0.224-0.416 0.64-0.528t0.8 0.176l6.496 4.704q0.384 0.288 0.912 0.272t0.88-0.336l17.312-14.272q0.352-0.288 0.848-0.256t0.848 0.352l-0.416-0.416q0.32 0.352 0.32 0.816t-0.32 0.816l-18.656 18.912q-0.32 0.352-0.8 0.352t-0.8-0.32l-7.936-8.064z",Iu="M31.562 4.9966666659375q0.435 0.399 0.435 0.87 0.036 0.58-0.399 0.98l-18.61 19.917q-0.145 0.145-0.327 0.217-0.073 0.037-0.145 0.11-0.254 0.035-0.472 0.035-0.29 0-0.544-0.036l-0.145-0.072q-0.109-0.073-0.217-0.182l-0.11-0.072L0.363 16.2786666659375q-0.327-0.399-0.363-0.907 0-0.544 0.363-1.016 0.435-0.326 0.961-0.362 0.527-0.036 0.962 0.362l9.722 9.542L29.712 5.0326666659375q0.399-0.363 0.943-0.363 0.544-0.036 0.907 0.327z";function Pu(e){var t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:27;return ml("svg",{width:t,height:t,viewBox:"0 0 32 32"},[ml("path",{d:e,fill:arguments.length>1&&void 0!==arguments[1]?arguments[1]:"#000"},null,8,["d","fill"])],8,["width","height"])}function Du(){return Bu()}function Bu(){return window.__id__||(window.__id__=plus.webview.currentWebview().id),parseInt(window.__id__)}function Ru(e){e.preventDefault()}var Fu,qu,ju,Vu,$u,Hu=0;function Wu(e){var{onPageScroll:t,onReachBottom:n,onReachBottomDistance:r}=e,i=!1,a=!1,o=!0,s=()=>{function e(){if((()=>{var{scrollHeight:e}=document.documentElement,t=window.innerHeight,n=window.scrollY,i=n>0&&e>t&&n+t+r>=e,o=Math.abs(e-Hu)>r;return!i||a&&!o?(!i&&a&&(a=!1),!1):(Hu=e,a=!0,!0)})())return n&&n(),o=!1,setTimeout((function(){o=!0}),350),!0}t&&t(window.pageYOffset),n&&o&&(e()||(zu=setTimeout(e,300))),i=!1};return function(){clearTimeout(zu),i||requestAnimationFrame(s),i=!0}}function Uu(e,t){if(0===t.indexOf("/"))return t;if(0===t.indexOf("./"))return Uu(e,t.slice(2));for(var n=t.split("/"),r=n.length,i=0;i<r&&".."===n[i];i++);n.splice(0,i),t=n.join("/");var a=e.length>0?e.split("/"):[];return a.splice(a.length-i-1,i+1),cr(a.concat(n).join("/"))}function Yu(){return"object"==typeof window&&"object"==typeof navigator&&"object"==typeof document?"webview":"v8"}function Xu(){return Fu.webview.currentWebview().id}var Zu={};function Gu(e){var t=e.data&&e.data.__message;if(t&&t.__page){var n=t.__page,r=Zu[n];r&&r(t),t.keep||delete Zu[n]}}class Ku{constructor(e){this.webview=e}sendMessage(e){var t=JSON.parse(JSON.stringify({__message:{data:e}})),n=this.webview.id;ju?new ju(n).postMessage(t):Fu.webview.postMessageToUniNView&&Fu.webview.postMessageToUniNView(t,n)}close(){this.webview.close()}}function Ju(e){var{context:t={},url:n,data:r={},style:i={},onMessage:a,onClose:o}=e,s=__uniConfig.darkmode;Fu=t.plus||plus,qu=t.weex||("object"==typeof weex?weex:null),ju=t.BroadcastChannel||("object"==typeof BroadcastChannel?BroadcastChannel:null);var l="page".concat(Date.now());!1!==(i=on({},i)).titleNView&&"none"!==i.titleNView&&(i.titleNView=on({autoBackButton:!0,titleSize:"17px"},i.titleNView));var u={top:0,bottom:0,usingComponents:{},popGesture:"close",scrollIndicator:"none",animationType:"pop-in",animationDuration:200,uniNView:{path:"/".concat(n,".js"),defaultFontSize:16,viewport:Fu.screen.resolutionWidth}};i=on(u,i);var c=Fu.webview.create("",l,i,{extras:{from:Xu(),runtime:Yu(),data:on({},r,{darkmode:s}),useGlobalEvent:!ju}});return c.addEventListener("close",o),function(e,t){"v8"===Yu()?ju?(Vu&&Vu.close(),(Vu=new ju(Xu())).onmessage=Gu):$u||($u=qu.requireModule("globalEvent")).addEventListener("plusMessage",Gu):window.__plusMessage=Gu,Zu[e]=t}(l,(e=>{fn(a)&&a(e.data),e.keep||c.close("auto")})),c.show(i.animationType,i.animationDuration),new Ku(c)}class Qu{constructor(e){this.$bindClass=!1,this.$bindStyle=!1,this.$vm=e,this.$el=e.$el,this.$el.getAttribute&&(this.$bindClass=!!this.$el.getAttribute("class"),this.$bindStyle=!!this.$el.getAttribute("style"))}selectComponent(e){if(this.$el&&e){var t=nc(this.$el.querySelector(e));if(t)return ec(t)}}selectAllComponents(e){if(!this.$el||!e)return[];for(var t=[],n=this.$el.querySelectorAll(e),r=0;r<n.length;r++){var i=nc(n[r]);i&&t.push(ec(i))}return t}forceUpdate(e){"class"===e?this.$bindClass?(this.$el.__wxsClassChanged=!0,this.$vm.$forceUpdate()):this.updateWxsClass():"style"===e&&(this.$bindStyle?(this.$el.__wxsStyleChanged=!0,this.$vm.$forceUpdate()):this.updateWxsStyle())}updateWxsClass(){var{__wxsAddClass:e}=this.$el;e.length&&(this.$el.className=e.join(" "))}updateWxsStyle(){var{__wxsStyle:e}=this.$el;e&&this.$el.setAttribute("style",function(e){var t="";if(!e||pn(e))return t;for(var n in e){var r=e[n],i=n.startsWith("--")?n:Mn(n);(pn(r)||"number"==typeof r)&&(t+="".concat(i,":").concat(r,";"))}return t}(e))}setStyle(e){return this.$el&&e?(pn(e)&&(e=qn(e)),wn(e)&&(this.$el.__wxsStyle=e,this.forceUpdate("style")),this):this}addClass(e){if(!this.$el||!e)return this;var t=this.$el.__wxsAddClass||(this.$el.__wxsAddClass=[]);return-1===t.indexOf(e)&&(t.push(e),this.forceUpdate("class")),this}removeClass(e){if(!this.$el||!e)return this;var{__wxsAddClass:t}=this.$el;if(t){var n=t.indexOf(e);n>-1&&t.splice(n,1)}var r=this.$el.__wxsRemoveClass||(this.$el.__wxsRemoveClass=[]);return-1===r.indexOf(e)&&(r.push(e),this.forceUpdate("class")),this}hasClass(e){return this.$el&&this.$el.classList.contains(e)}getDataset(){return this.$el&&this.$el.dataset}callMethod(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=this.$vm[e];fn(n)?n(JSON.parse(JSON.stringify(t))):this.$vm.ownerId&&UniViewJSBridge.publishHandler("onWxsInvokeCallMethod",{nodeId:this.$el.__id,ownerId:this.$vm.ownerId,method:e,args:t})}requestAnimationFrame(e){return window.requestAnimationFrame(e)}getState(){return this.$el&&(this.$el.__wxsState||(this.$el.__wxsState={}))}triggerEvent(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return this.$vm.$emit(e,t),this}getComputedStyle(e){if(this.$el){var t=window.getComputedStyle(this.$el);return e&&e.length?e.reduce(((e,n)=>(e[n]=t[n],e)),{}):t}return{}}setTimeout(e,t){return window.setTimeout(e,t)}clearTimeout(e){return window.clearTimeout(e)}getBoundingClientRect(){return this.$el.getBoundingClientRect()}}function ec(e){if(e&&e.$el)return e.$el.__wxsComponentDescriptor||(e.$el.__wxsComponentDescriptor=new Qu(e)),e.$el.__wxsComponentDescriptor}function tc(e,t){return ec(e)}function nc(e){if(e)return rc(e)}function rc(e){return e.__wxsVm||(e.__wxsVm={ownerId:e.__ownerId,$el:e,$emit(){},$forceUpdate(){var t,n,{__wxsStyle:r,__wxsAddClass:i,__wxsRemoveClass:a,__wxsStyleChanged:o,__wxsClassChanged:s}=e;o&&(e.__wxsStyleChanged=!1,r&&(n=()=>{Object.keys(r).forEach((t=>{e.style[t]=r[t]}))})),s&&(e.__wxsClassChanged=!1,t=()=>{a&&a.forEach((t=>{e.classList.remove(t)})),i&&i.forEach((t=>{e.classList.add(t)}))}),requestAnimationFrame((()=>{t&&t(),n&&n()}))}})}var ic=e=>!e.type.indexOf("key")&&e instanceof KeyboardEvent,ac=e=>"click"===e.type,oc=e=>0===e.type.indexOf("mouse")||["contextmenu"].includes(e.type),sc=e=>"undefined"!=typeof TouchEvent&&e instanceof TouchEvent||0===e.type.indexOf("touch")||["longpress"].indexOf(e.type)>=0;function lc(e,t,n){var{currentTarget:r}=e;if(!(e instanceof Event&&r instanceof HTMLElement))return[e];var i=0!==r.tagName.indexOf("UNI-"),a=uc(e,i);if(ac(e))!function(e,t){var{x:n,y:r}=t,i=Mu();e.detail={x:n,y:r-i},e.touches=e.changedTouches=[cc(t,i)]}(a,e);else if(oc(e))!function(e,t){var n=Mu();e.pageX=t.pageX,e.pageY=t.pageY-n,e.clientX=t.clientX,e.clientY=t.clientY-n,e.touches=e.changedTouches=[cc(t,n)]}(a,e);else if(sc(e)){var o=Mu();a.touches=dc(e.touches,o),a.changedTouches=dc(e.changedTouches,o)}else if(ic(e)){["key","code"].forEach((t=>{Object.defineProperty(a,t,{get:()=>e[t]})}))}return[a]}function uc(e){var t,n=arguments.length>1&&void 0!==arguments[1]&&arguments[1],{type:r,timeStamp:i,target:a,currentTarget:o}=e;t=br(n?a:function(e){for(;e&&0!==e.tagName.indexOf("UNI-");)e=e.parentElement;return e}(a));var s={type:r,timeStamp:i,target:t,detail:{},currentTarget:br(o)};return e._stopped&&(s._stopped=!0),e.type.startsWith("touch")&&(s.touches=e.touches,s.changedTouches=e.changedTouches),s}function cc(e,t){return{force:1,identifier:0,clientX:e.clientX,clientY:e.clientY-t,pageX:e.pageX,pageY:e.pageY-t}}function dc(e,t){for(var n=[],r=0;r<e.length;r++){var{identifier:i,pageX:a,pageY:o,clientX:s,clientY:l,force:u}=e[r];n.push({identifier:i,pageX:a,pageY:o-t,clientX:s,clientY:l-t,force:u||0})}return n}var hc="vdSync",fc="onWebviewReady",pc=0,vc="setLocale",gc=on(Si,{publishHandler:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=Bu()+"";plus.webview.postMessageToUniNView({type:"subscribeHandler",args:{type:e,data:t,pageId:n}},"__uniapp__service")}});function mc(e,t,n,r){return function(e,t){return function(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];var i=function(e,t){var n=function(e){e[0]}(t);if(n)return n}(0,n);if(i)throw new Error(i);return t.apply(null,n)}}(0,t)}function _c(){if("undefined"!=typeof __SYSTEM_INFO__)return window.__SYSTEM_INFO__;var{resolutionWidth:e}=plus.screen.getCurrentSize()||{resolutionWidth:0};return{platform:(plus.os.name||"").toLowerCase(),pixelRatio:plus.screen.scale,windowWidth:Math.round(e)}}function yc(e){if(0===e.indexOf("//"))return"https:"+e;if(tr.test(e)||nr.test(e))return e;if(function(e){if(0===e.indexOf("_www")||0===e.indexOf("_doc")||0===e.indexOf("_documents")||0===e.indexOf("_downloads"))return!0;return!1}(e))return"file://"+bc(e);var t="file://"+bc("_www");if(0===e.indexOf("/"))return e.startsWith("/storage/")||e.startsWith("/sdcard/")||e.includes("/Containers/Data/Application/")?"file://"+e:t+e;if(0===e.indexOf("../")||0===e.indexOf("./")){if("string"==typeof __id__)return t+Uu(cr(__id__),e);var n=window.__PAGE_INFO__;if(n)return t+Uu(cr(n.route),e)}return e}var bc=function(e){var t=Object.create(null);return n=>t[n]||(t[n]=e(n))}((e=>plus.io.convertLocalFileSystemURL(e).replace(/^\/?apps\//,"/android_asset/apps/").replace(/\/$/,"")));var wc=0;var xc="_doc/uniapp_temp";function Sc(e){return function(e){return new Promise((function(t,n){0===e.indexOf("http://")||0===e.indexOf("https://")?plus.downloader.createDownload(e,{filename:"".concat(xc,"/download/")},(function(e,r){200===r?t(e.filename):n(new Error("network fail"))})).start():t(e)}))}(e).then((function(e){var t,n=window;return n.webkit&&n.webkit.messageHandlers?(t=e,new Promise((function(e,n){function r(){var r=new plus.nativeObj.Bitmap("bitmap_".concat(Date.now(),"_").concat(Math.random(),"}"));r.load(t,(function(){e(r.toBase64Data()),r.clear()}),(function(e){r.clear(),n(e)}))}plus.io.resolveLocalFileSystemURL(t,(function(t){t.file((function(t){var n=new plus.io.FileReader;n.onload=function(t){e(t.target.result)},n.onerror=r,n.readAsDataURL(t)}),r)}),r)}))):plus.io.convertLocalFileSystemURL(e)}))}var kc={};!function(e){var t="undefined"!=typeof Uint8Array&&"undefined"!=typeof Uint16Array&&"undefined"!=typeof Int32Array;function n(e,t){return Object.prototype.hasOwnProperty.call(e,t)}e.assign=function(e){for(var t=Array.prototype.slice.call(arguments,1);t.length;){var r=t.shift();if(r){if("object"!=typeof r)throw new TypeError(r+"must be non-object");for(var i in r)n(r,i)&&(e[i]=r[i])}}return e},e.shrinkBuf=function(e,t){return e.length===t?e:e.subarray?e.subarray(0,t):(e.length=t,e)};var r={arraySet:function(e,t,n,r,i){if(t.subarray&&e.subarray)e.set(t.subarray(n,n+r),i);else for(var a=0;a<r;a++)e[i+a]=t[n+a]},flattenChunks:function(e){var t,n,r,i,a,o;for(r=0,t=0,n=e.length;t<n;t++)r+=e[t].length;for(o=new Uint8Array(r),i=0,t=0,n=e.length;t<n;t++)a=e[t],o.set(a,i),i+=a.length;return o}},i={arraySet:function(e,t,n,r,i){for(var a=0;a<r;a++)e[i+a]=t[n+a]},flattenChunks:function(e){return[].concat.apply([],e)}};e.setTyped=function(t){t?(e.Buf8=Uint8Array,e.Buf16=Uint16Array,e.Buf32=Int32Array,e.assign(e,r)):(e.Buf8=Array,e.Buf16=Array,e.Buf32=Array,e.assign(e,i))},e.setTyped(t)}(kc);var Cc={},Tc={},Ac={},Mc=kc,Ec=0,Oc=1;function Lc(e){for(var t=e.length;--t>=0;)e[t]=0}var zc=0,Nc=29,Ic=256,Pc=Ic+1+Nc,Dc=30,Bc=19,Rc=2*Pc+1,Fc=15,qc=16,jc=7,Vc=256,$c=16,Hc=17,Wc=18,Uc=[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0],Yc=[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13],Xc=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,2,3,7],Zc=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],Gc=new Array(2*(Pc+2));Lc(Gc);var Kc=new Array(2*Dc);Lc(Kc);var Jc=new Array(512);Lc(Jc);var Qc=new Array(256);Lc(Qc);var ed=new Array(Nc);Lc(ed);var td,nd,rd,id=new Array(Dc);function ad(e,t,n,r,i){this.static_tree=e,this.extra_bits=t,this.extra_base=n,this.elems=r,this.max_length=i,this.has_stree=e&&e.length}function od(e,t){this.dyn_tree=e,this.max_code=0,this.stat_desc=t}function sd(e){return e<256?Jc[e]:Jc[256+(e>>>7)]}function ld(e,t){e.pending_buf[e.pending++]=255&t,e.pending_buf[e.pending++]=t>>>8&255}function ud(e,t,n){e.bi_valid>qc-n?(e.bi_buf|=t<<e.bi_valid&65535,ld(e,e.bi_buf),e.bi_buf=t>>qc-e.bi_valid,e.bi_valid+=n-qc):(e.bi_buf|=t<<e.bi_valid&65535,e.bi_valid+=n)}function cd(e,t,n){ud(e,n[2*t],n[2*t+1])}function dd(e,t){var n=0;do{n|=1&e,e>>>=1,n<<=1}while(--t>0);return n>>>1}function hd(e,t,n){var r,i,a=new Array(Fc+1),o=0;for(r=1;r<=Fc;r++)a[r]=o=o+n[r-1]<<1;for(i=0;i<=t;i++){var s=e[2*i+1];0!==s&&(e[2*i]=dd(a[s]++,s))}}function fd(e){var t;for(t=0;t<Pc;t++)e.dyn_ltree[2*t]=0;for(t=0;t<Dc;t++)e.dyn_dtree[2*t]=0;for(t=0;t<Bc;t++)e.bl_tree[2*t]=0;e.dyn_ltree[2*Vc]=1,e.opt_len=e.static_len=0,e.last_lit=e.matches=0}function pd(e){e.bi_valid>8?ld(e,e.bi_buf):e.bi_valid>0&&(e.pending_buf[e.pending++]=e.bi_buf),e.bi_buf=0,e.bi_valid=0}function vd(e,t,n,r){var i=2*t,a=2*n;return e[i]<e[a]||e[i]===e[a]&&r[t]<=r[n]}function gd(e,t,n){for(var r=e.heap[n],i=n<<1;i<=e.heap_len&&(i<e.heap_len&&vd(t,e.heap[i+1],e.heap[i],e.depth)&&i++,!vd(t,r,e.heap[i],e.depth));)e.heap[n]=e.heap[i],n=i,i<<=1;e.heap[n]=r}function md(e,t,n){var r,i,a,o,s=0;if(0!==e.last_lit)do{r=e.pending_buf[e.d_buf+2*s]<<8|e.pending_buf[e.d_buf+2*s+1],i=e.pending_buf[e.l_buf+s],s++,0===r?cd(e,i,t):(cd(e,(a=Qc[i])+Ic+1,t),0!==(o=Uc[a])&&ud(e,i-=ed[a],o),cd(e,a=sd(--r),n),0!==(o=Yc[a])&&ud(e,r-=id[a],o))}while(s<e.last_lit);cd(e,Vc,t)}function _d(e,t){var n,r,i,a=t.dyn_tree,o=t.stat_desc.static_tree,s=t.stat_desc.has_stree,l=t.stat_desc.elems,u=-1;for(e.heap_len=0,e.heap_max=Rc,n=0;n<l;n++)0!==a[2*n]?(e.heap[++e.heap_len]=u=n,e.depth[n]=0):a[2*n+1]=0;for(;e.heap_len<2;)a[2*(i=e.heap[++e.heap_len]=u<2?++u:0)]=1,e.depth[i]=0,e.opt_len--,s&&(e.static_len-=o[2*i+1]);for(t.max_code=u,n=e.heap_len>>1;n>=1;n--)gd(e,a,n);i=l;do{n=e.heap[1],e.heap[1]=e.heap[e.heap_len--],gd(e,a,1),r=e.heap[1],e.heap[--e.heap_max]=n,e.heap[--e.heap_max]=r,a[2*i]=a[2*n]+a[2*r],e.depth[i]=(e.depth[n]>=e.depth[r]?e.depth[n]:e.depth[r])+1,a[2*n+1]=a[2*r+1]=i,e.heap[1]=i++,gd(e,a,1)}while(e.heap_len>=2);e.heap[--e.heap_max]=e.heap[1],function(e,t){var n,r,i,a,o,s,l=t.dyn_tree,u=t.max_code,c=t.stat_desc.static_tree,d=t.stat_desc.has_stree,h=t.stat_desc.extra_bits,f=t.stat_desc.extra_base,p=t.stat_desc.max_length,v=0;for(a=0;a<=Fc;a++)e.bl_count[a]=0;for(l[2*e.heap[e.heap_max]+1]=0,n=e.heap_max+1;n<Rc;n++)(a=l[2*l[2*(r=e.heap[n])+1]+1]+1)>p&&(a=p,v++),l[2*r+1]=a,r>u||(e.bl_count[a]++,o=0,r>=f&&(o=h[r-f]),s=l[2*r],e.opt_len+=s*(a+o),d&&(e.static_len+=s*(c[2*r+1]+o)));if(0!==v){do{for(a=p-1;0===e.bl_count[a];)a--;e.bl_count[a]--,e.bl_count[a+1]+=2,e.bl_count[p]--,v-=2}while(v>0);for(a=p;0!==a;a--)for(r=e.bl_count[a];0!==r;)(i=e.heap[--n])>u||(l[2*i+1]!==a&&(e.opt_len+=(a-l[2*i+1])*l[2*i],l[2*i+1]=a),r--)}}(e,t),hd(a,u,e.bl_count)}function yd(e,t,n){var r,i,a=-1,o=t[1],s=0,l=7,u=4;for(0===o&&(l=138,u=3),t[2*(n+1)+1]=65535,r=0;r<=n;r++)i=o,o=t[2*(r+1)+1],++s<l&&i===o||(s<u?e.bl_tree[2*i]+=s:0!==i?(i!==a&&e.bl_tree[2*i]++,e.bl_tree[2*$c]++):s<=10?e.bl_tree[2*Hc]++:e.bl_tree[2*Wc]++,s=0,a=i,0===o?(l=138,u=3):i===o?(l=6,u=3):(l=7,u=4))}function bd(e,t,n){var r,i,a=-1,o=t[1],s=0,l=7,u=4;for(0===o&&(l=138,u=3),r=0;r<=n;r++)if(i=o,o=t[2*(r+1)+1],!(++s<l&&i===o)){if(s<u)do{cd(e,i,e.bl_tree)}while(0!=--s);else 0!==i?(i!==a&&(cd(e,i,e.bl_tree),s--),cd(e,$c,e.bl_tree),ud(e,s-3,2)):s<=10?(cd(e,Hc,e.bl_tree),ud(e,s-3,3)):(cd(e,Wc,e.bl_tree),ud(e,s-11,7));s=0,a=i,0===o?(l=138,u=3):i===o?(l=6,u=3):(l=7,u=4)}}Lc(id);var wd=!1;function xd(e,t,n,r){ud(e,(zc<<1)+(r?1:0),3),function(e,t,n){pd(e),ld(e,n),ld(e,~n),Mc.arraySet(e.pending_buf,e.window,t,n,e.pending),e.pending+=n}(e,t,n)}Ac._tr_init=function(e){wd||(!function(){var e,t,n,r,i,a=new Array(Fc+1);for(n=0,r=0;r<Nc-1;r++)for(ed[r]=n,e=0;e<1<<Uc[r];e++)Qc[n++]=r;for(Qc[n-1]=r,i=0,r=0;r<16;r++)for(id[r]=i,e=0;e<1<<Yc[r];e++)Jc[i++]=r;for(i>>=7;r<Dc;r++)for(id[r]=i<<7,e=0;e<1<<Yc[r]-7;e++)Jc[256+i++]=r;for(t=0;t<=Fc;t++)a[t]=0;for(e=0;e<=143;)Gc[2*e+1]=8,e++,a[8]++;for(;e<=255;)Gc[2*e+1]=9,e++,a[9]++;for(;e<=279;)Gc[2*e+1]=7,e++,a[7]++;for(;e<=287;)Gc[2*e+1]=8,e++,a[8]++;for(hd(Gc,Pc+1,a),e=0;e<Dc;e++)Kc[2*e+1]=5,Kc[2*e]=dd(e,5);td=new ad(Gc,Uc,Ic+1,Pc,Fc),nd=new ad(Kc,Yc,0,Dc,Fc),rd=new ad(new Array(0),Xc,0,Bc,jc)}(),wd=!0),e.l_desc=new od(e.dyn_ltree,td),e.d_desc=new od(e.dyn_dtree,nd),e.bl_desc=new od(e.bl_tree,rd),e.bi_buf=0,e.bi_valid=0,fd(e)},Ac._tr_stored_block=xd,Ac._tr_flush_block=function(e,t,n,r){var i,a,o=0;e.level>0?(2===e.strm.data_type&&(e.strm.data_type=function(e){var t,n=4093624447;for(t=0;t<=31;t++,n>>>=1)if(1&n&&0!==e.dyn_ltree[2*t])return Ec;if(0!==e.dyn_ltree[18]||0!==e.dyn_ltree[20]||0!==e.dyn_ltree[26])return Oc;for(t=32;t<Ic;t++)if(0!==e.dyn_ltree[2*t])return Oc;return Ec}(e)),_d(e,e.l_desc),_d(e,e.d_desc),o=function(e){var t;for(yd(e,e.dyn_ltree,e.l_desc.max_code),yd(e,e.dyn_dtree,e.d_desc.max_code),_d(e,e.bl_desc),t=Bc-1;t>=3&&0===e.bl_tree[2*Zc[t]+1];t--);return e.opt_len+=3*(t+1)+5+5+4,t}(e),i=e.opt_len+3+7>>>3,(a=e.static_len+3+7>>>3)<=i&&(i=a)):i=a=n+5,n+4<=i&&-1!==t?xd(e,t,n,r):4===e.strategy||a===i?(ud(e,2+(r?1:0),3),md(e,Gc,Kc)):(ud(e,4+(r?1:0),3),function(e,t,n,r){var i;for(ud(e,t-257,5),ud(e,n-1,5),ud(e,r-4,4),i=0;i<r;i++)ud(e,e.bl_tree[2*Zc[i]+1],3);bd(e,e.dyn_ltree,t-1),bd(e,e.dyn_dtree,n-1)}(e,e.l_desc.max_code+1,e.d_desc.max_code+1,o+1),md(e,e.dyn_ltree,e.dyn_dtree)),fd(e),r&&pd(e)},Ac._tr_tally=function(e,t,n){return e.pending_buf[e.d_buf+2*e.last_lit]=t>>>8&255,e.pending_buf[e.d_buf+2*e.last_lit+1]=255&t,e.pending_buf[e.l_buf+e.last_lit]=255&n,e.last_lit++,0===t?e.dyn_ltree[2*n]++:(e.matches++,t--,e.dyn_ltree[2*(Qc[n]+Ic+1)]++,e.dyn_dtree[2*sd(t)]++),e.last_lit===e.lit_bufsize-1},Ac._tr_align=function(e){ud(e,2,3),cd(e,Vc,Gc),function(e){16===e.bi_valid?(ld(e,e.bi_buf),e.bi_buf=0,e.bi_valid=0):e.bi_valid>=8&&(e.pending_buf[e.pending++]=255&e.bi_buf,e.bi_buf>>=8,e.bi_valid-=8)}(e)};var Sd=function(e,t,n,r){for(var i=65535&e,a=e>>>16&65535,o=0;0!==n;){n-=o=n>2e3?2e3:n;do{a=a+(i=i+t[r++]|0)|0}while(--o);i%=65521,a%=65521}return i|a<<16};var kd=function(){for(var e,t=[],n=0;n<256;n++){e=n;for(var r=0;r<8;r++)e=1&e?3988292384^e>>>1:e>>>1;t[n]=e}return t}();var Cd,Td=function(e,t,n,r){var i=kd,a=r+n;e^=-1;for(var o=r;o<a;o++)e=e>>>8^i[255&(e^t[o])];return~e},Ad={2:"need dictionary",1:"stream end",0:"","-1":"file error","-2":"stream error","-3":"data error","-4":"insufficient memory","-5":"buffer error","-6":"incompatible version"},Md=kc,Ed=Ac,Od=Sd,Ld=Td,zd=Ad,Nd=0,Id=4,Pd=0,Dd=-2,Bd=-1,Rd=4,Fd=2,qd=8,jd=9,Vd=286,$d=30,Hd=19,Wd=2*Vd+1,Ud=15,Yd=3,Xd=258,Zd=Xd+Yd+1,Gd=42,Kd=103,Jd=113,Qd=666,eh=1,th=2,nh=3,rh=4;function ih(e,t){return e.msg=zd[t],t}function ah(e){return(e<<1)-(e>4?9:0)}function oh(e){for(var t=e.length;--t>=0;)e[t]=0}function sh(e){var t=e.state,n=t.pending;n>e.avail_out&&(n=e.avail_out),0!==n&&(Md.arraySet(e.output,t.pending_buf,t.pending_out,n,e.next_out),e.next_out+=n,t.pending_out+=n,e.total_out+=n,e.avail_out-=n,t.pending-=n,0===t.pending&&(t.pending_out=0))}function lh(e,t){Ed._tr_flush_block(e,e.block_start>=0?e.block_start:-1,e.strstart-e.block_start,t),e.block_start=e.strstart,sh(e.strm)}function uh(e,t){e.pending_buf[e.pending++]=t}function ch(e,t){e.pending_buf[e.pending++]=t>>>8&255,e.pending_buf[e.pending++]=255&t}function dh(e,t){var n,r,i=e.max_chain_length,a=e.strstart,o=e.prev_length,s=e.nice_match,l=e.strstart>e.w_size-Zd?e.strstart-(e.w_size-Zd):0,u=e.window,c=e.w_mask,d=e.prev,h=e.strstart+Xd,f=u[a+o-1],p=u[a+o];e.prev_length>=e.good_match&&(i>>=2),s>e.lookahead&&(s=e.lookahead);do{if(u[(n=t)+o]===p&&u[n+o-1]===f&&u[n]===u[a]&&u[++n]===u[a+1]){a+=2,n++;do{}while(u[++a]===u[++n]&&u[++a]===u[++n]&&u[++a]===u[++n]&&u[++a]===u[++n]&&u[++a]===u[++n]&&u[++a]===u[++n]&&u[++a]===u[++n]&&u[++a]===u[++n]&&a<h);if(r=Xd-(h-a),a=h-Xd,r>o){if(e.match_start=t,o=r,r>=s)break;f=u[a+o-1],p=u[a+o]}}}while((t=d[t&c])>l&&0!=--i);return o<=e.lookahead?o:e.lookahead}function hh(e){var t,n,r,i,a,o,s,l,u,c,d=e.w_size;do{if(i=e.window_size-e.lookahead-e.strstart,e.strstart>=d+(d-Zd)){Md.arraySet(e.window,e.window,d,d,0),e.match_start-=d,e.strstart-=d,e.block_start-=d,t=n=e.hash_size;do{r=e.head[--t],e.head[t]=r>=d?r-d:0}while(--n);t=n=d;do{r=e.prev[--t],e.prev[t]=r>=d?r-d:0}while(--n);i+=d}if(0===e.strm.avail_in)break;if(o=e.strm,s=e.window,l=e.strstart+e.lookahead,u=i,c=void 0,(c=o.avail_in)>u&&(c=u),n=0===c?0:(o.avail_in-=c,Md.arraySet(s,o.input,o.next_in,c,l),1===o.state.wrap?o.adler=Od(o.adler,s,c,l):2===o.state.wrap&&(o.adler=Ld(o.adler,s,c,l)),o.next_in+=c,o.total_in+=c,c),e.lookahead+=n,e.lookahead+e.insert>=Yd)for(a=e.strstart-e.insert,e.ins_h=e.window[a],e.ins_h=(e.ins_h<<e.hash_shift^e.window[a+1])&e.hash_mask;e.insert&&(e.ins_h=(e.ins_h<<e.hash_shift^e.window[a+Yd-1])&e.hash_mask,e.prev[a&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=a,a++,e.insert--,!(e.lookahead+e.insert<Yd)););}while(e.lookahead<Zd&&0!==e.strm.avail_in)}function fh(e,t){for(var n,r;;){if(e.lookahead<Zd){if(hh(e),e.lookahead<Zd&&t===Nd)return eh;if(0===e.lookahead)break}if(n=0,e.lookahead>=Yd&&(e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+Yd-1])&e.hash_mask,n=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart),0!==n&&e.strstart-n<=e.w_size-Zd&&(e.match_length=dh(e,n)),e.match_length>=Yd)if(r=Ed._tr_tally(e,e.strstart-e.match_start,e.match_length-Yd),e.lookahead-=e.match_length,e.match_length<=e.max_lazy_match&&e.lookahead>=Yd){e.match_length--;do{e.strstart++,e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+Yd-1])&e.hash_mask,n=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart}while(0!=--e.match_length);e.strstart++}else e.strstart+=e.match_length,e.match_length=0,e.ins_h=e.window[e.strstart],e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+1])&e.hash_mask;else r=Ed._tr_tally(e,0,e.window[e.strstart]),e.lookahead--,e.strstart++;if(r&&(lh(e,!1),0===e.strm.avail_out))return eh}return e.insert=e.strstart<Yd-1?e.strstart:Yd-1,t===Id?(lh(e,!0),0===e.strm.avail_out?nh:rh):e.last_lit&&(lh(e,!1),0===e.strm.avail_out)?eh:th}function ph(e,t){for(var n,r,i;;){if(e.lookahead<Zd){if(hh(e),e.lookahead<Zd&&t===Nd)return eh;if(0===e.lookahead)break}if(n=0,e.lookahead>=Yd&&(e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+Yd-1])&e.hash_mask,n=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart),e.prev_length=e.match_length,e.prev_match=e.match_start,e.match_length=Yd-1,0!==n&&e.prev_length<e.max_lazy_match&&e.strstart-n<=e.w_size-Zd&&(e.match_length=dh(e,n),e.match_length<=5&&(1===e.strategy||e.match_length===Yd&&e.strstart-e.match_start>4096)&&(e.match_length=Yd-1)),e.prev_length>=Yd&&e.match_length<=e.prev_length){i=e.strstart+e.lookahead-Yd,r=Ed._tr_tally(e,e.strstart-1-e.prev_match,e.prev_length-Yd),e.lookahead-=e.prev_length-1,e.prev_length-=2;do{++e.strstart<=i&&(e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+Yd-1])&e.hash_mask,n=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart)}while(0!=--e.prev_length);if(e.match_available=0,e.match_length=Yd-1,e.strstart++,r&&(lh(e,!1),0===e.strm.avail_out))return eh}else if(e.match_available){if((r=Ed._tr_tally(e,0,e.window[e.strstart-1]))&&lh(e,!1),e.strstart++,e.lookahead--,0===e.strm.avail_out)return eh}else e.match_available=1,e.strstart++,e.lookahead--}return e.match_available&&(r=Ed._tr_tally(e,0,e.window[e.strstart-1]),e.match_available=0),e.insert=e.strstart<Yd-1?e.strstart:Yd-1,t===Id?(lh(e,!0),0===e.strm.avail_out?nh:rh):e.last_lit&&(lh(e,!1),0===e.strm.avail_out)?eh:th}function vh(e,t,n,r,i){this.good_length=e,this.max_lazy=t,this.nice_length=n,this.max_chain=r,this.func=i}function gh(){this.strm=null,this.status=0,this.pending_buf=null,this.pending_buf_size=0,this.pending_out=0,this.pending=0,this.wrap=0,this.gzhead=null,this.gzindex=0,this.method=qd,this.last_flush=-1,this.w_size=0,this.w_bits=0,this.w_mask=0,this.window=null,this.window_size=0,this.prev=null,this.head=null,this.ins_h=0,this.hash_size=0,this.hash_bits=0,this.hash_mask=0,this.hash_shift=0,this.block_start=0,this.match_length=0,this.prev_match=0,this.match_available=0,this.strstart=0,this.match_start=0,this.lookahead=0,this.prev_length=0,this.max_chain_length=0,this.max_lazy_match=0,this.level=0,this.strategy=0,this.good_match=0,this.nice_match=0,this.dyn_ltree=new Md.Buf16(2*Wd),this.dyn_dtree=new Md.Buf16(2*(2*$d+1)),this.bl_tree=new Md.Buf16(2*(2*Hd+1)),oh(this.dyn_ltree),oh(this.dyn_dtree),oh(this.bl_tree),this.l_desc=null,this.d_desc=null,this.bl_desc=null,this.bl_count=new Md.Buf16(Ud+1),this.heap=new Md.Buf16(2*Vd+1),oh(this.heap),this.heap_len=0,this.heap_max=0,this.depth=new Md.Buf16(2*Vd+1),oh(this.depth),this.l_buf=0,this.lit_bufsize=0,this.last_lit=0,this.d_buf=0,this.opt_len=0,this.static_len=0,this.matches=0,this.insert=0,this.bi_buf=0,this.bi_valid=0}function mh(e){var t;return e&&e.state?(e.total_in=e.total_out=0,e.data_type=Fd,(t=e.state).pending=0,t.pending_out=0,t.wrap<0&&(t.wrap=-t.wrap),t.status=t.wrap?Gd:Jd,e.adler=2===t.wrap?0:1,t.last_flush=Nd,Ed._tr_init(t),Pd):ih(e,Dd)}function _h(e){var t,n=mh(e);return n===Pd&&((t=e.state).window_size=2*t.w_size,oh(t.head),t.max_lazy_match=Cd[t.level].max_lazy,t.good_match=Cd[t.level].good_length,t.nice_match=Cd[t.level].nice_length,t.max_chain_length=Cd[t.level].max_chain,t.strstart=0,t.block_start=0,t.lookahead=0,t.insert=0,t.match_length=t.prev_length=Yd-1,t.match_available=0,t.ins_h=0),n}function yh(e,t,n,r,i,a){if(!e)return Dd;var o=1;if(t===Bd&&(t=6),r<0?(o=0,r=-r):r>15&&(o=2,r-=16),i<1||i>jd||n!==qd||r<8||r>15||t<0||t>9||a<0||a>Rd)return ih(e,Dd);8===r&&(r=9);var s=new gh;return e.state=s,s.strm=e,s.wrap=o,s.gzhead=null,s.w_bits=r,s.w_size=1<<s.w_bits,s.w_mask=s.w_size-1,s.hash_bits=i+7,s.hash_size=1<<s.hash_bits,s.hash_mask=s.hash_size-1,s.hash_shift=~~((s.hash_bits+Yd-1)/Yd),s.window=new Md.Buf8(2*s.w_size),s.head=new Md.Buf16(s.hash_size),s.prev=new Md.Buf16(s.w_size),s.lit_bufsize=1<<i+6,s.pending_buf_size=4*s.lit_bufsize,s.pending_buf=new Md.Buf8(s.pending_buf_size),s.d_buf=1*s.lit_bufsize,s.l_buf=3*s.lit_bufsize,s.level=t,s.strategy=a,s.method=n,_h(e)}Cd=[new vh(0,0,0,0,(function(e,t){var n=65535;for(n>e.pending_buf_size-5&&(n=e.pending_buf_size-5);;){if(e.lookahead<=1){if(hh(e),0===e.lookahead&&t===Nd)return eh;if(0===e.lookahead)break}e.strstart+=e.lookahead,e.lookahead=0;var r=e.block_start+n;if((0===e.strstart||e.strstart>=r)&&(e.lookahead=e.strstart-r,e.strstart=r,lh(e,!1),0===e.strm.avail_out))return eh;if(e.strstart-e.block_start>=e.w_size-Zd&&(lh(e,!1),0===e.strm.avail_out))return eh}return e.insert=0,t===Id?(lh(e,!0),0===e.strm.avail_out?nh:rh):(e.strstart>e.block_start&&(lh(e,!1),e.strm.avail_out),eh)})),new vh(4,4,8,4,fh),new vh(4,5,16,8,fh),new vh(4,6,32,32,fh),new vh(4,4,16,16,ph),new vh(8,16,32,32,ph),new vh(8,16,128,128,ph),new vh(8,32,128,256,ph),new vh(32,128,258,1024,ph),new vh(32,258,258,4096,ph)],Tc.deflateInit=function(e,t){return yh(e,t,qd,15,8,0)},Tc.deflateInit2=yh,Tc.deflateReset=_h,Tc.deflateResetKeep=mh,Tc.deflateSetHeader=function(e,t){return e&&e.state?2!==e.state.wrap?Dd:(e.state.gzhead=t,Pd):Dd},Tc.deflate=function(e,t){var n,r,i,a;if(!e||!e.state||t>5||t<0)return e?ih(e,Dd):Dd;if(r=e.state,!e.output||!e.input&&0!==e.avail_in||r.status===Qd&&t!==Id)return ih(e,0===e.avail_out?-5:Dd);if(r.strm=e,n=r.last_flush,r.last_flush=t,r.status===Gd)if(2===r.wrap)e.adler=0,uh(r,31),uh(r,139),uh(r,8),r.gzhead?(uh(r,(r.gzhead.text?1:0)+(r.gzhead.hcrc?2:0)+(r.gzhead.extra?4:0)+(r.gzhead.name?8:0)+(r.gzhead.comment?16:0)),uh(r,255&r.gzhead.time),uh(r,r.gzhead.time>>8&255),uh(r,r.gzhead.time>>16&255),uh(r,r.gzhead.time>>24&255),uh(r,9===r.level?2:r.strategy>=2||r.level<2?4:0),uh(r,255&r.gzhead.os),r.gzhead.extra&&r.gzhead.extra.length&&(uh(r,255&r.gzhead.extra.length),uh(r,r.gzhead.extra.length>>8&255)),r.gzhead.hcrc&&(e.adler=Ld(e.adler,r.pending_buf,r.pending,0)),r.gzindex=0,r.status=69):(uh(r,0),uh(r,0),uh(r,0),uh(r,0),uh(r,0),uh(r,9===r.level?2:r.strategy>=2||r.level<2?4:0),uh(r,3),r.status=Jd);else{var o=qd+(r.w_bits-8<<4)<<8;o|=(r.strategy>=2||r.level<2?0:r.level<6?1:6===r.level?2:3)<<6,0!==r.strstart&&(o|=32),o+=31-o%31,r.status=Jd,ch(r,o),0!==r.strstart&&(ch(r,e.adler>>>16),ch(r,65535&e.adler)),e.adler=1}if(69===r.status)if(r.gzhead.extra){for(i=r.pending;r.gzindex<(65535&r.gzhead.extra.length)&&(r.pending!==r.pending_buf_size||(r.gzhead.hcrc&&r.pending>i&&(e.adler=Ld(e.adler,r.pending_buf,r.pending-i,i)),sh(e),i=r.pending,r.pending!==r.pending_buf_size));)uh(r,255&r.gzhead.extra[r.gzindex]),r.gzindex++;r.gzhead.hcrc&&r.pending>i&&(e.adler=Ld(e.adler,r.pending_buf,r.pending-i,i)),r.gzindex===r.gzhead.extra.length&&(r.gzindex=0,r.status=73)}else r.status=73;if(73===r.status)if(r.gzhead.name){i=r.pending;do{if(r.pending===r.pending_buf_size&&(r.gzhead.hcrc&&r.pending>i&&(e.adler=Ld(e.adler,r.pending_buf,r.pending-i,i)),sh(e),i=r.pending,r.pending===r.pending_buf_size)){a=1;break}a=r.gzindex<r.gzhead.name.length?255&r.gzhead.name.charCodeAt(r.gzindex++):0,uh(r,a)}while(0!==a);r.gzhead.hcrc&&r.pending>i&&(e.adler=Ld(e.adler,r.pending_buf,r.pending-i,i)),0===a&&(r.gzindex=0,r.status=91)}else r.status=91;if(91===r.status)if(r.gzhead.comment){i=r.pending;do{if(r.pending===r.pending_buf_size&&(r.gzhead.hcrc&&r.pending>i&&(e.adler=Ld(e.adler,r.pending_buf,r.pending-i,i)),sh(e),i=r.pending,r.pending===r.pending_buf_size)){a=1;break}a=r.gzindex<r.gzhead.comment.length?255&r.gzhead.comment.charCodeAt(r.gzindex++):0,uh(r,a)}while(0!==a);r.gzhead.hcrc&&r.pending>i&&(e.adler=Ld(e.adler,r.pending_buf,r.pending-i,i)),0===a&&(r.status=Kd)}else r.status=Kd;if(r.status===Kd&&(r.gzhead.hcrc?(r.pending+2>r.pending_buf_size&&sh(e),r.pending+2<=r.pending_buf_size&&(uh(r,255&e.adler),uh(r,e.adler>>8&255),e.adler=0,r.status=Jd)):r.status=Jd),0!==r.pending){if(sh(e),0===e.avail_out)return r.last_flush=-1,Pd}else if(0===e.avail_in&&ah(t)<=ah(n)&&t!==Id)return ih(e,-5);if(r.status===Qd&&0!==e.avail_in)return ih(e,-5);if(0!==e.avail_in||0!==r.lookahead||t!==Nd&&r.status!==Qd){var s=2===r.strategy?function(e,t){for(var n;;){if(0===e.lookahead&&(hh(e),0===e.lookahead)){if(t===Nd)return eh;break}if(e.match_length=0,n=Ed._tr_tally(e,0,e.window[e.strstart]),e.lookahead--,e.strstart++,n&&(lh(e,!1),0===e.strm.avail_out))return eh}return e.insert=0,t===Id?(lh(e,!0),0===e.strm.avail_out?nh:rh):e.last_lit&&(lh(e,!1),0===e.strm.avail_out)?eh:th}(r,t):3===r.strategy?function(e,t){for(var n,r,i,a,o=e.window;;){if(e.lookahead<=Xd){if(hh(e),e.lookahead<=Xd&&t===Nd)return eh;if(0===e.lookahead)break}if(e.match_length=0,e.lookahead>=Yd&&e.strstart>0&&(r=o[i=e.strstart-1])===o[++i]&&r===o[++i]&&r===o[++i]){a=e.strstart+Xd;do{}while(r===o[++i]&&r===o[++i]&&r===o[++i]&&r===o[++i]&&r===o[++i]&&r===o[++i]&&r===o[++i]&&r===o[++i]&&i<a);e.match_length=Xd-(a-i),e.match_length>e.lookahead&&(e.match_length=e.lookahead)}if(e.match_length>=Yd?(n=Ed._tr_tally(e,1,e.match_length-Yd),e.lookahead-=e.match_length,e.strstart+=e.match_length,e.match_length=0):(n=Ed._tr_tally(e,0,e.window[e.strstart]),e.lookahead--,e.strstart++),n&&(lh(e,!1),0===e.strm.avail_out))return eh}return e.insert=0,t===Id?(lh(e,!0),0===e.strm.avail_out?nh:rh):e.last_lit&&(lh(e,!1),0===e.strm.avail_out)?eh:th}(r,t):Cd[r.level].func(r,t);if(s!==nh&&s!==rh||(r.status=Qd),s===eh||s===nh)return 0===e.avail_out&&(r.last_flush=-1),Pd;if(s===th&&(1===t?Ed._tr_align(r):5!==t&&(Ed._tr_stored_block(r,0,0,!1),3===t&&(oh(r.head),0===r.lookahead&&(r.strstart=0,r.block_start=0,r.insert=0))),sh(e),0===e.avail_out))return r.last_flush=-1,Pd}return t!==Id?Pd:r.wrap<=0?1:(2===r.wrap?(uh(r,255&e.adler),uh(r,e.adler>>8&255),uh(r,e.adler>>16&255),uh(r,e.adler>>24&255),uh(r,255&e.total_in),uh(r,e.total_in>>8&255),uh(r,e.total_in>>16&255),uh(r,e.total_in>>24&255)):(ch(r,e.adler>>>16),ch(r,65535&e.adler)),sh(e),r.wrap>0&&(r.wrap=-r.wrap),0!==r.pending?Pd:1)},Tc.deflateEnd=function(e){var t;return e&&e.state?(t=e.state.status)!==Gd&&69!==t&&73!==t&&91!==t&&t!==Kd&&t!==Jd&&t!==Qd?ih(e,Dd):(e.state=null,t===Jd?ih(e,-3):Pd):Dd},Tc.deflateSetDictionary=function(e,t){var n,r,i,a,o,s,l,u,c=t.length;if(!e||!e.state)return Dd;if(2===(a=(n=e.state).wrap)||1===a&&n.status!==Gd||n.lookahead)return Dd;for(1===a&&(e.adler=Od(e.adler,t,c,0)),n.wrap=0,c>=n.w_size&&(0===a&&(oh(n.head),n.strstart=0,n.block_start=0,n.insert=0),u=new Md.Buf8(n.w_size),Md.arraySet(u,t,c-n.w_size,n.w_size,0),t=u,c=n.w_size),o=e.avail_in,s=e.next_in,l=e.input,e.avail_in=c,e.next_in=0,e.input=t,hh(n);n.lookahead>=Yd;){r=n.strstart,i=n.lookahead-(Yd-1);do{n.ins_h=(n.ins_h<<n.hash_shift^n.window[r+Yd-1])&n.hash_mask,n.prev[r&n.w_mask]=n.head[n.ins_h],n.head[n.ins_h]=r,r++}while(--i);n.strstart=r,n.lookahead=Yd-1,hh(n)}return n.strstart+=n.lookahead,n.block_start=n.strstart,n.insert=n.lookahead,n.lookahead=0,n.match_length=n.prev_length=Yd-1,n.match_available=0,e.next_in=s,e.input=l,e.avail_in=o,n.wrap=a,Pd},Tc.deflateInfo="pako deflate (from Nodeca project)";var bh={},wh=kc,xh=!0,Sh=!0;try{String.fromCharCode.apply(null,[0])}catch(mb){xh=!1}try{String.fromCharCode.apply(null,new Uint8Array(1))}catch(mb){Sh=!1}for(var kh=new wh.Buf8(256),Ch=0;Ch<256;Ch++)kh[Ch]=Ch>=252?6:Ch>=248?5:Ch>=240?4:Ch>=224?3:Ch>=192?2:1;function Th(e,t){if(t<65534&&(e.subarray&&Sh||!e.subarray&&xh))return String.fromCharCode.apply(null,wh.shrinkBuf(e,t));for(var n="",r=0;r<t;r++)n+=String.fromCharCode(e[r]);return n}kh[254]=kh[254]=1,bh.string2buf=function(e){var t,n,r,i,a,o=e.length,s=0;for(i=0;i<o;i++)55296==(64512&(n=e.charCodeAt(i)))&&i+1<o&&56320==(64512&(r=e.charCodeAt(i+1)))&&(n=65536+(n-55296<<10)+(r-56320),i++),s+=n<128?1:n<2048?2:n<65536?3:4;for(t=new wh.Buf8(s),a=0,i=0;a<s;i++)55296==(64512&(n=e.charCodeAt(i)))&&i+1<o&&56320==(64512&(r=e.charCodeAt(i+1)))&&(n=65536+(n-55296<<10)+(r-56320),i++),n<128?t[a++]=n:n<2048?(t[a++]=192|n>>>6,t[a++]=128|63&n):n<65536?(t[a++]=224|n>>>12,t[a++]=128|n>>>6&63,t[a++]=128|63&n):(t[a++]=240|n>>>18,t[a++]=128|n>>>12&63,t[a++]=128|n>>>6&63,t[a++]=128|63&n);return t},bh.buf2binstring=function(e){return Th(e,e.length)},bh.binstring2buf=function(e){for(var t=new wh.Buf8(e.length),n=0,r=t.length;n<r;n++)t[n]=e.charCodeAt(n);return t},bh.buf2string=function(e,t){var n,r,i,a,o=t||e.length,s=new Array(2*o);for(r=0,n=0;n<o;)if((i=e[n++])<128)s[r++]=i;else if((a=kh[i])>4)s[r++]=65533,n+=a-1;else{for(i&=2===a?31:3===a?15:7;a>1&&n<o;)i=i<<6|63&e[n++],a--;a>1?s[r++]=65533:i<65536?s[r++]=i:(i-=65536,s[r++]=55296|i>>10&1023,s[r++]=56320|1023&i)}return Th(s,r)},bh.utf8border=function(e,t){var n;for((t=t||e.length)>e.length&&(t=e.length),n=t-1;n>=0&&128==(192&e[n]);)n--;return n<0||0===n?t:n+kh[e[n]]>t?n:t};var Ah=function(){this.input=null,this.next_in=0,this.avail_in=0,this.total_in=0,this.output=null,this.next_out=0,this.avail_out=0,this.total_out=0,this.msg="",this.state=null,this.data_type=2,this.adler=0},Mh=Tc,Eh=kc,Oh=bh,Lh=Ad,zh=Ah,Nh=Object.prototype.toString,Ih=0,Ph=-1,Dh=0,Bh=8;function Rh(e){if(!(this instanceof Rh))return new Rh(e);this.options=Eh.assign({level:Ph,method:Bh,chunkSize:16384,windowBits:15,memLevel:8,strategy:Dh,to:""},e||{});var t=this.options;t.raw&&t.windowBits>0?t.windowBits=-t.windowBits:t.gzip&&t.windowBits>0&&t.windowBits<16&&(t.windowBits+=16),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new zh,this.strm.avail_out=0;var n=Mh.deflateInit2(this.strm,t.level,t.method,t.windowBits,t.memLevel,t.strategy);if(n!==Ih)throw new Error(Lh[n]);if(t.header&&Mh.deflateSetHeader(this.strm,t.header),t.dictionary){var r;if(r="string"==typeof t.dictionary?Oh.string2buf(t.dictionary):"[object ArrayBuffer]"===Nh.call(t.dictionary)?new Uint8Array(t.dictionary):t.dictionary,(n=Mh.deflateSetDictionary(this.strm,r))!==Ih)throw new Error(Lh[n]);this._dict_set=!0}}function Fh(e,t){var n=new Rh(t);if(n.push(e,!0),n.err)throw n.msg||Lh[n.err];return n.result}Rh.prototype.push=function(e,t){var n,r,i=this.strm,a=this.options.chunkSize;if(this.ended)return!1;r=t===~~t?t:!0===t?4:0,"string"==typeof e?i.input=Oh.string2buf(e):"[object ArrayBuffer]"===Nh.call(e)?i.input=new Uint8Array(e):i.input=e,i.next_in=0,i.avail_in=i.input.length;do{if(0===i.avail_out&&(i.output=new Eh.Buf8(a),i.next_out=0,i.avail_out=a),1!==(n=Mh.deflate(i,r))&&n!==Ih)return this.onEnd(n),this.ended=!0,!1;0!==i.avail_out&&(0!==i.avail_in||4!==r&&2!==r)||("string"===this.options.to?this.onData(Oh.buf2binstring(Eh.shrinkBuf(i.output,i.next_out))):this.onData(Eh.shrinkBuf(i.output,i.next_out)))}while((i.avail_in>0||0===i.avail_out)&&1!==n);return 4===r?(n=Mh.deflateEnd(this.strm),this.onEnd(n),this.ended=!0,n===Ih):2!==r||(this.onEnd(Ih),i.avail_out=0,!0)},Rh.prototype.onData=function(e){this.chunks.push(e)},Rh.prototype.onEnd=function(e){e===Ih&&("string"===this.options.to?this.result=this.chunks.join(""):this.result=Eh.flattenChunks(this.chunks)),this.chunks=[],this.err=e,this.msg=this.strm.msg},Cc.Deflate=Rh,Cc.deflate=Fh,Cc.deflateRaw=function(e,t){return(t=t||{}).raw=!0,Fh(e,t)},Cc.gzip=function(e,t){return(t=t||{}).gzip=!0,Fh(e,t)};var qh={},jh={},Vh=kc,$h=15,Hh=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,0,0],Wh=[16,16,16,16,16,16,16,16,17,17,17,17,18,18,18,18,19,19,19,19,20,20,20,20,21,21,21,21,16,72,78],Uh=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577,0,0],Yh=[16,16,16,16,17,17,18,18,19,19,20,20,21,21,22,22,23,23,24,24,25,25,26,26,27,27,28,28,29,29,64,64],Xh=kc,Zh=Sd,Gh=Td,Kh=function(e,t){var n,r,i,a,o,s,l,u,c,d,h,f,p,v,g,m,_,y,b,w,x,S,k,C,T;n=e.state,r=e.next_in,C=e.input,i=r+(e.avail_in-5),a=e.next_out,T=e.output,o=a-(t-e.avail_out),s=a+(e.avail_out-257),l=n.dmax,u=n.wsize,c=n.whave,d=n.wnext,h=n.window,f=n.hold,p=n.bits,v=n.lencode,g=n.distcode,m=(1<<n.lenbits)-1,_=(1<<n.distbits)-1;e:do{p<15&&(f+=C[r++]<<p,p+=8,f+=C[r++]<<p,p+=8),y=v[f&m];t:for(;;){if(f>>>=b=y>>>24,p-=b,0===(b=y>>>16&255))T[a++]=65535&y;else{if(!(16&b)){if(64&b){if(32&b){n.mode=12;break e}e.msg="invalid literal/length code",n.mode=30;break e}y=v[(65535&y)+(f&(1<<b)-1)];continue t}for(w=65535&y,(b&=15)&&(p<b&&(f+=C[r++]<<p,p+=8),w+=f&(1<<b)-1,f>>>=b,p-=b),p<15&&(f+=C[r++]<<p,p+=8,f+=C[r++]<<p,p+=8),y=g[f&_];;){if(f>>>=b=y>>>24,p-=b,16&(b=y>>>16&255)){if(x=65535&y,p<(b&=15)&&(f+=C[r++]<<p,(p+=8)<b&&(f+=C[r++]<<p,p+=8)),(x+=f&(1<<b)-1)>l){e.msg="invalid distance too far back",n.mode=30;break e}if(f>>>=b,p-=b,x>(b=a-o)){if((b=x-b)>c&&n.sane){e.msg="invalid distance too far back",n.mode=30;break e}if(S=0,k=h,0===d){if(S+=u-b,b<w){w-=b;do{T[a++]=h[S++]}while(--b);S=a-x,k=T}}else if(d<b){if(S+=u+d-b,(b-=d)<w){w-=b;do{T[a++]=h[S++]}while(--b);if(S=0,d<w){w-=b=d;do{T[a++]=h[S++]}while(--b);S=a-x,k=T}}}else if(S+=d-b,b<w){w-=b;do{T[a++]=h[S++]}while(--b);S=a-x,k=T}for(;w>2;)T[a++]=k[S++],T[a++]=k[S++],T[a++]=k[S++],w-=3;w&&(T[a++]=k[S++],w>1&&(T[a++]=k[S++]))}else{S=a-x;do{T[a++]=T[S++],T[a++]=T[S++],T[a++]=T[S++],w-=3}while(w>2);w&&(T[a++]=T[S++],w>1&&(T[a++]=T[S++]))}break}if(64&b){e.msg="invalid distance code",n.mode=30;break e}y=g[(65535&y)+(f&(1<<b)-1)]}}break}}while(r<i&&a<s);r-=w=p>>3,f&=(1<<(p-=w<<3))-1,e.next_in=r,e.next_out=a,e.avail_in=r<i?i-r+5:5-(r-i),e.avail_out=a<s?s-a+257:257-(a-s),n.hold=f,n.bits=p},Jh=function(e,t,n,r,i,a,o,s){var l,u,c,d,h,f,p,v,g,m=s.bits,_=0,y=0,b=0,w=0,x=0,S=0,k=0,C=0,T=0,A=0,M=null,E=0,O=new Vh.Buf16(16),L=new Vh.Buf16(16),z=null,N=0;for(_=0;_<=$h;_++)O[_]=0;for(y=0;y<r;y++)O[t[n+y]]++;for(x=m,w=$h;w>=1&&0===O[w];w--);if(x>w&&(x=w),0===w)return i[a++]=20971520,i[a++]=20971520,s.bits=1,0;for(b=1;b<w&&0===O[b];b++);for(x<b&&(x=b),C=1,_=1;_<=$h;_++)if(C<<=1,(C-=O[_])<0)return-1;if(C>0&&(0===e||1!==w))return-1;for(L[1]=0,_=1;_<$h;_++)L[_+1]=L[_]+O[_];for(y=0;y<r;y++)0!==t[n+y]&&(o[L[t[n+y]]++]=y);if(0===e?(M=z=o,f=19):1===e?(M=Hh,E-=257,z=Wh,N-=257,f=256):(M=Uh,z=Yh,f=-1),A=0,y=0,_=b,h=a,S=x,k=0,c=-1,d=(T=1<<x)-1,1===e&&T>852||2===e&&T>592)return 1;for(;;){p=_-k,o[y]<f?(v=0,g=o[y]):o[y]>f?(v=z[N+o[y]],g=M[E+o[y]]):(v=96,g=0),l=1<<_-k,b=u=1<<S;do{i[h+(A>>k)+(u-=l)]=p<<24|v<<16|g}while(0!==u);for(l=1<<_-1;A&l;)l>>=1;if(0!==l?(A&=l-1,A+=l):A=0,y++,0==--O[_]){if(_===w)break;_=t[n+o[y]]}if(_>x&&(A&d)!==c){for(0===k&&(k=x),h+=b,C=1<<(S=_-k);S+k<w&&!((C-=O[S+k])<=0);)S++,C<<=1;if(T+=1<<S,1===e&&T>852||2===e&&T>592)return 1;i[c=A&d]=x<<24|S<<16|h-a}}return 0!==A&&(i[h+A]=_-k<<24|64<<16),s.bits=x,0},Qh=1,ef=2,tf=0,nf=-2,rf=1,af=12,of=30,sf=852,lf=592;function uf(e){return(e>>>24&255)+(e>>>8&65280)+((65280&e)<<8)+((255&e)<<24)}function cf(){this.mode=0,this.last=!1,this.wrap=0,this.havedict=!1,this.flags=0,this.dmax=0,this.check=0,this.total=0,this.head=null,this.wbits=0,this.wsize=0,this.whave=0,this.wnext=0,this.window=null,this.hold=0,this.bits=0,this.length=0,this.offset=0,this.extra=0,this.lencode=null,this.distcode=null,this.lenbits=0,this.distbits=0,this.ncode=0,this.nlen=0,this.ndist=0,this.have=0,this.next=null,this.lens=new Xh.Buf16(320),this.work=new Xh.Buf16(288),this.lendyn=null,this.distdyn=null,this.sane=0,this.back=0,this.was=0}function df(e){var t;return e&&e.state?(t=e.state,e.total_in=e.total_out=t.total=0,e.msg="",t.wrap&&(e.adler=1&t.wrap),t.mode=rf,t.last=0,t.havedict=0,t.dmax=32768,t.head=null,t.hold=0,t.bits=0,t.lencode=t.lendyn=new Xh.Buf32(sf),t.distcode=t.distdyn=new Xh.Buf32(lf),t.sane=1,t.back=-1,tf):nf}function hf(e){var t;return e&&e.state?((t=e.state).wsize=0,t.whave=0,t.wnext=0,df(e)):nf}function ff(e,t){var n,r;return e&&e.state?(r=e.state,t<0?(n=0,t=-t):(n=1+(t>>4),t<48&&(t&=15)),t&&(t<8||t>15)?nf:(null!==r.window&&r.wbits!==t&&(r.window=null),r.wrap=n,r.wbits=t,hf(e))):nf}function pf(e,t){var n,r;return e?(r=new cf,e.state=r,r.window=null,(n=ff(e,t))!==tf&&(e.state=null),n):nf}var vf,gf,mf=!0;function _f(e){if(mf){var t;for(vf=new Xh.Buf32(512),gf=new Xh.Buf32(32),t=0;t<144;)e.lens[t++]=8;for(;t<256;)e.lens[t++]=9;for(;t<280;)e.lens[t++]=7;for(;t<288;)e.lens[t++]=8;for(Jh(Qh,e.lens,0,288,vf,0,e.work,{bits:9}),t=0;t<32;)e.lens[t++]=5;Jh(ef,e.lens,0,32,gf,0,e.work,{bits:5}),mf=!1}e.lencode=vf,e.lenbits=9,e.distcode=gf,e.distbits=5}function yf(e,t,n,r){var i,a=e.state;return null===a.window&&(a.wsize=1<<a.wbits,a.wnext=0,a.whave=0,a.window=new Xh.Buf8(a.wsize)),r>=a.wsize?(Xh.arraySet(a.window,t,n-a.wsize,a.wsize,0),a.wnext=0,a.whave=a.wsize):((i=a.wsize-a.wnext)>r&&(i=r),Xh.arraySet(a.window,t,n-r,i,a.wnext),(r-=i)?(Xh.arraySet(a.window,t,n-r,r,0),a.wnext=r,a.whave=a.wsize):(a.wnext+=i,a.wnext===a.wsize&&(a.wnext=0),a.whave<a.wsize&&(a.whave+=i))),0}jh.inflateReset=hf,jh.inflateReset2=ff,jh.inflateResetKeep=df,jh.inflateInit=function(e){return pf(e,15)},jh.inflateInit2=pf,jh.inflate=function(e,t){var n,r,i,a,o,s,l,u,c,d,h,f,p,v,g,m,_,y,b,w,x,S,k,C,T=0,A=new Xh.Buf8(4),M=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15];if(!e||!e.state||!e.output||!e.input&&0!==e.avail_in)return nf;(n=e.state).mode===af&&(n.mode=13),o=e.next_out,i=e.output,l=e.avail_out,a=e.next_in,r=e.input,s=e.avail_in,u=n.hold,c=n.bits,d=s,h=l,S=tf;e:for(;;)switch(n.mode){case rf:if(0===n.wrap){n.mode=13;break}for(;c<16;){if(0===s)break e;s--,u+=r[a++]<<c,c+=8}if(2&n.wrap&&35615===u){n.check=0,A[0]=255&u,A[1]=u>>>8&255,n.check=Gh(n.check,A,2,0),u=0,c=0,n.mode=2;break}if(n.flags=0,n.head&&(n.head.done=!1),!(1&n.wrap)||(((255&u)<<8)+(u>>8))%31){e.msg="incorrect header check",n.mode=of;break}if(8!=(15&u)){e.msg="unknown compression method",n.mode=of;break}if(c-=4,x=8+(15&(u>>>=4)),0===n.wbits)n.wbits=x;else if(x>n.wbits){e.msg="invalid window size",n.mode=of;break}n.dmax=1<<x,e.adler=n.check=1,n.mode=512&u?10:af,u=0,c=0;break;case 2:for(;c<16;){if(0===s)break e;s--,u+=r[a++]<<c,c+=8}if(n.flags=u,8!=(255&n.flags)){e.msg="unknown compression method",n.mode=of;break}if(57344&n.flags){e.msg="unknown header flags set",n.mode=of;break}n.head&&(n.head.text=u>>8&1),512&n.flags&&(A[0]=255&u,A[1]=u>>>8&255,n.check=Gh(n.check,A,2,0)),u=0,c=0,n.mode=3;case 3:for(;c<32;){if(0===s)break e;s--,u+=r[a++]<<c,c+=8}n.head&&(n.head.time=u),512&n.flags&&(A[0]=255&u,A[1]=u>>>8&255,A[2]=u>>>16&255,A[3]=u>>>24&255,n.check=Gh(n.check,A,4,0)),u=0,c=0,n.mode=4;case 4:for(;c<16;){if(0===s)break e;s--,u+=r[a++]<<c,c+=8}n.head&&(n.head.xflags=255&u,n.head.os=u>>8),512&n.flags&&(A[0]=255&u,A[1]=u>>>8&255,n.check=Gh(n.check,A,2,0)),u=0,c=0,n.mode=5;case 5:if(1024&n.flags){for(;c<16;){if(0===s)break e;s--,u+=r[a++]<<c,c+=8}n.length=u,n.head&&(n.head.extra_len=u),512&n.flags&&(A[0]=255&u,A[1]=u>>>8&255,n.check=Gh(n.check,A,2,0)),u=0,c=0}else n.head&&(n.head.extra=null);n.mode=6;case 6:if(1024&n.flags&&((f=n.length)>s&&(f=s),f&&(n.head&&(x=n.head.extra_len-n.length,n.head.extra||(n.head.extra=new Array(n.head.extra_len)),Xh.arraySet(n.head.extra,r,a,f,x)),512&n.flags&&(n.check=Gh(n.check,r,f,a)),s-=f,a+=f,n.length-=f),n.length))break e;n.length=0,n.mode=7;case 7:if(2048&n.flags){if(0===s)break e;f=0;do{x=r[a+f++],n.head&&x&&n.length<65536&&(n.head.name+=String.fromCharCode(x))}while(x&&f<s);if(512&n.flags&&(n.check=Gh(n.check,r,f,a)),s-=f,a+=f,x)break e}else n.head&&(n.head.name=null);n.length=0,n.mode=8;case 8:if(4096&n.flags){if(0===s)break e;f=0;do{x=r[a+f++],n.head&&x&&n.length<65536&&(n.head.comment+=String.fromCharCode(x))}while(x&&f<s);if(512&n.flags&&(n.check=Gh(n.check,r,f,a)),s-=f,a+=f,x)break e}else n.head&&(n.head.comment=null);n.mode=9;case 9:if(512&n.flags){for(;c<16;){if(0===s)break e;s--,u+=r[a++]<<c,c+=8}if(u!==(65535&n.check)){e.msg="header crc mismatch",n.mode=of;break}u=0,c=0}n.head&&(n.head.hcrc=n.flags>>9&1,n.head.done=!0),e.adler=n.check=0,n.mode=af;break;case 10:for(;c<32;){if(0===s)break e;s--,u+=r[a++]<<c,c+=8}e.adler=n.check=uf(u),u=0,c=0,n.mode=11;case 11:if(0===n.havedict)return e.next_out=o,e.avail_out=l,e.next_in=a,e.avail_in=s,n.hold=u,n.bits=c,2;e.adler=n.check=1,n.mode=af;case af:if(5===t||6===t)break e;case 13:if(n.last){u>>>=7&c,c-=7&c,n.mode=27;break}for(;c<3;){if(0===s)break e;s--,u+=r[a++]<<c,c+=8}switch(n.last=1&u,c-=1,3&(u>>>=1)){case 0:n.mode=14;break;case 1:if(_f(n),n.mode=20,6===t){u>>>=2,c-=2;break e}break;case 2:n.mode=17;break;case 3:e.msg="invalid block type",n.mode=of}u>>>=2,c-=2;break;case 14:for(u>>>=7&c,c-=7&c;c<32;){if(0===s)break e;s--,u+=r[a++]<<c,c+=8}if((65535&u)!=(u>>>16^65535)){e.msg="invalid stored block lengths",n.mode=of;break}if(n.length=65535&u,u=0,c=0,n.mode=15,6===t)break e;case 15:n.mode=16;case 16:if(f=n.length){if(f>s&&(f=s),f>l&&(f=l),0===f)break e;Xh.arraySet(i,r,a,f,o),s-=f,a+=f,l-=f,o+=f,n.length-=f;break}n.mode=af;break;case 17:for(;c<14;){if(0===s)break e;s--,u+=r[a++]<<c,c+=8}if(n.nlen=257+(31&u),u>>>=5,c-=5,n.ndist=1+(31&u),u>>>=5,c-=5,n.ncode=4+(15&u),u>>>=4,c-=4,n.nlen>286||n.ndist>30){e.msg="too many length or distance symbols",n.mode=of;break}n.have=0,n.mode=18;case 18:for(;n.have<n.ncode;){for(;c<3;){if(0===s)break e;s--,u+=r[a++]<<c,c+=8}n.lens[M[n.have++]]=7&u,u>>>=3,c-=3}for(;n.have<19;)n.lens[M[n.have++]]=0;if(n.lencode=n.lendyn,n.lenbits=7,k={bits:n.lenbits},S=Jh(0,n.lens,0,19,n.lencode,0,n.work,k),n.lenbits=k.bits,S){e.msg="invalid code lengths set",n.mode=of;break}n.have=0,n.mode=19;case 19:for(;n.have<n.nlen+n.ndist;){for(;m=(T=n.lencode[u&(1<<n.lenbits)-1])>>>16&255,_=65535&T,!((g=T>>>24)<=c);){if(0===s)break e;s--,u+=r[a++]<<c,c+=8}if(_<16)u>>>=g,c-=g,n.lens[n.have++]=_;else{if(16===_){for(C=g+2;c<C;){if(0===s)break e;s--,u+=r[a++]<<c,c+=8}if(u>>>=g,c-=g,0===n.have){e.msg="invalid bit length repeat",n.mode=of;break}x=n.lens[n.have-1],f=3+(3&u),u>>>=2,c-=2}else if(17===_){for(C=g+3;c<C;){if(0===s)break e;s--,u+=r[a++]<<c,c+=8}c-=g,x=0,f=3+(7&(u>>>=g)),u>>>=3,c-=3}else{for(C=g+7;c<C;){if(0===s)break e;s--,u+=r[a++]<<c,c+=8}c-=g,x=0,f=11+(127&(u>>>=g)),u>>>=7,c-=7}if(n.have+f>n.nlen+n.ndist){e.msg="invalid bit length repeat",n.mode=of;break}for(;f--;)n.lens[n.have++]=x}}if(n.mode===of)break;if(0===n.lens[256]){e.msg="invalid code -- missing end-of-block",n.mode=of;break}if(n.lenbits=9,k={bits:n.lenbits},S=Jh(Qh,n.lens,0,n.nlen,n.lencode,0,n.work,k),n.lenbits=k.bits,S){e.msg="invalid literal/lengths set",n.mode=of;break}if(n.distbits=6,n.distcode=n.distdyn,k={bits:n.distbits},S=Jh(ef,n.lens,n.nlen,n.ndist,n.distcode,0,n.work,k),n.distbits=k.bits,S){e.msg="invalid distances set",n.mode=of;break}if(n.mode=20,6===t)break e;case 20:n.mode=21;case 21:if(s>=6&&l>=258){e.next_out=o,e.avail_out=l,e.next_in=a,e.avail_in=s,n.hold=u,n.bits=c,Kh(e,h),o=e.next_out,i=e.output,l=e.avail_out,a=e.next_in,r=e.input,s=e.avail_in,u=n.hold,c=n.bits,n.mode===af&&(n.back=-1);break}for(n.back=0;m=(T=n.lencode[u&(1<<n.lenbits)-1])>>>16&255,_=65535&T,!((g=T>>>24)<=c);){if(0===s)break e;s--,u+=r[a++]<<c,c+=8}if(m&&!(240&m)){for(y=g,b=m,w=_;m=(T=n.lencode[w+((u&(1<<y+b)-1)>>y)])>>>16&255,_=65535&T,!(y+(g=T>>>24)<=c);){if(0===s)break e;s--,u+=r[a++]<<c,c+=8}u>>>=y,c-=y,n.back+=y}if(u>>>=g,c-=g,n.back+=g,n.length=_,0===m){n.mode=26;break}if(32&m){n.back=-1,n.mode=af;break}if(64&m){e.msg="invalid literal/length code",n.mode=of;break}n.extra=15&m,n.mode=22;case 22:if(n.extra){for(C=n.extra;c<C;){if(0===s)break e;s--,u+=r[a++]<<c,c+=8}n.length+=u&(1<<n.extra)-1,u>>>=n.extra,c-=n.extra,n.back+=n.extra}n.was=n.length,n.mode=23;case 23:for(;m=(T=n.distcode[u&(1<<n.distbits)-1])>>>16&255,_=65535&T,!((g=T>>>24)<=c);){if(0===s)break e;s--,u+=r[a++]<<c,c+=8}if(!(240&m)){for(y=g,b=m,w=_;m=(T=n.distcode[w+((u&(1<<y+b)-1)>>y)])>>>16&255,_=65535&T,!(y+(g=T>>>24)<=c);){if(0===s)break e;s--,u+=r[a++]<<c,c+=8}u>>>=y,c-=y,n.back+=y}if(u>>>=g,c-=g,n.back+=g,64&m){e.msg="invalid distance code",n.mode=of;break}n.offset=_,n.extra=15&m,n.mode=24;case 24:if(n.extra){for(C=n.extra;c<C;){if(0===s)break e;s--,u+=r[a++]<<c,c+=8}n.offset+=u&(1<<n.extra)-1,u>>>=n.extra,c-=n.extra,n.back+=n.extra}if(n.offset>n.dmax){e.msg="invalid distance too far back",n.mode=of;break}n.mode=25;case 25:if(0===l)break e;if(f=h-l,n.offset>f){if((f=n.offset-f)>n.whave&&n.sane){e.msg="invalid distance too far back",n.mode=of;break}f>n.wnext?(f-=n.wnext,p=n.wsize-f):p=n.wnext-f,f>n.length&&(f=n.length),v=n.window}else v=i,p=o-n.offset,f=n.length;f>l&&(f=l),l-=f,n.length-=f;do{i[o++]=v[p++]}while(--f);0===n.length&&(n.mode=21);break;case 26:if(0===l)break e;i[o++]=n.length,l--,n.mode=21;break;case 27:if(n.wrap){for(;c<32;){if(0===s)break e;s--,u|=r[a++]<<c,c+=8}if(h-=l,e.total_out+=h,n.total+=h,h&&(e.adler=n.check=n.flags?Gh(n.check,i,h,o-h):Zh(n.check,i,h,o-h)),h=l,(n.flags?u:uf(u))!==n.check){e.msg="incorrect data check",n.mode=of;break}u=0,c=0}n.mode=28;case 28:if(n.wrap&&n.flags){for(;c<32;){if(0===s)break e;s--,u+=r[a++]<<c,c+=8}if(u!==(4294967295&n.total)){e.msg="incorrect length check",n.mode=of;break}u=0,c=0}n.mode=29;case 29:S=1;break e;case of:S=-3;break e;case 31:return-4;default:return nf}return e.next_out=o,e.avail_out=l,e.next_in=a,e.avail_in=s,n.hold=u,n.bits=c,(n.wsize||h!==e.avail_out&&n.mode<of&&(n.mode<27||4!==t))&&yf(e,e.output,e.next_out,h-e.avail_out),d-=e.avail_in,h-=e.avail_out,e.total_in+=d,e.total_out+=h,n.total+=h,n.wrap&&h&&(e.adler=n.check=n.flags?Gh(n.check,i,h,e.next_out-h):Zh(n.check,i,h,e.next_out-h)),e.data_type=n.bits+(n.last?64:0)+(n.mode===af?128:0)+(20===n.mode||15===n.mode?256:0),(0===d&&0===h||4===t)&&S===tf&&(S=-5),S},jh.inflateEnd=function(e){if(!e||!e.state)return nf;var t=e.state;return t.window&&(t.window=null),e.state=null,tf},jh.inflateGetHeader=function(e,t){var n;return e&&e.state&&2&(n=e.state).wrap?(n.head=t,t.done=!1,tf):nf},jh.inflateSetDictionary=function(e,t){var n,r=t.length;return e&&e.state?0!==(n=e.state).wrap&&11!==n.mode?nf:11===n.mode&&Zh(1,t,r,0)!==n.check?-3:yf(e,t,r,r)?(n.mode=31,-4):(n.havedict=1,tf):nf},jh.inflateInfo="pako inflate (from Nodeca project)";var bf={Z_NO_FLUSH:0,Z_PARTIAL_FLUSH:1,Z_SYNC_FLUSH:2,Z_FULL_FLUSH:3,Z_FINISH:4,Z_BLOCK:5,Z_TREES:6,Z_OK:0,Z_STREAM_END:1,Z_NEED_DICT:2,Z_ERRNO:-1,Z_STREAM_ERROR:-2,Z_DATA_ERROR:-3,Z_BUF_ERROR:-5,Z_NO_COMPRESSION:0,Z_BEST_SPEED:1,Z_BEST_COMPRESSION:9,Z_DEFAULT_COMPRESSION:-1,Z_FILTERED:1,Z_HUFFMAN_ONLY:2,Z_RLE:3,Z_FIXED:4,Z_DEFAULT_STRATEGY:0,Z_BINARY:0,Z_TEXT:1,Z_UNKNOWN:2,Z_DEFLATED:8};var wf=jh,xf=kc,Sf=bh,kf=bf,Cf=Ad,Tf=Ah,Af=function(){this.text=0,this.time=0,this.xflags=0,this.os=0,this.extra=null,this.extra_len=0,this.name="",this.comment="",this.hcrc=0,this.done=!1},Mf=Object.prototype.toString;function Ef(e){if(!(this instanceof Ef))return new Ef(e);this.options=xf.assign({chunkSize:16384,windowBits:0,to:""},e||{});var t=this.options;t.raw&&t.windowBits>=0&&t.windowBits<16&&(t.windowBits=-t.windowBits,0===t.windowBits&&(t.windowBits=-15)),!(t.windowBits>=0&&t.windowBits<16)||e&&e.windowBits||(t.windowBits+=32),t.windowBits>15&&t.windowBits<48&&(15&t.windowBits||(t.windowBits|=15)),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new Tf,this.strm.avail_out=0;var n=wf.inflateInit2(this.strm,t.windowBits);if(n!==kf.Z_OK)throw new Error(Cf[n]);if(this.header=new Af,wf.inflateGetHeader(this.strm,this.header),t.dictionary&&("string"==typeof t.dictionary?t.dictionary=Sf.string2buf(t.dictionary):"[object ArrayBuffer]"===Mf.call(t.dictionary)&&(t.dictionary=new Uint8Array(t.dictionary)),t.raw&&(n=wf.inflateSetDictionary(this.strm,t.dictionary))!==kf.Z_OK))throw new Error(Cf[n])}function Of(e,t){var n=new Ef(t);if(n.push(e,!0),n.err)throw n.msg||Cf[n.err];return n.result}Ef.prototype.push=function(e,t){var n,r,i,a,o,s=this.strm,l=this.options.chunkSize,u=this.options.dictionary,c=!1;if(this.ended)return!1;r=t===~~t?t:!0===t?kf.Z_FINISH:kf.Z_NO_FLUSH,"string"==typeof e?s.input=Sf.binstring2buf(e):"[object ArrayBuffer]"===Mf.call(e)?s.input=new Uint8Array(e):s.input=e,s.next_in=0,s.avail_in=s.input.length;do{if(0===s.avail_out&&(s.output=new xf.Buf8(l),s.next_out=0,s.avail_out=l),(n=wf.inflate(s,kf.Z_NO_FLUSH))===kf.Z_NEED_DICT&&u&&(n=wf.inflateSetDictionary(this.strm,u)),n===kf.Z_BUF_ERROR&&!0===c&&(n=kf.Z_OK,c=!1),n!==kf.Z_STREAM_END&&n!==kf.Z_OK)return this.onEnd(n),this.ended=!0,!1;s.next_out&&(0!==s.avail_out&&n!==kf.Z_STREAM_END&&(0!==s.avail_in||r!==kf.Z_FINISH&&r!==kf.Z_SYNC_FLUSH)||("string"===this.options.to?(i=Sf.utf8border(s.output,s.next_out),a=s.next_out-i,o=Sf.buf2string(s.output,i),s.next_out=a,s.avail_out=l-a,a&&xf.arraySet(s.output,s.output,i,a,0),this.onData(o)):this.onData(xf.shrinkBuf(s.output,s.next_out)))),0===s.avail_in&&0===s.avail_out&&(c=!0)}while((s.avail_in>0||0===s.avail_out)&&n!==kf.Z_STREAM_END);return n===kf.Z_STREAM_END&&(r=kf.Z_FINISH),r===kf.Z_FINISH?(n=wf.inflateEnd(this.strm),this.onEnd(n),this.ended=!0,n===kf.Z_OK):r!==kf.Z_SYNC_FLUSH||(this.onEnd(kf.Z_OK),s.avail_out=0,!0)},Ef.prototype.onData=function(e){this.chunks.push(e)},Ef.prototype.onEnd=function(e){e===kf.Z_OK&&("string"===this.options.to?this.result=this.chunks.join(""):this.result=xf.flattenChunks(this.chunks)),this.chunks=[],this.err=e,this.msg=this.strm.msg},qh.Inflate=Ef,qh.inflate=Of,qh.inflateRaw=function(e,t){return(t=t||{}).raw=!0,Of(e,t)},qh.ungzip=Of;var Lf={};(0,kc.assign)(Lf,Cc,qh,bf);var zf=Lf,Nf=!1,If=0,Pf=0,Df=960,Bf=375,Rf=750;function Ff(e,t){var n=Number(e);return isNaN(n)?t:n}var qf=mc(0,((e,t)=>{var n;if(0===If&&(!function(){var{platform:e,pixelRatio:t,windowWidth:n}=_c();If=n,Pf=t,Nf="ios"===e}(),n=__uniConfig.globalStyle||{},Df=Ff(n.rpxCalcMaxDeviceWidth,960),Bf=Ff(n.rpxCalcBaseDeviceWidth,375),Rf=Ff(n.rpxCalcBaseDeviceWidth,750)),0===(e=Number(e)))return 0;var r=t||If,i=e/750*(r=e===Rf||r<=Df?r:Bf);return i<0&&(i=-i),0===(i=Math.floor(i+1e-4))&&(i=1!==Pf&&Nf?.5:1),e<0?-i:i})),jf={};jf.f={}.propertyIsEnumerable;var Vf,$f=E,Hf=Ve,Wf=Z,Uf=jf.f,Yf=(Vf=!1,function(e){for(var t,n=Wf(e),r=Hf(n),i=r.length,a=0,o=[];i>a;)t=r[a++],$f&&!Uf.call(n,t)||o.push(Vf?[t,n[t]]:n[t]);return o});be(be.S,"Object",{values:function(e){return Yf(e)}});var Xf="setPageMeta",Zf="loadFontFace",Gf="pageScrollTo",Kf=function(){if("object"==typeof window)if("IntersectionObserver"in window&&"IntersectionObserverEntry"in window&&"intersectionRatio"in window.IntersectionObserverEntry.prototype)"isIntersecting"in window.IntersectionObserverEntry.prototype||Object.defineProperty(window.IntersectionObserverEntry.prototype,"isIntersecting",{get:function(){return this.intersectionRatio>0}});else{var e=function(){for(var e=window.document,t=i(e);t;)t=i(e=t.ownerDocument);return e}(),t=[],n=null,r=null;o.prototype.THROTTLE_TIMEOUT=100,o.prototype.POLL_INTERVAL=null,o.prototype.USE_MUTATION_OBSERVER=!0,o._setupCrossOriginUpdater=function(){return n||(n=function(e,n){r=e&&n?d(e,n):{top:0,bottom:0,left:0,right:0,width:0,height:0},t.forEach((function(e){e._checkForIntersections()}))}),n},o._resetCrossOriginUpdater=function(){n=null,r=null},o.prototype.observe=function(e){if(!this._observationTargets.some((function(t){return t.element==e}))){if(!e||1!=e.nodeType)throw new Error("target must be an Element");this._registerInstance(),this._observationTargets.push({element:e,entry:null}),this._monitorIntersections(e.ownerDocument),this._checkForIntersections()}},o.prototype.unobserve=function(e){this._observationTargets=this._observationTargets.filter((function(t){return t.element!=e})),this._unmonitorIntersections(e.ownerDocument),0==this._observationTargets.length&&this._unregisterInstance()},o.prototype.disconnect=function(){this._observationTargets=[],this._unmonitorAllIntersections(),this._unregisterInstance()},o.prototype.takeRecords=function(){var e=this._queuedEntries.slice();return this._queuedEntries=[],e},o.prototype._initThresholds=function(e){var t=e||[0];return Array.isArray(t)||(t=[t]),t.sort().filter((function(e,t,n){if("number"!=typeof e||isNaN(e)||e<0||e>1)throw new Error("threshold must be a number between 0 and 1 inclusively");return e!==n[t-1]}))},o.prototype._parseRootMargin=function(e){var t=(e||"0px").split(/\s+/).map((function(e){var t=/^(-?\d*\.?\d+)(px|%)$/.exec(e);if(!t)throw new Error("rootMargin must be specified in pixels or percent");return{value:parseFloat(t[1]),unit:t[2]}}));return t[1]=t[1]||t[0],t[2]=t[2]||t[0],t[3]=t[3]||t[1],t},o.prototype._monitorIntersections=function(t){var n=t.defaultView;if(n&&-1==this._monitoringDocuments.indexOf(t)){var r=this._checkForIntersections,a=null,o=null;this.POLL_INTERVAL?a=n.setInterval(r,this.POLL_INTERVAL):(s(n,"resize",r,!0),s(t,"scroll",r,!0),this.USE_MUTATION_OBSERVER&&"MutationObserver"in n&&(o=new n.MutationObserver(r)).observe(t,{attributes:!0,childList:!0,characterData:!0,subtree:!0})),this._monitoringDocuments.push(t),this._monitoringUnsubscribes.push((function(){var e=t.defaultView;e&&(a&&e.clearInterval(a),l(e,"resize",r,!0)),l(t,"scroll",r,!0),o&&o.disconnect()}));var u=this.root&&(this.root.ownerDocument||this.root)||e;if(t!=u){var c=i(t);c&&this._monitorIntersections(c.ownerDocument)}}},o.prototype._unmonitorIntersections=function(t){var n=this._monitoringDocuments.indexOf(t);if(-1!=n){var r=this.root&&(this.root.ownerDocument||this.root)||e;if(!this._observationTargets.some((function(e){var n=e.element.ownerDocument;if(n==t)return!0;for(;n&&n!=r;){var a=i(n);if((n=a&&a.ownerDocument)==t)return!0}return!1}))){var a=this._monitoringUnsubscribes[n];if(this._monitoringDocuments.splice(n,1),this._monitoringUnsubscribes.splice(n,1),a(),t!=r){var o=i(t);o&&this._unmonitorIntersections(o.ownerDocument)}}}},o.prototype._unmonitorAllIntersections=function(){var e=this._monitoringUnsubscribes.slice(0);this._monitoringDocuments.length=0,this._monitoringUnsubscribes.length=0;for(var t=0;t<e.length;t++)e[t]()},o.prototype._checkForIntersections=function(){if(this.root||!n||r){var e=this._rootIsInDom(),t=e?this._getRootRect():{top:0,bottom:0,left:0,right:0,width:0,height:0};this._observationTargets.forEach((function(r){var i=r.element,o=u(i),s=this._rootContainsTarget(i),l=r.entry,c=e&&s&&this._computeTargetAndRootIntersection(i,o,t),d=null;this._rootContainsTarget(i)?n&&!this.root||(d=t):d={top:0,bottom:0,left:0,right:0,width:0,height:0};var h=r.entry=new a({time:window.performance&&performance.now&&performance.now(),target:i,boundingClientRect:o,rootBounds:d,intersectionRect:c});l?e&&s?this._hasCrossedThreshold(l,h)&&this._queuedEntries.push(h):l&&l.isIntersecting&&this._queuedEntries.push(h):this._queuedEntries.push(h)}),this),this._queuedEntries.length&&this._callback(this.takeRecords(),this)}},o.prototype._computeTargetAndRootIntersection=function(t,i,a){if("none"!=window.getComputedStyle(t).display){for(var o,s,l,c,h,p,v,g,m=i,_=f(t),y=!1;!y&&_;){var b=null,w=1==_.nodeType?window.getComputedStyle(_):{};if("none"==w.display)return null;if(_==this.root||9==_.nodeType)if(y=!0,_==this.root||_==e)n&&!this.root?!r||0==r.width&&0==r.height?(_=null,b=null,m=null):b=r:b=a;else{var x=f(_),S=x&&u(x),k=x&&this._computeTargetAndRootIntersection(x,S,a);S&&k?(_=x,b=d(S,k)):(_=null,m=null)}else{var C=_.ownerDocument;_!=C.body&&_!=C.documentElement&&"visible"!=w.overflow&&(b=u(_))}if(b&&(o=b,s=m,l=void 0,c=void 0,h=void 0,p=void 0,v=void 0,g=void 0,l=Math.max(o.top,s.top),c=Math.min(o.bottom,s.bottom),h=Math.max(o.left,s.left),p=Math.min(o.right,s.right),g=c-l,m=(v=p-h)>=0&&g>=0&&{top:l,bottom:c,left:h,right:p,width:v,height:g}||null),!m)break;_=_&&f(_)}return m}},o.prototype._getRootRect=function(){var t;if(this.root&&!p(this.root))t=u(this.root);else{var n=p(this.root)?this.root:e,r=n.documentElement,i=n.body;t={top:0,left:0,right:r.clientWidth||i.clientWidth,width:r.clientWidth||i.clientWidth,bottom:r.clientHeight||i.clientHeight,height:r.clientHeight||i.clientHeight}}return this._expandRectByRootMargin(t)},o.prototype._expandRectByRootMargin=function(e){var t=this._rootMarginValues.map((function(t,n){return"px"==t.unit?t.value:t.value*(n%2?e.width:e.height)/100})),n={top:e.top-t[0],right:e.right+t[1],bottom:e.bottom+t[2],left:e.left-t[3]};return n.width=n.right-n.left,n.height=n.bottom-n.top,n},o.prototype._hasCrossedThreshold=function(e,t){var n=e&&e.isIntersecting?e.intersectionRatio||0:-1,r=t.isIntersecting?t.intersectionRatio||0:-1;if(n!==r)for(var i=0;i<this.thresholds.length;i++){var a=this.thresholds[i];if(a==n||a==r||a<n!=a<r)return!0}},o.prototype._rootIsInDom=function(){return!this.root||h(e,this.root)},o.prototype._rootContainsTarget=function(t){var n=this.root&&(this.root.ownerDocument||this.root)||e;return h(n,t)&&(!this.root||n==t.ownerDocument)},o.prototype._registerInstance=function(){t.indexOf(this)<0&&t.push(this)},o.prototype._unregisterInstance=function(){var e=t.indexOf(this);-1!=e&&t.splice(e,1)},window.IntersectionObserver=o,window.IntersectionObserverEntry=a}function i(e){try{return e.defaultView&&e.defaultView.frameElement||null}catch(t){return null}}function a(e){this.time=e.time,this.target=e.target,this.rootBounds=c(e.rootBounds),this.boundingClientRect=c(e.boundingClientRect),this.intersectionRect=c(e.intersectionRect||{top:0,bottom:0,left:0,right:0,width:0,height:0}),this.isIntersecting=!!e.intersectionRect;var t=this.boundingClientRect,n=t.width*t.height,r=this.intersectionRect,i=r.width*r.height;this.intersectionRatio=n?Number((i/n).toFixed(4)):this.isIntersecting?1:0}function o(e,t){var n=t||{};if("function"!=typeof e)throw new Error("callback must be a function");if(n.root&&1!=n.root.nodeType&&9!=n.root.nodeType)throw new Error("root must be a Document or Element");this._checkForIntersections=function(e,t){var n=null;return function(){n||(n=setTimeout((function(){e(),n=null}),t))}}(this._checkForIntersections.bind(this),this.THROTTLE_TIMEOUT),this._callback=e,this._observationTargets=[],this._queuedEntries=[],this._rootMarginValues=this._parseRootMargin(n.rootMargin),this.thresholds=this._initThresholds(n.threshold),this.root=n.root||null,this.rootMargin=this._rootMarginValues.map((function(e){return e.value+e.unit})).join(" "),this._monitoringDocuments=[],this._monitoringUnsubscribes=[]}function s(e,t,n,r){"function"==typeof e.addEventListener?e.addEventListener(t,n,r):"function"==typeof e.attachEvent&&e.attachEvent("on"+t,n)}function l(e,t,n,r){"function"==typeof e.removeEventListener?e.removeEventListener(t,n,r):"function"==typeof e.detatchEvent&&e.detatchEvent("on"+t,n)}function u(e){var t;try{t=e.getBoundingClientRect()}catch(n){}return t?(t.width&&t.height||(t={top:t.top,right:t.right,bottom:t.bottom,left:t.left,width:t.right-t.left,height:t.bottom-t.top}),t):{top:0,bottom:0,left:0,right:0,width:0,height:0}}function c(e){return!e||"x"in e?e:{top:e.top,y:e.top,bottom:e.bottom,left:e.left,x:e.left,right:e.right,width:e.width,height:e.height}}function d(e,t){var n=t.top-e.top,r=t.left-e.left;return{top:n,left:r,height:t.height,width:t.width,bottom:n+t.height,right:r+t.width}}function h(e,t){for(var n=t;n;){if(n==e)return!0;n=f(n)}return!1}function f(t){var n=t.parentNode;return 9==t.nodeType&&t!=e?i(t):(n&&n.assignedSlot&&(n=n.assignedSlot.parentNode),n&&11==n.nodeType&&n.host?n.host:n)}function p(e){return e&&9===e.nodeType}};function Jf(e){var{bottom:t,height:n,left:r,right:i,top:a,width:o}=e||{};return{bottom:t,height:n,left:r,right:i,top:a,width:o}}function Qf(e){var{intersectionRatio:t,boundingClientRect:{height:n,width:r},intersectionRect:{height:i,width:a}}=e;return 0!==t?t:i===n?a/r:i/n}const ep=Object.freeze(Object.defineProperty({__proto__:null,navigateBack:function(e){UniViewJSBridge.invokeServiceMethod("navigateBack",e)},navigateTo:function(e){UniViewJSBridge.invokeServiceMethod("navigateTo",e)},reLaunch:function(e){UniViewJSBridge.invokeServiceMethod("reLaunch",e)},redirectTo:function(e){UniViewJSBridge.invokeServiceMethod("redirectTo",e)},switchTab:function(e){UniViewJSBridge.invokeServiceMethod("switchTab",e)},upx2px:qf},Symbol.toStringTag,{value:"Module"}));function tp(e,t){if(t)return un(t,"a")&&(t.a=e(t.a)),un(t,"e")&&(t.e=e(t.e,!1)),un(t,"w")&&(t.w=function(e,t){var n={};return e.forEach((e=>{var[r,[i,a]]=e;n[t(r)]=[t(i),a]})),n}(t.w,e)),un(t,"s")&&(t.s=e(t.s)),un(t,"t")&&(t.t=e(t.t)),t}var np=new Map;function rp(e){return np.get(e)}function ip(e){return np.delete(e)}var ap=new Set;function op(e,t){ap.add(function(e,t){return e.priority=t,e}(e,t))}function sp(e,t){var n=window["__"+ar],r=n&&n[e];return r||(t&&t.__renderjsInstances?t.__renderjsInstances[e]:void 0)}var lp=6;function up(e,t,n){var[r,i,a,o]=dp(t),s=cp(e,r);if(cn(n)||cn(o)){var[l,u]=a.split(".");return hp(s,i,l,u,o)}return function(e,t,n){var r=sp(t,e);if(!r)return console.error(pr("wxs","module "+n+" not found"));return hr(r,n.slice(n.indexOf(".")+1))}(s,i,a)}function cp(e,t){if(e.__ownerId===t)return e;for(var n=e.parentElement;n;){if(n.__ownerId===t)return n;n=n.parentElement}return e}function dp(e){return JSON.parse(e.slice(lp))}function hp(e,t,n,r,i){var a=sp(t,e);if(!a)return console.error(pr("wxs","module "+n+" not found"));var o=a[r];return fn(o)?o.apply(a,i):console.error(n+"."+r+" is not a function")}function fp(e,t,n){var r=n;return n=>{try{!function(e,t,n,r){var[i,a,o]=dp(e),s=cp(t,i),[l,u]=o.split(".");hp(s,a,l,u,[n,r,tc(rc(s)),tc(rc(t))])}(t,e.$,n,r)}catch(i){console.error(i)}r=n}}function pp(e,t){var n=rc(t);return Object.defineProperty(e,"instance",{get:()=>tc(n)}),e}function vp(e,t){Object.keys(t).forEach((n=>{!function(e,t){var n=function(e){var t=window["__"+or],n=t&&t[e];if(!n)return console.error(pr("renderjs",e+" not found"));return n}(t);if(!n)return;var r=e.$;(r.__renderjsInstances||(r.__renderjsInstances={}))[t]=function(e,t){return t=t.default||t,t.render=()=>{},vu(t).mixin({mounted(){this.$ownerInstance=tc(rc(e))}}).mount(document.createElement("div"))}(r,n)}(e,t[n])}))}function gp(e,t){return pn(e)?(0===e.indexOf(ir)?e=JSON.parse(e.slice(7)):0===e.indexOf(rr)&&(e=up(t,e)),e):e}function mp(e){return 0===e.indexOf("--")}class _p{constructor(e,t,n,r){this.isMounted=!1,this.isUnmounted=!1,this.$hasWxsProps=!1,this.$children=[],this.id=e,this.tag=t,this.pid=n,r&&(this.$=r),this.$wxsProps=new Map;var i=this.$parent=function(e){return np.get(e)}(n);i&&i.appendUniChild(this)}init(e){un(e,"t")&&(this.$.textContent=e.t)}setText(e){this.$.textContent=e,this.updateView()}insert(e,t,n){n&&this.init(n,!1);var r=this.$,i=rp(e);-1===t?i.appendChild(r):i.insertBefore(r,rp(t).$),this.isMounted=!0}remove(){this.removeUniParent();var{$:e}=this;e.parentNode.removeChild(e),this.isUnmounted=!0,ip(this.id),function(e){var{__renderjsInstances:t}=e.$;t&&Object.keys(t).forEach((e=>{t[e].$.appContext.app.unmount()}))}(this),this.removeUniChildren(),this.updateView()}appendChild(e){var t=this.$.appendChild(e);return this.updateView(!0),t}insertBefore(e,t){var n=this.$.insertBefore(e,t);return this.updateView(!0),n}appendUniChild(e){this.$children.push(e)}removeUniChild(e){var t=this.$children.indexOf(e);t>=0&&this.$children.splice(t,1)}removeUniParent(){var{$parent:e}=this;e&&(e.removeUniChild(this),this.$parent=void 0)}removeUniChildren(){this.$children.forEach((e=>e.remove())),this.$children.length=0}setWxsProps(e){Object.keys(e).forEach((t=>{if(0===t.indexOf(Or)){var n=t.replace(Or,""),r=gp(e[n]),i=fp(this,e[t],r);op((()=>i(r)),4),this.$wxsProps.set(t,i),delete e[t],delete e[n],this.$hasWxsProps=!0}}))}addWxsEvents(e){Object.keys(e).forEach((t=>{var[n,r]=e[t];this.addWxsEvent(t,n,r)}))}addWxsEvent(e,t,n){}wxsPropsInvoke(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=this.$hasWxsProps&&this.$wxsProps.get(Or+e);if(r)return op((()=>n?bo((()=>r(t))):r(t)),4),!0}updateView(e){(this.isMounted||e)&&window.dispatchEvent(new CustomEvent("updateview"))}}function yp(e,t){var{__wxsAddClass:n,__wxsRemoveClass:r}=e;r&&r.length&&(t=t.split(/\s+/).filter((e=>-1===r.indexOf(e))).join(" "),r.length=0),n&&n.length&&(t=t+" "+n.join(" ")),e.className=t}function bp(e){return Cp(Op(e))}var wp,xp,Sp,kp=/url\(\s*'?"?([a-zA-Z0-9\.\-\_\/]+\.(jpg|gif|png))"?'?\s*\)/,Cp=e=>{if(pn(e)&&-1!==e.indexOf("url(")){var t=e.match(kp);t&&3===t.length&&(e=e.replace(t[1],yc(t[1])))}return e},{unit:Tp,unitRatio:Ap,unitPrecision:Mp}={unit:"rem",unitRatio:10/320,unitPrecision:5},Ep=(wp=Tp,xp=Ap,Sp=Mp,e=>e.replace(_r,((e,t)=>{if(!t)return e;if(1===xp)return"".concat(t).concat(wp);var n,r,i,a,o=(n=parseFloat(t)*xp,r=Sp,i=Math.pow(10,r+1),a=Math.floor(n*i),10*Math.round(a/10)/i);return 0===o?"0":"".concat(o).concat(wp)}))),Op=e=>pn(e)?Ep(e):e,Lp=["Webkit"],zp={};function Np(e,t){var n=zp[t];if(n)return n;var r=Tn(t);if("filter"!==r&&r in e)return zp[t]=r;r=En(r);for(var i=0;i<Lp.length;i++){var a=Lp[i]+r;if(a in e)return zp[t]=a}return t}function Ip(e,t){var n=e.style;if(pn(t))""===t?e.removeAttribute("style"):n.cssText=bp(t);else for(var r in t)Dp(n,r,t[r]);var{__wxsStyle:i}=e;if(i)for(var a in i)Dp(n,a,i[a])}var Pp=/\s*!important$/;function Dp(e,t,n){if(cn(n))n.forEach((n=>Dp(e,t,n)));else if(n=bp(n),t.startsWith("--"))e.setProperty(t,n);else{var r=Np(e,t);Pp.test(n)?e.setProperty(Mn(r),n.replace(Pp,""),"important"):e[r]=n}}function Bp(e,t){var n=e.__listeners[t];n&&e.removeEventListener(t,n)}function Rp(e,t){if(e.__listeners[t])return!0}function Fp(e,t,n){var[r,i]=Sr(t);-1===n?Bp(e,r):Rp(e,r)||e.addEventListener(r,e.__listeners[r]=qp(e.__id,n,i),i)}function qp(e,t,n){var r=t=>{var[r]=lc(t);r.type=function(e,t){return t&&(t.capture&&(e+="Capture"),t.once&&(e+="Once"),t.passive&&(e+="Passive")),"on".concat(En(Tn(e)))}(t.type,n),UniViewJSBridge.publishHandler(hc,[[$r,e,r]])};return t?fu(r,jp(t)):r}function jp(e){var t=[];return e&kr.prevent&&t.push("prevent"),e&kr.self&&t.push("self"),e&kr.stop&&t.push("stop"),t}function Vp(e,t,n){var r=n=>{!function(e,t,n){var[r,i,a]=dp(t),[o,s]=a.split("."),l=cp(e,r);hp(l,i,o,s,[pp(n,e),tc(rc(l))])}(function(e){return!!e.addWxsEvent}(e)?e.$:e,t,lc(n)[0])};return n?fu(r,jp(n)):r}function $p(e,t){e._vod="none"===e.style.display?"":e.style.display,e.style.display=t?e._vod:"none"}class Hp extends _p{constructor(e,t,n,r,i){var a=arguments.length>5&&void 0!==arguments[5]?arguments[5]:[];super(e,t.tagName,n,t),this.$props=qa({}),this.$.__id=e,this.$.__listeners=Object.create(null),this.$propNames=a,this._update=this.update.bind(this),this.init(i),this.insert(n,r)}init(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];un(e,"a")&&this.setAttrs(e.a),un(e,"s")&&this.setAttr("style",e.s),un(e,"e")&&this.addEvents(e.e),un(e,"w")&&this.addWxsEvents(e.w),super.init(e),t&&(Vo(this.$props,(()=>{op(this._update,1)}),{flush:"sync"}),this.update(!0))}setAttrs(e){this.setWxsProps(e),Object.keys(e).forEach((t=>{this.setAttr(t,e[t])}))}addEvents(e){Object.keys(e).forEach((t=>{this.addEvent(t,e[t])}))}addWxsEvent(e,t,n){!function(e,t,n,r){var[i,a]=Sr(t);-1===r?Bp(e,i):Rp(e,i)||e.addEventListener(i,e.__listeners[i]=Vp(e,n,r),a)}(this.$,e,t,n)}addEvent(e,t){Fp(this.$,e,t)}removeEvent(e){Fp(this.$,e,-1)}setAttr(e,t){e===Cr?yp(this.$,t):e===Tr?Ip(this.$,t):e===Ar?$p(this.$,t):e===Mr?this.$.__ownerId=t:e===Er?op((()=>vp(this,t)),3):"innerHTML"===e?this.$.innerHTML=t:"textContent"===e?this.setText(t):this.setAttribute(e,t),this.updateView()}removeAttr(e){e===Cr?yp(this.$,""):e===Tr?Ip(this.$,""):this.removeAttribute(e),this.updateView()}setAttribute(e,t){t=gp(t,this.$),-1!==this.$propNames.indexOf(e)?this.$props[e]=t:mp(e)?this.$.style.setProperty(e,bp(t)):this.wxsPropsInvoke(e,t)||this.$.setAttribute(e,t)}removeAttribute(e){-1!==this.$propNames.indexOf(e)?delete this.$props[e]:mp(e)?this.$.style.removeProperty(e):this.$.removeAttribute(e)}update(){}}function Wp(e){return/^-?\d+[ur]px$/i.test(e)?e.replace(/(^-?\d+)[ur]px$/i,((e,t)=>"".concat(uni.upx2px(parseFloat(t)),"px"))):/^-?[\d\.]+$/.test(e)?"".concat(e,"px"):e||""}function Up(e){var t=e.animation;if(t&&t.actions&&t.actions.length){var n=0,r=t.actions,i=t.actions.length;setTimeout((()=>{a()}),0)}function a(){var t=r[n],o=t.option.transition,s=function(e){var t=["matrix","matrix3d","scale","scale3d","rotate3d","skew","translate","translate3d"],n=["scaleX","scaleY","scaleZ","rotate","rotateX","rotateY","rotateZ","skewX","skewY","translateX","translateY","translateZ"],r=["opacity","background-color"],i=["width","height","left","right","top","bottom"],a=e.animates,o=e.option,s=o.transition,l={},u=[];return a.forEach((e=>{var a=e.type,o=[...e.args];if(t.concat(n).includes(a))a.startsWith("rotate")||a.startsWith("skew")?o=o.map((e=>parseFloat(e)+"deg")):a.startsWith("translate")&&(o=o.map(Wp)),n.indexOf(a)>=0&&(o.length=1),u.push("".concat(a,"(").concat(o.join(","),")"));else if(r.concat(i).includes(o[0])){a=o[0];var s=o[1];l[a]=i.includes(a)?Wp(s):s}})),l.transform=l.webkitTransform=u.join(" "),l.transition=l.webkitTransition=Object.keys(l).map((e=>"".concat(function(e){return e.replace(/[A-Z]/g,(e=>"-".concat(e.toLowerCase()))).replace("webkit","-webkit")}(e)," ").concat(s.duration,"ms ").concat(s.timingFunction," ").concat(s.delay,"ms"))).join(","),l.transformOrigin=l.webkitTransformOrigin=o.transformOrigin,l}(t);Object.keys(s).forEach((t=>{e.$el.style[t]=s[t]})),(n+=1)<i&&setTimeout(a,o.duration+o.delay)}}const Yp={props:["animation"],watch:{animation:{deep:!0,handler(){Up(this)}}},mounted(){Up(this)}};var Xp=e=>{e.__reserved=!0;var{props:t,mixins:n}=e;return t&&t.animation||(n||(e.mixins=[])).push(Yp),Zp(e)},Zp=e=>(e.__reserved=!0,e.compatConfig={MODE:3},Zo(e));function Gp(e){return e.__wwe=!0,e}function Kp(e,t){return(n,r,i)=>{e.value&&t(n,function(e,t,n,r){var i;return i=br(n),{type:r.type||e,timeStamp:t.timeStamp||0,target:i,currentTarget:i,detail:r}}(n,r,e.value,i||{}))}}function Jp(e){var t,n,r=to(!1),i=!1;function a(){requestAnimationFrame((()=>{clearTimeout(n),n=setTimeout((()=>{r.value=!1}),parseInt(e.hoverStayTime))}))}function o(n){n._hoverPropagationStopped||e.hoverClass&&"none"!==e.hoverClass&&!e.disabled&&(e.hoverStopPropagation&&(n._hoverPropagationStopped=!0),i=!0,t=setTimeout((()=>{r.value=!0,i||a()}),parseInt(e.hoverStartTime)))}function s(){i=!1,r.value&&a()}function l(){s(),window.removeEventListener("mouseup",l)}return{hovering:r,binding:{onTouchstartPassive:Gp((function(e){e.touches.length>1||o(e)})),onMousedown:Gp((function(e){i||(o(e),window.addEventListener("mouseup",l))})),onTouchend:Gp((function(){s()})),onMouseup:Gp((function(){i&&l()})),onTouchcancel:Gp((function(){i=!1,r.value=!1,clearTimeout(t)}))}}}function Qp(e,t){return pn(t)&&(t=[t]),t.reduce(((t,n)=>(e[n]&&(t[n]=!0),t)),Object.create(null))}var ev=Eu("uf");const tv=Xp({name:"Form",emits:["submit","reset"],setup(e,t){var n,r,{slots:i,emit:a}=t,o=to(null);return n=Kp(o,a),r=[],Ps(ev,{addField(e){r.push(e)},removeField(e){r.splice(r.indexOf(e),1)},submit(e){n("submit",e,{value:r.reduce(((e,t)=>{if(t.submit){var[n,r]=t.submit();n&&(e[n]=r)}return e}),Object.create(null))})},reset(e){r.forEach((e=>e.reset&&e.reset())),n("reset",e)}}),()=>ml("uni-form",{ref:o},[ml("span",null,[i.default&&i.default()])],512)}});var nv={for:{type:String,default:""}},rv=Eu("ul");const iv=Xp({name:"Label",props:nv,setup(e,t){var{slots:n}=t,r=to(null),i=Du(),a=function(){var e=[];return Ps(rv,{addHandler(t){e.push(t)},removeHandler(t){e.splice(e.indexOf(t),1)}}),e}(),o=jl((()=>e.for||n.default&&n.default.length)),s=Gp((t=>{var n=t.target,r=/^uni-(checkbox|radio|switch)-/.test(n.className);r||(r=/^uni-(checkbox|radio|switch|button)$|^(svg|path)$/i.test(n.tagName)),r||(e.for?UniViewJSBridge.emit("uni-label-click-"+i+"-"+e.for,t,!0):a.length&&a[0](t,!0))}));return()=>ml("uni-label",{ref:r,class:{"uni-label-pointer":o},onClick:s},[n.default&&n.default()],10,["onClick"])}});function av(e,t){ov(e.id,t),Vo((()=>e.id),((e,n)=>{sv(n,t,!0),ov(e,t,!0)})),us((()=>{sv(e.id,t)}))}function ov(e,t,n){var r=Du();n&&!e||wn(t)&&Object.keys(t).forEach((i=>{n?0!==i.indexOf("@")&&0!==i.indexOf("uni-")&&UniViewJSBridge.on("uni-".concat(i,"-").concat(r,"-").concat(e),t[i]):0===i.indexOf("uni-")?UniViewJSBridge.on(i,t[i]):e&&UniViewJSBridge.on("uni-".concat(i,"-").concat(r,"-").concat(e),t[i])}))}function sv(e,t,n){var r=Du();n&&!e||wn(t)&&Object.keys(t).forEach((i=>{n?0!==i.indexOf("@")&&0!==i.indexOf("uni-")&&UniViewJSBridge.off("uni-".concat(i,"-").concat(r,"-").concat(e),t[i]):0===i.indexOf("uni-")?UniViewJSBridge.off(i,t[i]):e&&UniViewJSBridge.off("uni-".concat(i,"-").concat(r,"-").concat(e),t[i])}))}const lv=Xp({name:"Button",props:{id:{type:String,default:""},hoverClass:{type:String,default:"button-hover"},hoverStartTime:{type:[Number,String],default:20},hoverStayTime:{type:[Number,String],default:70},hoverStopPropagation:{type:Boolean,default:!1},disabled:{type:[Boolean,String],default:!1},formType:{type:String,default:""},openType:{type:String,default:""},loading:{type:[Boolean,String],default:!1},plain:{type:[Boolean,String],default:!1}},setup(e,t){var{slots:n}=t,r=to(null);hi();var i=Ds(ev,!1),{hovering:a,binding:o}=Jp(e),{t:s}=ui(),l=Gp(((t,n)=>{if(e.disabled)return t.stopImmediatePropagation();n&&r.value.click();var a=e.formType;if(a){if(!i)return;"submit"===a?i.submit(t):"reset"===a&&i.reset(t)}else{var o,l,u;"feedback"===e.openType&&(o=s("uni.button.feedback.title"),l=s("uni.button.feedback.send"),(u=plus.webview.create("https://service.dcloud.net.cn/uniapp/feedback.html","feedback",{titleNView:{titleText:o,autoBackButton:!0,backgroundColor:"#F7F7F7",titleColor:"#007aff",buttons:[{text:l,color:"#007aff",fontSize:"16px",fontWeight:"bold",onclick:function(){u.evalJS('typeof mui !== "undefined" && mui.trigger(document.getElementById("submit"),"tap")')}}]}})).show("slide-in-right"))}})),u=Ds(rv,!1);return u&&(u.addHandler(l),ls((()=>{u.removeHandler(l)}))),av(e,{"label-click":l}),()=>{var t=e.hoverClass,i=Qp(e,"disabled"),s=Qp(e,"loading"),u=Qp(e,"plain"),c=t&&"none"!==t;return ml("uni-button",Sl({ref:r,onClick:l,id:e.id,class:c&&a.value?t:""},c&&o,i,s,u),[n.default&&n.default()],16,["onClick","id"])}}});const uv=Xp({name:"ResizeSensor",props:{initial:{type:Boolean,default:!1}},emits:["resize"],setup(e,t){var{emit:n}=t,r=to(null),i=function(e){return()=>{var{firstElementChild:t,lastElementChild:n}=e.value;t.scrollLeft=1e5,t.scrollTop=1e5,n.scrollLeft=1e5,n.scrollTop=1e5}}(r),a=function(e,t,n){var r=qa({width:-1,height:-1});return Vo((()=>on({},r)),(e=>t("resize",e))),()=>{var t=e.value;t&&(r.width=t.offsetWidth,r.height=t.offsetHeight,n())}}(r,n,i);return function(e,t,n,r){Jo(r),as((()=>{t.initial&&bo(n);var i=e.value;i.offsetParent!==i.parentElement&&(i.parentElement.style.position="relative"),"AnimationEvent"in window||r()}))}(r,e,a,i),()=>ml("uni-resize-sensor",{ref:r,onAnimationstartOnce:a},[ml("div",{onScroll:a},[ml("div",null,null)],40,["onScroll"]),ml("div",{onScroll:a},[ml("div",null,null)],40,["onScroll"])],40,["onAnimationstartOnce"])}});var cv=function(){var e=document.createElement("canvas");e.height=e.width=0;var t=e.getContext("2d"),n=t.backingStorePixelRatio||t.webkitBackingStorePixelRatio||t.mozBackingStorePixelRatio||t.msBackingStorePixelRatio||t.oBackingStorePixelRatio||t.backingStorePixelRatio||1;return(window.devicePixelRatio||1)/n}();function dv(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],n=t?cv:1;e.width=e.offsetWidth*n,e.height=e.offsetHeight*n,e.getContext("2d").__hidpi__=t}var hv=!1;var fv,pv=dr((()=>function(){if(!hv){hv=!0;var e=CanvasRenderingContext2D.prototype;e.drawImageByCanvas=function(e){return function(t,n,r,i,a,o,s,l,u,c){if(!this.__hidpi__)return e.apply(this,arguments);n*=cv,r*=cv,i*=cv,a*=cv,o*=cv,s*=cv,l=c?l*cv:l,u=c?u*cv:u,e.call(this,t,n,r,i,a,o,s,l,u)}}(e.drawImage),1!==cv&&(function(e,t){for(var n in e)un(e,n)&&t(e[n],n)}({fillRect:"all",clearRect:"all",strokeRect:"all",moveTo:"all",lineTo:"all",arc:[0,1,2],arcTo:"all",bezierCurveTo:"all",isPointinPath:"all",isPointinStroke:"all",quadraticCurveTo:"all",rect:"all",translate:"all",createRadialGradient:"all",createLinearGradient:"all",transform:[4,5],setTransform:[4,5]},(function(t,n){e[n]=function(e){return function(){if(!this.__hidpi__)return e.apply(this,arguments);var n=Array.prototype.slice.call(arguments);if("all"===t)n=n.map((function(e){return e*cv}));else if(Array.isArray(t))for(var r=0;r<t.length;r++)n[t[r]]*=cv;return e.apply(this,n)}}(e[n])})),e.stroke=function(e){return function(){if(!this.__hidpi__)return e.apply(this,arguments);this.lineWidth*=cv,e.apply(this,arguments),this.lineWidth/=cv}}(e.stroke),e.fillText=function(e){return function(){if(!this.__hidpi__)return e.apply(this,arguments);var t=Array.prototype.slice.call(arguments);t[1]*=cv,t[2]*=cv,t[3]&&"number"==typeof t[3]&&(t[3]*=cv);var n=this.font;this.font=n.replace(/(\d+\.?\d*)(px|em|rem|pt)/g,(function(e,t,n){return t*cv+n})),e.apply(this,t),this.font=n}}(e.fillText),e.strokeText=function(e){return function(){if(!this.__hidpi__)return e.apply(this,arguments);var t=Array.prototype.slice.call(arguments);t[1]*=cv,t[2]*=cv,t[3]&&"number"==typeof t[3]&&(t[3]*=cv);var n=this.font;this.font=n.replace(/(\d+\.?\d*)(px|em|rem|pt)/g,(function(e,t,n){return t*cv+n})),e.apply(this,t),this.font=n}}(e.strokeText),e.drawImage=function(e){return function(){if(!this.__hidpi__)return e.apply(this,arguments);this.scale(cv,cv),e.apply(this,arguments),this.scale(1/cv,1/cv)}}(e.drawImage))}}()));function vv(e){return e?yc(e):e}function gv(e){return(e=e.slice(0))[3]=e[3]/255,"rgba("+e.join(",")+")"}function mv(e,t){Array.from(t).forEach((t=>{t.x=t.clientX-e.left,t.y=t.clientY-e.top}))}function _v(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return fv||(fv=document.createElement("canvas")),fv.width=e,fv.height=t,fv}const yv=Xp({inheritAttrs:!1,name:"Canvas",compatConfig:{MODE:3},props:{canvasId:{type:String,default:""},disableScroll:{type:[Boolean,String],default:!1},hidpi:{type:Boolean,default:!0}},computed:{id(){return this.canvasId}},setup(e,t){var{emit:n,slots:r}=t;pv();var i=to(null),a=to(null),o=to(null),s=to(!1),l=function(e){return(t,n)=>{e(t,uc(n))}}(n),{$attrs:u,$excludeAttrs:c,$listeners:d}=qg({excludeListeners:!0}),{_listeners:h}=function(e,t,n){var r=jl((()=>{var r=["onTouchstart","onTouchmove","onTouchend"],i=t.value,a=on({},(()=>{var e={};for(var t in i)if(un(i,t)){var n=i[t];e[t]=n}return e})());return r.forEach((t=>{var r=[];a[t]&&r.push(Gp((e=>{var r=e.currentTarget.getBoundingClientRect();mv(r,e.touches),mv(r,e.changedTouches),n(t.replace("on","").toLocaleLowerCase(),e)}))),e.disableScroll&&"onTouchmove"===t&&r.push(Tu),a[t]=r})),a}));return{_listeners:r}}(e,d,l),{_handleSubscribe:f,_resize:p}=function(e,t,n){var r=[],i={},a=jl((()=>e.hidpi?cv:1));function o(n){var r=t.value;if(!n||r.width!==Math.floor(n.width*a.value)||r.height!==Math.floor(n.height*a.value))if(r.width>0&&r.height>0){var i=r.getContext("2d"),o=i.getImageData(0,0,r.width,r.height);dv(r,e.hidpi),i.putImageData(o,0,0)}else dv(r,e.hidpi)}function s(e,a){var{actions:o,reserve:s}=e;if(o)if(n.value)r.push([o,s]);else{var c=t.value,d=c.getContext("2d");s||(d.fillStyle="#000000",d.strokeStyle="#000000",d.shadowColor="#000000",d.shadowBlur=0,d.shadowOffsetX=0,d.shadowOffsetY=0,d.setTransform(1,0,0,1,0,0),d.clearRect(0,0,c.width,c.height)),l(o);for(var h=function(e){var t=o[e],n=t.method,r=t.data,s=r[0];if(/^set/.test(n)&&"setTransform"!==n){var l,c=n[3].toLowerCase()+n.slice(4);if("fillStyle"===c||"strokeStyle"===c){if("normal"===s)l=gv(r[1]);else if("linear"===s){var h=d.createLinearGradient(...r[1]);r[2].forEach((function(e){var t=e[0],n=gv(e[1]);h.addColorStop(t,n)})),l=h}else if("radial"===s){var f=r[1],p=f[0],v=f[1],g=f[2],m=d.createRadialGradient(p,v,0,p,v,g);r[2].forEach((function(e){var t=e[0],n=gv(e[1]);m.addColorStop(t,n)})),l=m}else if("pattern"===s){return u(r[1],o.slice(e+1),a,(function(e){e&&(d[c]=d.createPattern(e,r[2]))}))?"continue":"break"}d[c]=l}else if("globalAlpha"===c)d[c]=Number(s)/255;else if("shadow"===c){var _=["shadowOffsetX","shadowOffsetY","shadowBlur","shadowColor"];r.forEach((function(e,t){d[_[t]]="shadowColor"===_[t]?gv(e):e}))}else if("fontSize"===c){var y=d.__font__||d.font;d.__font__=d.font=y.replace(/\d+\.?\d*px/,s+"px")}else"lineDash"===c?(d.setLineDash(s),d.lineDashOffset=r[1]||0):"textBaseline"===c?("normal"===s&&(r[0]="alphabetic"),d[c]=s):"font"===c?d.__font__=d.font=s:d[c]=s}else if("fillPath"===n||"strokePath"===n)n=n.replace(/Path/,""),d.beginPath(),r.forEach((function(e){d[e.method].apply(d,e.data)})),d[n]();else if("fillText"===n)d.fillText.apply(d,r);else if("drawImage"===n){if("break"===function(){var t=[...r],n=t[0],s=t.slice(1);if(i=i||{},!u(n,o.slice(e+1),a,(function(e){e&&d.drawImage.apply(d,[e].concat([...s.slice(4,8)],[...s.slice(0,4)]))})))return"break"}())return"break"}else"clip"===n?(r.forEach((function(e){d[e.method].apply(d,e.data)})),d.clip()):d[n].apply(d,r)},f=0;f<o.length;f++){var p=h(f);if("break"===p)break}n.value||a({errMsg:"drawCanvas:ok"})}}function l(e){e.forEach((function(e){var t=e.method,n=e.data,r="";function a(){var e=i[r]=new Image;if(e.onload=function(){e.ready=!0},"HarmonyOS"!==plus.os.name&&"Google Inc."===navigator.vendor)return 0===r.indexOf("file://")&&(e.crossOrigin="anonymous"),void(e.src=r);Sc(r).then((t=>{e.src=t})).catch((()=>{e.src=r}))}"drawImage"===t?(r=vv(r=n[0]),n[0]=r):"setFillStyle"===t&&"pattern"===n[0]&&(r=vv(r=n[1]),n[1]=r),r&&!i[r]&&a()}))}function u(e,t,a,o){var l=i[e];return l.ready?(o(l),!0):(r.unshift([t,!0]),n.value=!0,l.onload=function(){l.ready=!0,o(l),n.value=!1;var e=r.slice(0);r=[];for(var t=e.shift();t;)s({actions:t[0],reserve:t[1]},a),t=e.shift()},!1)}function c(e,n){var r,{x:i=0,y:o=0,width:s,height:l,destWidth:u,destHeight:c,hidpi:d=!0,dataType:h,quality:f=1,type:p="png"}=e,v=t.value,g=v.offsetWidth-i;s=s?Math.min(s,g):g;var m=v.offsetHeight-o;l=l?Math.min(l,m):m,d?(u=s,c=l):u||c?u?c||(c=Math.round(l/s*u)):(c||(c=Math.round(l*a.value)),u=Math.round(s/l*c)):(u=Math.round(s*a.value),c=Math.round(l*a.value));var _,y=_v(u,c),b=y.getContext("2d");"jpeg"!==p&&"jpg"!==p||(p="jpeg",b.fillStyle="#fff",b.fillRect(0,0,u,c)),b.__hidpi__=!0,b.drawImageByCanvas(v,i,o,s,l,0,0,u,c,!1);try{var w;if("base64"===h)r=y.toDataURL("image/".concat(p),f);else{var x=b.getImageData(0,0,u,c);r=zf.deflateRaw(x.data,{to:"string"}),w=!0}_={data:r,compressed:w,width:u,height:c}}catch(S){_={errMsg:"canvasGetImageData:fail ".concat(S)}}if(y.height=y.width=0,b.__hidpi__=!1,!n)return _;n(_)}function d(e,n){var{data:r,x:i,y:a,width:o,height:s,compressed:l}=e;try{l&&(r=zf.inflateRaw(r)),s||(s=Math.round(r.length/4/o));var u=_v(o,s);u.getContext("2d").putImageData(new ImageData(new Uint8ClampedArray(r),o,s),0,0),t.value.getContext("2d").drawImage(u,i,a,o,s),u.height=u.width=0}catch(c){return void n({errMsg:"canvasPutImageData:fail"})}n({errMsg:"canvasPutImageData:ok"})}function h(e,t){var{x:n=0,y:r=0,width:i,height:a,destWidth:o,destHeight:s,fileType:l,quality:u,dirname:d}=e,h=c({x:n,y:r,width:i,height:a,destWidth:o,destHeight:s,hidpi:!1,dataType:"base64",type:l,quality:u});h.errMsg?t({errMsg:h.errMsg.replace("canvasPutImageData","toTempFilePath")}):function(e,t,n){var r="".concat(Date.now()).concat(wc++),i=e.split(","),a=i[0],o=i[1],s=(a.match(/data:image\/(\S+?);/)||["","png"])[1].replace("jpeg","jpg"),l="".concat(r,".").concat(s),u="".concat(t,"/").concat(l),c=t.indexOf("/"),d=t.substring(0,c),h=t.substring(c+1);plus.io.resolveLocalFileSystemURL(d,(function(e){e.getDirectory(h,{create:!0,exclusive:!1},(function(e){e.getFile(l,{create:!0,exclusive:!1},(function(e){e.createWriter((function(e){e.onwrite=function(){n(null,u)},e.onerror=n,e.seek(0),e.writeAsBinary(o)}),n)}),n)}),n)}),n)}(h.data,d,((e,n)=>{var r="toTempFilePath:".concat(e?"fail":"ok");e&&(r+=" ".concat(e.message)),t({errMsg:r,tempFilePath:n})}))}var f={actionsChanged:s,getImageData:c,putImageData:d,toTempFilePath:h};function p(e,t,n){var r=f[e];0!==e.indexOf("_")&&fn(r)&&r(t,n)}return on(f,{_resize:o,_handleSubscribe:p})}(e,a,s);return a_(f,s_(e.canvasId)),as((()=>{p()})),()=>{var{canvasId:t,disableScroll:n}=e;return ml("uni-canvas",Sl({ref:i,"canvas-id":t,"disable-scroll":n},u.value,c.value,h.value),[ml("canvas",{ref:a,class:"uni-canvas-canvas",width:"300",height:"150"},null,512),ml("div",{style:"position: absolute;top: 0;left: 0;width: 100%;height: 100%;overflow: hidden;"},[r.default&&r.default()]),ml(uv,{ref:o,onResize:p},null,8,["onResize"])],16,["canvas-id","disable-scroll"])}}});var bv=Eu("ucg");const wv=Xp({name:"CheckboxGroup",props:{name:{type:String,default:""}},emits:["change"],setup(e,t){var{emit:n,slots:r}=t,i=to(null);return function(e,t){var n=[],r=()=>n.reduce(((e,t)=>(t.value.checkboxChecked&&e.push(t.value.value),e)),new Array);Ps(bv,{addField(e){n.push(e)},removeField(e){n.splice(n.indexOf(e),1)},checkboxChange(e){t("change",e,{value:r()})}});var i=Ds(ev,!1);i&&i.addField({submit:()=>{var t=["",null];return""!==e.name&&(t[0]=e.name,t[1]=r()),t}})}(e,Kp(i,n)),()=>ml("uni-checkbox-group",{ref:i},[r.default&&r.default()],512)}});const xv=Xp({name:"Checkbox",props:{checked:{type:[Boolean,String],default:!1},id:{type:String,default:""},disabled:{type:[Boolean,String],default:!1},value:{type:String,default:""},color:{type:String,default:"#007aff"},backgroundColor:{type:String,default:""},borderColor:{type:String,default:""},activeBackgroundColor:{type:String,default:""},activeBorderColor:{type:String,default:""},iconColor:{type:String,default:""},foreColor:{type:String,default:""}},setup(e,t){var{slots:n}=t,r=to(null),i=to(e.checked),a=jl((()=>"true"===i.value||!0===i.value)),o=to(e.value);var s=jl((()=>function(t){if(e.disabled)return{backgroundColor:"#E1E1E1",borderColor:"#D1D1D1"};var n={};return t?(e.activeBorderColor&&(n.borderColor=e.activeBorderColor),e.activeBackgroundColor&&(n.backgroundColor=e.activeBackgroundColor)):(e.borderColor&&(n.borderColor=e.borderColor),e.backgroundColor&&(n.backgroundColor=e.backgroundColor)),n}(a.value)));Vo([()=>e.checked,()=>e.value],(e=>{var[t,n]=e;i.value=t,o.value=n}));var{uniCheckGroup:l,uniLabel:u}=function(e,t,n){var r=jl((()=>({checkboxChecked:Boolean(e.value),value:t.value}))),i={reset:n},a=Ds(bv,!1);a&&a.addField(r);var o=Ds(ev,!1);o&&o.addField(i);var s=Ds(rv,!1);return ls((()=>{a&&a.removeField(r),o&&o.removeField(i)})),{uniCheckGroup:a,uniForm:o,uniLabel:s}}(i,o,(()=>{i.value=!1})),c=t=>{e.disabled||(i.value=!i.value,l&&l.checkboxChange(t),t.stopPropagation())};return u&&(u.addHandler(c),ls((()=>{u.removeHandler(c)}))),av(e,{"label-click":c}),()=>{var t,a=Qp(e,"disabled");return t=i.value,ml("uni-checkbox",Sl(a,{id:e.id,onClick:c,ref:r}),[ml("div",{class:"uni-checkbox-wrapper",style:{"--HOVER-BD-COLOR":e.activeBorderColor}},[ml("div",{class:["uni-checkbox-input",{"uni-checkbox-input-disabled":e.disabled}],style:s.value},[t?Pu(Nu,e.disabled?"#ADADAD":e.foreColor||e.iconColor||e.color,22):""],6),n.default&&n.default()],4)],16,["id","onClick"])}}});var Sv,kv,Cv,Tv,Av,Mv;function Ev(){}function Ov(e,t,n){wr((()=>{var r="adjustResize",i="adjustPan",a=plus.webview.currentWebview(),o=Mv||a.getStyle()||{},s={mode:n||o.softinputMode===r?r:e.adjustPosition?i:"nothing",position:{top:0,height:0}};if(s.mode===i){var l=t.getBoundingClientRect();s.position.top=l.top,s.position.height=l.height+(Number(e.cursorSpacing)||0)}a.setSoftinputTemporary(s)}))}wr((()=>{kv="Android"===plus.os.name,Cv=plus.os.version||""})),document.addEventListener("keyboardchange",(function(e){Tv=e.height,Av&&Av()}),!1);var Lv={cursorSpacing:{type:[Number,String],default:0},showConfirmBar:{type:[Boolean,String],default:"auto"},adjustPosition:{type:[Boolean,String],default:!0},autoBlur:{type:[Boolean,String],default:!1}},zv=["keyboardheightchange"];function Nv(e,t,n){var r={};function i(t){var i,a=jl((()=>0===String(navigator.vendor).indexOf("Apple"))),o=()=>{n("keyboardheightchange",{},{height:Tv,duration:.25}),i&&0===Tv&&Ov(e,t),e.autoBlur&&i&&0===Tv&&(kv||parseInt(Cv)>=13)&&document.activeElement.blur()};t.addEventListener("focus",(()=>{i=!0,clearTimeout(Sv),document.addEventListener("click",Ev,!1),Av=o,Tv&&n("keyboardheightchange",{},{height:Tv,duration:0}),function(e,t){"auto"!==e.showConfirmBar?wr((()=>{var n=plus.webview.currentWebview(),{softinputNavBar:r}=n.getStyle()||{};"none"!==r!==e.showConfirmBar?(t.softinputNavBar=r||"auto",n.setStyle({softinputNavBar:e.showConfirmBar?"auto":"none"})):delete t.softinputNavBar})):delete t.softinputNavBar}(e,r),Ov(e,t)})),kv&&t.addEventListener("click",(()=>{e.disabled||e.readOnly||!i||0!==Tv||Ov(e,t)})),kv||(parseInt(Cv)<12&&t.addEventListener("touchstart",(()=>{e.disabled||e.readOnly||i||Ov(e,t)})),parseFloat(Cv)>=14.6&&!Mv&&wr((()=>{var e=plus.webview.currentWebview();Mv=e.getStyle()||{}})));var s=()=>{document.removeEventListener("click",Ev,!1),Av=null,Tv&&n("keyboardheightchange",{},{height:0,duration:0}),function(e){var t=e.softinputNavBar;t&&wr((()=>{plus.webview.currentWebview().setStyle({softinputNavBar:t})}))}(r),kv&&(Sv=setTimeout((()=>{Ov(e,t,!0)}),300)),a.value&&document.documentElement.scrollTo(document.documentElement.scrollLeft,document.documentElement.scrollTop)};t.addEventListener("blur",(()=>{a.value&&t.blur(),i=!1,s()}))}Vo((()=>t.value),(e=>e&&i(e)))}var Iv=/^<([-A-Za-z0-9_]+)((?:\s+[a-zA-Z_:][-a-zA-Z0-9_:.]*(?:\s*=\s*(?:(?:"[^"]*")|(?:'[^']*')|[^>\s]+))?)*)\s*(\/?)>/,Pv=/^<\/([-A-Za-z0-9_]+)[^>]*>/,Dv=/([a-zA-Z_:][-a-zA-Z0-9_:.]*)(?:\s*=\s*(?:(?:"((?:\\.|[^"])*)")|(?:'((?:\\.|[^'])*)')|([^>\s]+)))?/g,Bv=Hv("area,base,basefont,br,col,frame,hr,img,input,link,meta,param,embed,command,keygen,source,track,wbr"),Rv=Hv("a,address,article,applet,aside,audio,blockquote,button,canvas,center,dd,del,dir,div,dl,dt,fieldset,figcaption,figure,footer,form,frameset,h1,h2,h3,h4,h5,h6,header,hgroup,hr,iframe,isindex,li,map,menu,noframes,noscript,object,ol,output,p,pre,section,script,table,tbody,td,tfoot,th,thead,tr,ul,video"),Fv=Hv("abbr,acronym,applet,b,basefont,bdo,big,br,button,cite,code,del,dfn,em,font,i,iframe,img,input,ins,kbd,label,map,object,q,s,samp,script,select,small,span,strike,strong,sub,sup,textarea,tt,u,var"),qv=Hv("colgroup,dd,dt,li,options,p,td,tfoot,th,thead,tr"),jv=Hv("checked,compact,declare,defer,disabled,ismap,multiple,nohref,noresize,noshade,nowrap,readonly,selected"),Vv=Hv("script,style");function $v(e,t){var n,r,i,a=[],o=e;for(a.last=function(){return this[this.length-1]};e;){if(r=!0,a.last()&&Vv[a.last()])e=e.replace(new RegExp("([\\s\\S]*?)</"+a.last()+"[^>]*>"),(function(e,n){return n=n.replace(/<!--([\s\S]*?)-->|<!\[CDATA\[([\s\S]*?)]]>/g,"$1$2"),t.chars&&t.chars(n),""})),u("",a.last());else if(0==e.indexOf("\x3c!--")?(n=e.indexOf("--\x3e"))>=0&&(t.comment&&t.comment(e.substring(4,n)),e=e.substring(n+3),r=!1):0==e.indexOf("</")?(i=e.match(Pv))&&(e=e.substring(i[0].length),i[0].replace(Pv,u),r=!1):0==e.indexOf("<")&&(i=e.match(Iv))&&(e=e.substring(i[0].length),i[0].replace(Iv,l),r=!1),r){var s=(n=e.indexOf("<"))<0?e:e.substring(0,n);e=n<0?"":e.substring(n),t.chars&&t.chars(s)}if(e==o)throw"Parse Error: "+e;o=e}function l(e,n,r,i){if(n=n.toLowerCase(),Rv[n])for(;a.last()&&Fv[a.last()];)u("",a.last());if(qv[n]&&a.last()==n&&u("",n),(i=Bv[n]||!!i)||a.push(n),t.start){var o=[];r.replace(Dv,(function(e,t){var n=arguments[2]?arguments[2]:arguments[3]?arguments[3]:arguments[4]?arguments[4]:jv[t]?t:"";o.push({name:t,value:n,escaped:n.replace(/(^|[^\\])"/g,'$1\\"')})})),t.start&&t.start(n,o,i)}}function u(e,n){if(n)for(r=a.length-1;r>=0&&a[r]!=n;r--);else var r=0;if(r>=0){for(var i=a.length-1;i>=r;i--)t.end&&t.end(a[i]);a.length=r}}u()}function Hv(e){for(var t={},n=e.split(","),r=0;r<n.length;r++)t[n[r]]=!0;return t}var Wv={};function Uv(e,t,n){if(pn(e)?window[e]:e)n();else{var r=Wv[t];if(!r){r=Wv[t]=[];var i=document.createElement("script");i.src=t,document.body.appendChild(i),i.onload=function(){r.forEach((e=>e())),delete Wv[t]}}r.push(n)}}function Yv(e){var t=e.import("blots/block/embed");class n extends t{}return n.blotName="divider",n.tagName="HR",{"formats/divider":n}}function Xv(e){var t=e.import("blots/inline");class n extends t{}return n.blotName="ins",n.tagName="INS",{"formats/ins":n}}function Zv(e){var{Scope:t,Attributor:n}=e.import("parchment"),r={scope:t.BLOCK,whitelist:["left","right","center","justify"]};return{"formats/align":new n.Style("align","text-align",r)}}function Gv(e){var{Scope:t,Attributor:n}=e.import("parchment"),r={scope:t.BLOCK,whitelist:["rtl"]};return{"formats/direction":new n.Style("direction","direction",r)}}function Kv(e){var t=e.import("parchment"),n=e.import("blots/container"),r=e.import("formats/list/item");class i extends n{static create(e){var t="ordered"===e?"OL":"UL",n=super.create(t);return"checked"!==e&&"unchecked"!==e||n.setAttribute("data-checked","checked"===e),n}static formats(e){return"OL"===e.tagName?"ordered":"UL"===e.tagName?e.hasAttribute("data-checked")?"true"===e.getAttribute("data-checked")?"checked":"unchecked":"bullet":void 0}constructor(e){super(e);e.addEventListener("click",(n=>{if(n.target.parentNode===e){var r=this.statics.formats(e),i=t.find(n.target);"checked"===r?i.format("list","unchecked"):"unchecked"===r&&i.format("list","checked")}}))}format(e,t){this.children.length>0&&this.children.tail.format(e,t)}formats(){return{[this.statics.blotName]:this.statics.formats(this.domNode)}}insertBefore(e,t){if(e instanceof r)super.insertBefore(e,t);else{var n=null==t?this.length():t.offset(this),i=this.split(n);i.parent.insertBefore(e,i)}}optimize(e){super.optimize(e);var t=this.next;null!=t&&t.prev===this&&t.statics.blotName===this.statics.blotName&&t.domNode.tagName===this.domNode.tagName&&t.domNode.getAttribute("data-checked")===this.domNode.getAttribute("data-checked")&&(t.moveChildren(this),t.remove())}replace(e){if(e.statics.blotName!==this.statics.blotName){var n=t.create(this.statics.defaultChild);e.moveChildren(n),this.appendChild(n)}super.replace(e)}}return i.blotName="list",i.scope=t.Scope.BLOCK_BLOT,i.tagName=["OL","UL"],i.defaultChild="list-item",i.allowedChildren=[r],{"formats/list":i}}function Jv(e){var{Scope:t}=e.import("parchment");return{"formats/backgroundColor":new(e.import("formats/background").constructor)("backgroundColor","background-color",{scope:t.INLINE})}}function Qv(e){var{Scope:t,Attributor:n}=e.import("parchment"),r={scope:t.BLOCK},i={};return["margin","marginTop","marginBottom","marginLeft","marginRight"].concat(["padding","paddingTop","paddingBottom","paddingLeft","paddingRight"]).forEach((e=>{i["formats/".concat(e)]=new n.Style(e,Mn(e),r)})),i}function eg(e){var{Scope:t,Attributor:n}=e.import("parchment"),r={scope:t.INLINE},i={};return["font","fontSize","fontStyle","fontVariant","fontWeight","fontFamily"].forEach((e=>{i["formats/".concat(e)]=new n.Style(e,Mn(e),r)})),i}function tg(e){var{Scope:t,Attributor:n}=e.import("parchment"),r=[{name:"lineHeight",scope:t.BLOCK},{name:"letterSpacing",scope:t.INLINE},{name:"textDecoration",scope:t.INLINE},{name:"textIndent",scope:t.BLOCK}],i={};return r.forEach((e=>{var{name:t,scope:r}=e;i["formats/".concat(t)]=new n.Style(t,Mn(t),{scope:r})})),i}function ng(e){var t=e.import("formats/image"),n=["alt","height","width","data-custom","class","data-local"];t.sanitize=e=>e?yc(e):e,t.formats=function(e){return n.reduce((function(t,n){return e.hasAttribute(n)&&(t[n]=e.getAttribute(n)),t}),{})};var r=t.prototype.format;t.prototype.format=function(e,t){n.indexOf(e)>-1?t?this.domNode.setAttribute(e,t):this.domNode.removeAttribute(e):r.call(this,e,t)}}function rg(e){var t=e.import("formats/link");t.sanitize=e=>{var n=document.createElement("a");n.href=e;var r=n.href.slice(0,n.href.indexOf(":"));return t.PROTOCOL_WHITELIST.concat("file").indexOf(r)>-1?e:t.SANITIZED_URL}}function ig(e,t,n){var r,i,a;function o(){return{html:a.root.innerHTML,text:a.getText(),delta:a.getContents()}}function s(e){var t="data-placeholder",n=a.root;n.getAttribute(t)!==e&&n.setAttribute(t,e)}Vo((()=>e.readOnly),(e=>{r&&(a.enable(!e),e||a.blur())})),Vo((()=>e.placeholder),(e=>{r&&s(e)}));var l={};function u(e){var t=e?a.getFormat(e):{},r=Object.keys(t);(r.length!==Object.keys(l).length||r.find((e=>t[e]!==l[e])))&&(l=t,n("statuschange",{},t))}function c(){n("input",{},o())}function d(l){var d=window.Quill;!function(e){var t={divider:Yv,ins:Xv,align:Zv,direction:Gv,list:Kv,background:Jv,box:Qv,font:eg,text:tg,image:ng,link:rg},n={};Object.values(t).forEach((t=>on(n,t(e)))),e.register(n,!0)}(d);var h={toolbar:!1,readOnly:e.readOnly,placeholder:e.placeholder};l.length&&(d.register("modules/ImageResize",window.ImageResize.default),h.modules={ImageResize:{modules:l}});var f=t.value,p=(a=new d(f,h)).root;["focus","blur","input"].forEach((t=>{p.addEventListener(t,(r=>{var i=o();if("input"===t){if("ios"===_c().platform){var a=(i.html.match(/<span [\s\S]*>([\s\S]*)<\/span>/)||[])[1];s(a&&a.replace(/\s/g,"")?"":e.placeholder)}r.stopPropagation()}else n(t,r,i)}))})),a.on("text-change",c),a.on("selection-change",u),a.on("scroll-optimize",(()=>{u(a.selection.getRange()[0])})),a.clipboard.addMatcher(Node.ELEMENT_NODE,((e,t)=>(i||t.ops&&(t.ops=t.ops.filter((e=>{var{insert:t}=e;return pn(t)})).map((e=>{var{insert:t}=e;return{insert:t}}))),t))),r=!0,n("ready",{},{})}a_(((e,t,n)=>{var s,l,d,{options:h,callbackId:f}=t;if(r){var p=window.Quill;switch(e){case"format":var{name:v="",value:g=!1}=h;l=a.getSelection(!0);var m=a.getFormat(l)[v]||!1;if(["bold","italic","underline","strike","ins"].includes(v))g=!m;else if("direction"===v){g=("rtl"!==g||!m)&&g;var _=a.getFormat(l).align;"rtl"!==g||_?g||"right"!==_||a.format("align",!1,"user"):a.format("align","right","user")}else if("indent"===v){g="+1"===g,"rtl"===a.getFormat(l).direction&&(g=!g),g=g?"+1":"-1"}else"list"===v&&(g="check"===g?"unchecked":g,m="checked"===m?"unchecked":m),g=m&&m!==(g||!1)||!m&&g?g:!m;a.format(v,g,"user");break;case"insertDivider":l=a.getSelection(!0),a.insertText(l.index,Jn,"user"),a.insertEmbed(l.index+1,"divider",!0,"user"),a.setSelection(l.index+2,0,"silent");break;case"insertImage":l=a.getSelection(!0);var{src:y="",alt:b="",width:w="",height:x="",extClass:S="",data:k={}}=h,C=yc(y);a.insertEmbed(l.index,"image",C,"silent");var T=!!/^(file|blob):/.test(C)&&C;a.formatText(l.index,1,"data-local",T,"silent"),a.formatText(l.index,1,"alt",b,"silent"),a.formatText(l.index,1,"width",w,"silent"),a.formatText(l.index,1,"height",x,"silent"),a.formatText(l.index,1,"class",S,"silent"),a.formatText(l.index,1,"data-custom",Object.keys(k).map((e=>"".concat(e,"=").concat(k[e]))).join("&"),"silent"),a.setSelection(l.index+1,0,"silent"),a.scrollIntoView(),setTimeout((()=>{c()}),1e3);break;case"insertText":l=a.getSelection(!0);var{text:A=""}=h;a.insertText(l.index,A,"user"),a.setSelection(l.index+A.length,0,"silent");break;case"setContents":var{delta:M,html:E}=h;"object"==typeof M?a.setContents(M,"silent"):pn(E)?a.setContents(function(e){var t,n=["span","strong","b","ins","em","i","u","a","del","s","sub","sup","img","div","p","h1","h2","h3","h4","h5","h6","hr","ol","ul","li","br"],r="";$v(e,{start:function(e,i,a){if(n.includes(e)){t=!1;var o=i.map((e=>{var{name:t,value:n}=e;return"".concat(t,'="').concat(n,'"')})).join(" "),s="<".concat(e," ").concat(o," ").concat(a?"/":"",">");r+=s}else t=!a},end:function(e){t||(r+="</".concat(e,">"))},chars:function(e){t||(r+=e)}}),i=!0;var o=a.clipboard.convert(r);return i=!1,o}(E),"silent"):d="contents is missing";break;case"getContents":s=o();break;case"clear":a.setText("");break;case"removeFormat":l=a.getSelection(!0);var O=p.import("parchment");l.length?a.removeFormat(l.index,l.length,"user"):Object.keys(a.getFormat(l)).forEach((e=>{O.query(e,O.Scope.INLINE)&&a.format(e,!1)}));break;case"undo":a.history.undo();break;case"redo":a.history.redo();break;case"blur":a.blur();break;case"getSelectionText":s={text:""},(l=a.selection.savedRange)&&0!==l.length&&(s.text=a.getText(l.index,l.length));break;case"scrollIntoView":a.scrollIntoView()}u(l)}else d="not ready";f&&n({callbackId:f,data:on({},s,{errMsg:"".concat(e,":").concat(d?"fail "+d:"ok")})})}),s_()),as((()=>{var t=[];e.showImgSize&&t.push("DisplaySize"),e.showImgToolbar&&t.push("Toolbar"),e.showImgResize&&t.push("Resize");Uv(window.Quill,"./__uniappquill.js",(()=>{if(t.length){Uv(window.ImageResize,"./__uniappquillimageresize.js",(()=>{d(t)}))}else d(t)}))}))}const ag=Xp({name:"Editor",props:on({},Lv,{id:{type:String,default:""},readOnly:{type:[Boolean,String],default:!1},placeholder:{type:String,default:""},showImgSize:{type:[Boolean,String],default:!1},showImgToolbar:{type:[Boolean,String],default:!1},showImgResize:{type:[Boolean,String],default:!1}}),emit:["ready","focus","blur","input","statuschange",...zv],setup(e,t){var{emit:n}=t,r=to(null),i=Kp(r,n);return ig(e,r,i),Nv(e,r,i),()=>ml("uni-editor",{ref:r,id:e.id,class:"ql-container"},null,8,["id"])}});var og="#10aeff",sg="#b2b2b2",lg={success:{d:"M15.808 0.16q-4.224 0-7.872 2.176-3.552 2.112-5.632 5.728-2.144 3.744-2.144 8.128 0 4.192 2.144 7.872 2.112 3.52 5.632 5.632 3.68 2.144 7.872 2.144 4.384 0 8.128-2.144 3.616-2.080 5.728-5.632 2.176-3.648 2.176-7.872 0-4.384-2.176-8.128-2.112-3.616-5.728-5.728-3.744-2.176-8.128-2.176zM24.832 11.328l-11.264 11.104q-0.032 0.032-0.112 0.032t-0.112-0.032l-5.216-5.376q-0.096-0.128 0-0.288l0.704-0.96q0.032-0.064 0.112-0.064t0.112 0.032l4.256 3.264q0.064 0.032 0.144 0.032t0.112-0.032l10.336-8.608q0.064-0.064 0.144-0.064t0.112 0.064l0.672 0.672q0.128 0.128 0 0.224z",c:er},success_no_circle:{d:Nu,c:er},info:{d:"M15.808 0.128q-4.224 0-7.872 2.176-3.552 2.112-5.632 5.728-2.176 3.776-2.176 8.16 0 4.224 2.176 7.872 2.080 3.552 5.632 5.632 3.648 2.176 7.872 2.176 4.384 0 8.16-2.176 3.616-2.080 5.728-5.632 2.176-3.648 2.176-7.872 0-4.416-2.176-8.16-2.112-3.616-5.728-5.728-3.744-2.176-8.16-2.176zM16.864 23.776q0 0.064-0.064 0.064h-1.568q-0.096 0-0.096-0.064l-0.256-11.328q0-0.064 0.064-0.064h2.112q0.096 0 0.064 0.064l-0.256 11.328zM16 10.88q-0.576 0-0.976-0.4t-0.4-0.96 0.4-0.96 0.976-0.4 0.976 0.4 0.4 0.96-0.4 0.96-0.976 0.4z",c:og},warn:{d:"M15.808 0.16q-4.224 0-7.872 2.176-3.552 2.112-5.632 5.728-2.144 3.744-2.144 8.128 0 4.192 2.144 7.872 2.112 3.52 5.632 5.632 3.68 2.144 7.872 2.144 4.384 0 8.128-2.144 3.616-2.080 5.728-5.632 2.176-3.648 2.176-7.872 0-4.384-2.176-8.128-2.112-3.616-5.728-5.728-3.744-2.176-8.128-2.176zM15.136 8.672h1.728q0.128 0 0.224 0.096t0.096 0.256l-0.384 10.24q0 0.064-0.048 0.112t-0.112 0.048h-1.248q-0.096 0-0.144-0.048t-0.048-0.112l-0.384-10.24q0-0.16 0.096-0.256t0.224-0.096zM16 23.328q-0.48 0-0.832-0.352t-0.352-0.848 0.352-0.848 0.832-0.352 0.832 0.352 0.352 0.848-0.352 0.848-0.832 0.352z",c:"#f76260"},waiting:{d:"M15.84 0.096q-4.224 0-7.872 2.176-3.552 2.112-5.632 5.728-2.144 3.744-2.144 8.128 0 4.192 2.144 7.872 2.112 3.52 5.632 5.632 3.68 2.144 7.872 2.144 4.384 0 8.128-2.144 3.616-2.080 5.728-5.632 2.176-3.648 2.176-7.872 0-4.384-2.176-8.128-2.112-3.616-5.728-5.728-3.744-2.176-8.128-2.176zM23.008 21.92l-0.512 0.896q-0.096 0.128-0.224 0.064l-8-3.808q-0.096-0.064-0.16-0.128-0.128-0.096-0.128-0.288l0.512-12.096q0-0.064 0.048-0.112t0.112-0.048h1.376q0.064 0 0.112 0.048t0.048 0.112l0.448 10.848 6.304 4.256q0.064 0.064 0.080 0.128t-0.016 0.128z",c:og},cancel:{d:"M20.928 10.176l-4.928 4.928-4.928-4.928-0.896 0.896 4.928 4.928-4.928 4.928 0.896 0.896 4.928-4.928 4.928 4.928 0.896-0.896-4.928-4.928 4.928-4.928-0.896-0.896zM16 2.080q-3.776 0-7.040 1.888-3.136 1.856-4.992 4.992-1.888 3.264-1.888 7.040t1.888 7.040q1.856 3.136 4.992 4.992 3.264 1.888 7.040 1.888t7.040-1.888q3.136-1.856 4.992-4.992 1.888-3.264 1.888-7.040t-1.888-7.040q-1.856-3.136-4.992-4.992-3.264-1.888-7.040-1.888zM16 28.64q-3.424 0-6.4-1.728-2.848-1.664-4.512-4.512-1.728-2.976-1.728-6.4t1.728-6.4q1.664-2.848 4.512-4.512 2.976-1.728 6.4-1.728t6.4 1.728q2.848 1.664 4.512 4.512 1.728 2.976 1.728 6.4t-1.728 6.4q-1.664 2.848-4.512 4.512-2.976 1.728-6.4 1.728z",c:"#f43530"},download:{d:"M15.808 1.696q-3.776 0-7.072 1.984-3.2 1.888-5.088 5.152-1.952 3.392-1.952 7.36 0 3.776 1.952 7.072 1.888 3.2 5.088 5.088 3.296 1.952 7.072 1.952 3.968 0 7.36-1.952 3.264-1.888 5.152-5.088 1.984-3.296 1.984-7.072 0-4-1.984-7.36-1.888-3.264-5.152-5.152-3.36-1.984-7.36-1.984zM20.864 18.592l-3.776 4.928q-0.448 0.576-1.088 0.576t-1.088-0.576l-3.776-4.928q-0.448-0.576-0.24-0.992t0.944-0.416h2.976v-8.928q0-0.256 0.176-0.432t0.4-0.176h1.216q0.224 0 0.4 0.176t0.176 0.432v8.928h2.976q0.736 0 0.944 0.416t-0.24 0.992z",c:er},search:{d:"M20.928 22.688q-1.696 1.376-3.744 2.112-2.112 0.768-4.384 0.768-3.488 0-6.464-1.728-2.88-1.696-4.576-4.608-1.76-2.976-1.76-6.464t1.76-6.464q1.696-2.88 4.576-4.576 2.976-1.76 6.464-1.76t6.464 1.76q2.912 1.696 4.608 4.576 1.728 2.976 1.728 6.464 0 2.272-0.768 4.384-0.736 2.048-2.112 3.744l9.312 9.28-1.824 1.824-9.28-9.312zM12.8 23.008q2.784 0 5.184-1.376 2.304-1.376 3.68-3.68 1.376-2.4 1.376-5.184t-1.376-5.152q-1.376-2.336-3.68-3.68-2.4-1.408-5.184-1.408t-5.152 1.408q-2.336 1.344-3.68 3.68-1.408 2.368-1.408 5.152t1.408 5.184q1.344 2.304 3.68 3.68 2.368 1.376 5.152 1.376zM12.8 23.008v0z",c:sg},clear:{d:"M16 0q-4.352 0-8.064 2.176-3.616 2.144-5.76 5.76-2.176 3.712-2.176 8.064t2.176 8.064q2.144 3.616 5.76 5.76 3.712 2.176 8.064 2.176t8.064-2.176q3.616-2.144 5.76-5.76 2.176-3.712 2.176-8.064t-2.176-8.064q-2.144-3.616-5.76-5.76-3.712-2.176-8.064-2.176zM22.688 21.408q0.32 0.32 0.304 0.752t-0.336 0.736-0.752 0.304-0.752-0.32l-5.184-5.376-5.376 5.184q-0.32 0.32-0.752 0.304t-0.736-0.336-0.304-0.752 0.32-0.752l5.376-5.184-5.184-5.376q-0.32-0.32-0.304-0.752t0.336-0.752 0.752-0.304 0.752 0.336l5.184 5.376 5.376-5.184q0.32-0.32 0.752-0.304t0.752 0.336 0.304 0.752-0.336 0.752l-5.376 5.184 5.184 5.376z",c:sg}};const ug=Xp({name:"Icon",props:{type:{type:String,required:!0,default:""},size:{type:[String,Number],default:23},color:{type:String,default:""}},setup(e){var t=to(null),n=jl((()=>lg[e.type]));return()=>{var{value:r}=n;return ml("uni-icon",{ref:t},[r&&r.d&&Pu(r.d,e.color||r.c,Lu(e.size))],512)}}});var cg={src:{type:String,default:""},mode:{type:String,default:"scaleToFill"},lazyLoad:{type:[Boolean,String],default:!1},draggable:{type:Boolean,default:!1}},dg={widthFix:["offsetWidth","height",(e,t)=>e/t],heightFix:["offsetHeight","width",(e,t)=>e*t]},hg={aspectFit:["center center","contain"],aspectFill:["center center","cover"],widthFix:[,"100% 100%"],heightFix:[,"100% 100%"],top:["center top"],bottom:["center bottom"],center:["center center"],left:["left center"],right:["right center"],"top left":["left top"],"top right":["right top"],"bottom left":["left bottom"],"bottom right":["right bottom"]};const fg=Xp({name:"Image",props:cg,setup(e,t){var{emit:n}=t,r=to(null),i=function(e,t){var n=to(""),r=jl((()=>{var e="auto",r="",i=hg[t.mode];return i?(i[0]&&(r=i[0]),i[1]&&(e=i[1])):(r="0% 0%",e="100% 100%"),"background-image:".concat(n.value?'url("'+n.value+'")':"none",";background-position:").concat(r,";background-size:").concat(e,";")})),i=qa({rootEl:e,src:jl((()=>t.src?yc(t.src):"")),origWidth:0,origHeight:0,origStyle:{width:"",height:""},modeStyle:r,imgSrc:n});return as((()=>{var t=e.value;i.origWidth=t.clientWidth||0,i.origHeight=t.clientHeight||0})),i}(r,e),a=Kp(r,n),{fixSize:o}=function(e,t,n){var r=()=>{var{mode:r}=t,i=dg[r];if(i){var{origWidth:a,origHeight:o}=n,s=a&&o?a/o:0;if(s){var l=e.value,u=l[i[0]];u&&(l.style[i[1]]=function(e){pg&&e>10&&(e=2*Math.round(e/2));return e}(i[2](u,s))+"px"),window.dispatchEvent(new CustomEvent("updateview"))}}},i=()=>{var{style:t}=e.value,{origStyle:{width:r,height:i}}=n;t.width=r,t.height=i};return Vo((()=>t.mode),((e,t)=>{dg[t]&&i(),dg[e]&&r()})),{fixSize:r,resetSize:i}}(r,e,i);return function(e,t,n,r,i){var a,o,s=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"";e.origWidth=t,e.origHeight=n,e.imgSrc=r},l=l=>{if(!l)return u(),void s();(a=a||new Image).onload=e=>{var{width:c,height:d}=a;s(c,d,l),bo((()=>{r()})),a.draggable=t.draggable,o&&o.remove(),o=a,n.value.appendChild(a),u(),i("load",e,{width:c,height:d})},a.onerror=t=>{s(),u(),i("error",t,{errMsg:"GET ".concat(e.src," 404 (Not Found)")})},a.src=l},u=()=>{a&&(a.onload=null,a.onerror=null,a=null)};Vo((()=>e.src),(e=>l(e))),Vo((()=>e.imgSrc),(e=>{!e&&o&&(o.remove(),o=null)})),as((()=>l(e.src))),ls((()=>u()))}(i,e,r,o,a),()=>ml("uni-image",{ref:r},[ml("div",{style:i.modeStyle},null,4),dg[e.mode]?ml(uv,{onResize:o},null,8,["onResize"]):ml("span",null,null)],512)}});var pg="Google Inc."===navigator.vendor;var vg=yr(!0),gg=[],mg=0,_g=!1,yg=e=>gg.forEach((t=>t.userAction=e));function bg(){var e=qa({userAction:!1});return as((()=>{!function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{userAction:!1};_g||(["touchstart","touchmove","touchend","mousedown","mouseup"].forEach((e=>{document.addEventListener(e,(function(){!mg&&yg(!0),mg++,setTimeout((()=>{! --mg&&yg(!1)}),0)}),vg)})),_g=!0);gg.push(e)}(e)})),ls((()=>{var t,n;t=e,(n=gg.indexOf(t))>=0&&gg.splice(n,1)})),{state:e}}function wg(){var e=qa({attrs:{}});return as((()=>{for(var t=Ol();t;){var n=t.type.__scopeId;n&&(e.attrs[n]=""),t=t.proxy&&"page"===t.proxy.$mpType?null:t.parent}})),{state:e}}function xg(e,t){var n=document.activeElement;if(!n)return t({});var r={};["input","textarea"].includes(n.tagName.toLowerCase())&&(r.start=n.selectionStart,r.end=n.selectionEnd),t(r)}var Sg,kg=200;function Cg(e,t,n){return"number"===t&&isNaN(Number(e))&&(e=""),null==e?"":String(e)}var Tg=["none","text","decimal","numeric","tel","search","email","url"],Ag=on({},{name:{type:String,default:""},modelValue:{type:[String,Number]},value:{type:[String,Number]},disabled:{type:[Boolean,String],default:!1},autoFocus:{type:[Boolean,String],default:!1},focus:{type:[Boolean,String],default:!1},cursor:{type:[Number,String],default:-1},selectionStart:{type:[Number,String],default:-1},selectionEnd:{type:[Number,String],default:-1},type:{type:String,default:"text"},password:{type:[Boolean,String],default:!1},placeholder:{type:String,default:""},placeholderStyle:{type:String,default:""},placeholderClass:{type:String,default:""},maxlength:{type:[Number,String],default:140},confirmType:{type:String,default:"done"},confirmHold:{type:Boolean,default:!1},ignoreCompositionEvent:{type:Boolean,default:!0},step:{type:String,default:"0.000000000000000001"},inputmode:{type:String,default:void 0,validator:e=>!!~Tg.indexOf(e)},cursorColor:{type:String,default:""}},Lv),Mg=["input","focus","blur","update:value","update:modelValue","update:focus","compositionstart","compositionupdate","compositionend",...zv];function Eg(e,t,n,r){var i=null;Vo((()=>e.modelValue),i=Hr((n=>{t.value=Cg(n,e.type)}),100,{setTimeout:setTimeout,clearTimeout:clearTimeout})),Vo((()=>e.value),i);var a=function(e,t){var n,r,i=0,a=function(){for(var a=arguments.length,o=new Array(a),s=0;s<a;s++)o[s]=arguments[s];var l=Date.now();clearTimeout(n),r=()=>{r=null,i=l,e.apply(this,o)},l-i<t?n=setTimeout(r,t-(l-i)):r()};return a.cancel=function(){clearTimeout(n),r=null},a.flush=function(){clearTimeout(n),r&&r()},a}(((e,t)=>{i.cancel(),n("update:modelValue",t.value),n("update:value",t.value),r("input",e,t)}),100);return is((()=>{i.cancel(),a.cancel()})),{trigger:r,triggerInput:(e,t,n)=>{i.cancel(),a(e,t),n&&a.flush()}}}function Og(e,t){var{state:n}=bg(),r=jl((()=>e.autoFocus||e.focus));function i(){if(r.value){var e=t.value;if(e&&"plus"in window){var a=kg-(Date.now()-Sg);a>0?setTimeout(i,a):"HarmonyOS"===plus.os.name?n.userAction?e.focus():(plus.key.showSoftKeybord(),setTimeout((()=>{e.focus()}),100)):(e.focus(),n.userAction||plus.key.showSoftKeybord())}else setTimeout(i,100)}}Vo((()=>e.focus),(e=>{var n;e?i():(n=t.value)&&n.blur()})),as((()=>{Sg=Sg||Date.now(),r.value&&bo(i)}))}function Lg(e,t,n,r){bi(Bu(),"getSelectedTextRange",xg);var{fieldRef:i,state:a,trigger:o}=function(e,t,n){var r,i=to(null),a=Kp(t,n),o=jl((()=>{var t=Number(e.selectionStart);return isNaN(t)?-1:t})),s=jl((()=>{var t=Number(e.selectionEnd);return isNaN(t)?-1:t})),l=jl((()=>{var t=Number(e.cursor);return isNaN(t)?-1:t})),u=jl((()=>{var t=Number(e.maxlength);return isNaN(t)?140:t})),c=qa({value:r=Cg(e.modelValue,e.type)||Cg(e.value,e.type),valueOrigin:r,maxlength:u,focus:e.focus,composing:!1,selectionStart:o,selectionEnd:s,cursor:l});return Vo((()=>c.focus),(e=>n("update:focus",e))),Vo((()=>c.maxlength),(e=>c.value=c.value.slice(0,e)),{immediate:!1}),{fieldRef:i,state:c,trigger:a}}(e,t,n),{triggerInput:s}=Eg(e,a,n,o);Og(e,i),Nv(e,i,o);var{state:l}=wg();return function(e,t){var n=Ds(ev,!1);if(n){var r=Ol(),i={submit(){var n=r.proxy;return[n[e],pn(t)?n[t]:t.value]},reset(){pn(t)?r.proxy[t]="":t.value=""}};n.addField(i),ls((()=>{n.removeField(i)}))}}("name",a),function(e,t,n,r,i,a){function o(){var n=e.value;n&&t.focus&&t.selectionStart>-1&&t.selectionEnd>-1&&"number"!==n.type&&(n.selectionStart=t.selectionStart,n.selectionEnd=t.selectionEnd)}function s(){var n=e.value;n&&t.focus&&t.selectionStart<0&&t.selectionEnd<0&&t.cursor>-1&&"number"!==n.type&&(n.selectionEnd=n.selectionStart=t.cursor)}function l(e){return"number"===e.type?null:e.selectionEnd}Vo([()=>t.selectionStart,()=>t.selectionEnd],o),Vo((()=>t.cursor),s),Vo((()=>e.value),(function(){var u=e.value;if(u){var c=function(e,r){e.stopPropagation(),fn(a)&&!1===a(e,t)||(t.value=u.value,t.composing&&n.ignoreCompositionEvent||i(e,{value:u.value,cursor:l(u)},r))};u.addEventListener("change",(e=>e.stopPropagation())),u.addEventListener("focus",(function(e){t.focus=!0,r("focus",e,{value:t.value}),o(),s()})),u.addEventListener("blur",(function(e){t.composing&&(t.composing=!1,c(e,!0)),t.focus=!1,r("blur",e,{value:t.value,cursor:l(e.target)})})),u.addEventListener("input",c),u.addEventListener("compositionstart",(e=>{e.stopPropagation(),t.composing=!0,d(e)})),u.addEventListener("compositionend",(e=>{e.stopPropagation(),t.composing&&(t.composing=!1,c(e)),d(e)})),u.addEventListener("compositionupdate",d)}function d(e){n.ignoreCompositionEvent||r(e.type,e,{value:e.data})}}))}(i,a,e,o,s,r),{fieldRef:i,state:a,scopedAttrsState:l,fixDisabledColor:0===String(navigator.vendor).indexOf("Apple")&&CSS.supports("image-orientation:from-image"),trigger:o}}var zg=on({},Ag,{placeholderClass:{type:String,default:"input-placeholder"},textContentType:{type:String,default:""}}),Ng=dr((()=>{var e=plus.os.version;return"iOS"===plus.os.name&&!!e&&parseInt(e)>=16&&parseFloat(e)<17.2}));function Ig(e,t,n,r,i){if(t.value)if("."===e.data){if("."===t.value.slice(-1))return n.value=r.value=t.value=t.value.slice(0,-1),!1;if(t.value&&!t.value.includes("."))return t.value+=".",i&&(i.fn=()=>{n.value=r.value=t.value=t.value.slice(0,-1),r.removeEventListener("blur",i.fn)},r.addEventListener("blur",i.fn)),!1}else if("deleteContentBackward"===e.inputType&&Ng()&&"."===t.value.slice(-2,-1))return t.value=n.value=r.value=t.value.slice(0,-2),!0}const Pg=Xp({name:"Input",props:zg,emits:["confirm",...Mg],setup(e,t){var{emit:n,expose:r}=t,i=["text","number","idcard","digit","password","tel"],a=["off","one-time-code"],o=jl((()=>{var t="";switch(e.type){case"text":t="text","search"===e.confirmType&&(t="search");break;case"idcard":t="text";break;case"digit":t="number";break;default:t=i.includes(e.type)?e.type:"text"}return e.password?"password":t})),s=jl((()=>{var t=a.indexOf(e.textContentType),n=a.indexOf(Mn(e.textContentType));return a[-1!==t?t:-1!==n?n:0]})),l=function(e,t){if("number"===t.value){var n=void 0===e.modelValue?e.value:e.modelValue,r=to(null!=n?n.toLocaleString():"");return Vo((()=>e.modelValue),(e=>{r.value=null!=e?e.toLocaleString():""})),Vo((()=>e.value),(e=>{r.value=null!=e?e.toLocaleString():""})),r}return to("")}(e,o),u={fn:null},c=to(null),{fieldRef:d,state:h,scopedAttrsState:f,fixDisabledColor:p,trigger:v}=Lg(e,c,n,((t,n)=>{var r=t.target;if("number"===o.value){if(u.fn&&(r.removeEventListener("blur",u.fn),u.fn=null),r.validity&&!r.validity.valid){if((!l.value||!r.value)&&"-"===t.data||"-"===l.value[0]&&"deleteContentBackward"===t.inputType)return l.value="-",n.value="",u.fn=()=>{l.value=r.value=""},r.addEventListener("blur",u.fn),!1;var i=Ig(t,l,n,r,u);return"boolean"==typeof i?i:(l.value=n.value=r.value="-"===l.value?"":l.value,!1)}var a=Ig(t,l,n,r,u);if("boolean"==typeof a)return a;l.value=r.value;var s=n.maxlength;if(s>0&&r.value.length>s)return r.value=r.value.slice(0,s),n.value=r.value,(void 0!==e.modelValue&&null!==e.modelValue?e.modelValue.toString():"")!==r.value}}));Vo((()=>h.value),(t=>{"number"!==e.type||"-"===l.value&&""===t||(l.value=t.toString())}));var g=["number","digit"],m=jl((()=>g.includes(e.type)?e.step:""));function _(t){if("Enter"===t.key){var n=t.target;t.stopPropagation(),v("confirm",t,{value:n.value}),!e.confirmHold&&n.blur()}}return r({$triggerInput:e=>{n("update:modelValue",e.value),n("update:value",e.value),h.value=e.value}}),()=>{var t=e.disabled&&p?ml("input",{key:"disabled-input",ref:d,value:h.value,tabindex:"-1",readonly:!!e.disabled,type:o.value,maxlength:h.maxlength,step:m.value,class:"uni-input-input",style:e.cursorColor?{caretColor:e.cursorColor}:{},onFocus:e=>e.target.blur()},null,44,["value","readonly","type","maxlength","step","onFocus"]):ml("input",{key:"input",ref:d,value:h.value,onInput:e=>{h.value=e.target.value.toString()},disabled:!!e.disabled,type:o.value,maxlength:h.maxlength,step:m.value,enterkeyhint:e.confirmType,pattern:"number"===e.type?"[0-9]*":void 0,class:"uni-input-input",style:e.cursorColor?{caretColor:e.cursorColor}:{},autocomplete:s.value,onKeyup:_,inputmode:e.inputmode},null,44,["value","onInput","disabled","type","maxlength","step","enterkeyhint","pattern","autocomplete","onKeyup","inputmode"]);return ml("uni-input",{ref:c},[ml("div",{class:"uni-input-wrapper"},[Yo(ml("div",Sl(f.attrs,{style:e.placeholderStyle,class:["uni-input-placeholder",e.placeholderClass]}),[e.placeholder],16),[[Gl,!(h.value.length||"-"===l.value||l.value.includes("."))]]),"search"===e.confirmType?ml("form",{action:"",onSubmit:e=>e.preventDefault(),class:"uni-input-form"},[t],40,["onSubmit"]):t])],512)}}});var Dg,Bg,Rg=["class","style"],Fg=/^on[A-Z]+/,qg=function(){var e,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{excludeListeners:n=!1,excludeKeys:r=[]}=t,i=Ol(),a=no({}),o=no({}),s=no({}),l=r.concat(Rg);return i.attrs=qa(i.attrs),$o((()=>{var e,t=(e=i.attrs,Object.keys(e).map((t=>[t,e[t]]))).reduce(((e,t)=>{var[r,i]=t;return l.includes(r)?e.exclude[r]=i:Fg.test(r)?(n||(e.attrs[r]=i),e.listeners[r]=i):e.attrs[r]=i,e}),{exclude:{},attrs:{},listeners:{}});a.value=t.attrs,o.value=t.listeners,s.value=t.exclude}),null,e),{$attrs:a,$listeners:o,$excludeAttrs:s}};function jg(){wr((()=>{Dg||(Dg=plus.webview.currentWebview()),Bg||(Bg=(Dg.getStyle()||{}).pullToRefresh||{})}))}function Vg(e){var{disable:t}=e;Bg&&Bg.support&&Dg.setPullToRefresh(Object.assign({},Bg,{support:!t}))}function $g(e){Ol().rebuild=e}const Hg=Xp({inheritAttrs:!1,name:"MovableArea",props:{scaleArea:{type:Boolean,default:!1}},setup(e,t){var{slots:n}=t,r=to(null),i=to(!1),{setContexts:a,events:o}=function(e,t){var n=to(0),r=to(0),i=qa({x:null,y:null}),a=to(null),o=null,s=[];function l(t){t&&1!==t&&(e.scaleArea?s.forEach((function(e){e._setScale(t)})):o&&o._setScale(t))}function u(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:s,r=t.value;function i(e){for(var t=0;t<n.length;t++){var a=n[t];if(e===a.rootRef.value)return a}return e===r||e===document.body||e===document?null:i(e.parentNode)}return i(e)}var c=Gp((t=>{Vg({disable:!0});var n=t.touches;if(n&&n.length>1){var r={x:n[1].pageX-n[0].pageX,y:n[1].pageY-n[0].pageY};if(a.value=Wg(r),i.x=r.x,i.y=r.y,!e.scaleArea){var s=u(n[0].target),l=u(n[1].target);o=s&&s===l?s:null}}})),d=Gp((e=>{var t=e.touches;if(t&&t.length>1){e.preventDefault();var n={x:t[1].pageX-t[0].pageX,y:t[1].pageY-t[0].pageY};if(null!==i.x&&a.value&&a.value>0)l(Wg(n)/a.value);i.x=n.x,i.y=n.y}})),h=Gp((t=>{Vg({disable:!1});var n=t.touches;n&&n.length||t.changedTouches&&(i.x=0,i.y=0,a.value=null,e.scaleArea?s.forEach((function(e){e._endScale()})):o&&o._endScale())}));function f(){p(),s.forEach((function(e,t){e.setParent()}))}function p(){var e=window.getComputedStyle(t.value),i=t.value.getBoundingClientRect();n.value=i.width-["Left","Right"].reduce((function(t,n){var r="padding"+n;return t+parseFloat(e["border"+n+"Width"])+parseFloat(e[r])}),0),r.value=i.height-["Top","Bottom"].reduce((function(t,n){var r="padding"+n;return t+parseFloat(e["border"+n+"Width"])+parseFloat(e[r])}),0)}return Ps("movableAreaWidth",n),Ps("movableAreaHeight",r),{setContexts(e){s=e},events:{_onTouchstart:c,_onTouchmove:d,_onTouchend:h,_resize:f}}}(e,r),{$listeners:s,$attrs:l,$excludeAttrs:u}=qg(),c=s.value;["onTouchstart","onTouchmove","onTouchend"].forEach((e=>{var t=c[e],n=o["_".concat(e)];c[e]=t?[].concat(t,n):n})),as((()=>{o._resize(),jg(),i.value=!0}));var d=[],h=[];function f(){for(var e=[],t=function(t){var n=d[t];n instanceof Element||(n=n.el);var r=h.find((e=>n===e.rootRef.value));r&&e.push(Xa(r))},n=0;n<d.length;n++)t(n);a(e)}$g((()=>{d=r.value.children,f()}));return Ps("_isMounted",i),Ps("movableAreaRootRef",r),Ps("addMovableViewContext",(e=>{h.push(e),f()})),Ps("removeMovableViewContext",(e=>{var t=h.indexOf(e);t>=0&&(h.splice(t,1),f())})),()=>(n.default&&n.default(),ml("uni-movable-area",Sl({ref:r},l.value,u.value,c),[ml(uv,{onResize:o._resize},null,8,["onResize"]),d],16))}});function Wg(e){return Math.sqrt(e.x*e.x+e.y*e.y)}var Ug,Yg,Xg=function(e,t,n,r){e.addEventListener(t,(e=>{fn(n)&&!1===n(e)&&((void 0===e.cancelable||e.cancelable)&&e.preventDefault(),e.stopPropagation())}),{passive:!1})};function Zg(e,t,n){ls((()=>{document.removeEventListener("mousemove",Ug),document.removeEventListener("mouseup",Yg)}));var r,i,a=0,o=0,s=0,l=0,u=function(e,n,r,i){if(!1===t({cancelable:e.cancelable,target:e.target,currentTarget:e.currentTarget,preventDefault:e.preventDefault.bind(e),stopPropagation:e.stopPropagation.bind(e),touches:e.touches,changedTouches:e.changedTouches,detail:{state:n,x:r,y:i,dx:r-a,dy:i-o,ddx:r-s,ddy:i-l,timeStamp:e.timeStamp}}))return!1},c=null;Xg(e,"touchstart",(function(e){if(r=!0,1===e.touches.length&&!c)return c=e,a=s=e.touches[0].pageX,o=l=e.touches[0].pageY,u(e,"start",a,o)})),Xg(e,"mousedown",(function(e){if(i=!0,!r&&!c)return c=e,a=s=e.pageX,o=l=e.pageY,u(e,"start",a,o)})),Xg(e,"touchmove",(function(e){if(1===e.touches.length&&c){var t=u(e,"move",e.touches[0].pageX,e.touches[0].pageY);return s=e.touches[0].pageX,l=e.touches[0].pageY,t}}));var d=Ug=function(e){if(!r&&i&&c){var t=u(e,"move",e.pageX,e.pageY);return s=e.pageX,l=e.pageY,t}};document.addEventListener("mousemove",d),Xg(e,"touchend",(function(e){if(0===e.touches.length&&c)return r=!1,c=null,u(e,"end",e.changedTouches[0].pageX,e.changedTouches[0].pageY)}));var h=Yg=function(e){if(i=!1,!r&&c)return c=null,u(e,"end",e.pageX,e.pageY)};document.addEventListener("mouseup",h),Xg(e,"touchcancel",(function(e){if(c){r=!1;var t=c;return c=null,u(e,n?"cancel":"end",t.touches[0].pageX,t.touches[0].pageY)}}))}function Gg(e,t,n){return e>t-n&&e<t+n}function Kg(e,t){return Gg(e,0,t)}function Jg(){}function Qg(e,t){this._m=e,this._f=1e3*t,this._startTime=0,this._v=0}function em(e,t,n){this._m=e,this._k=t,this._c=n,this._solution=null,this._endPosition=0,this._startTime=0}function tm(e,t,n){this._springX=new em(e,t,n),this._springY=new em(e,t,n),this._springScale=new em(e,t,n),this._startTime=0}function nm(e,t){return+((1e3*e-1e3*t)/1e3).toFixed(1)}Jg.prototype.x=function(e){return Math.sqrt(e)},Qg.prototype.setV=function(e,t){var n=Math.pow(Math.pow(e,2)+Math.pow(t,2),.5);this._x_v=e,this._y_v=t,this._x_a=-this._f*this._x_v/n,this._y_a=-this._f*this._y_v/n,this._t=Math.abs(e/this._x_a)||Math.abs(t/this._y_a),this._lastDt=null,this._startTime=(new Date).getTime()},Qg.prototype.setS=function(e,t){this._x_s=e,this._y_s=t},Qg.prototype.s=function(e){void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3),e>this._t&&(e=this._t,this._lastDt=e);var t=this._x_v*e+.5*this._x_a*Math.pow(e,2)+this._x_s,n=this._y_v*e+.5*this._y_a*Math.pow(e,2)+this._y_s;return(this._x_a>0&&t<this._endPositionX||this._x_a<0&&t>this._endPositionX)&&(t=this._endPositionX),(this._y_a>0&&n<this._endPositionY||this._y_a<0&&n>this._endPositionY)&&(n=this._endPositionY),{x:t,y:n}},Qg.prototype.ds=function(e){return void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3),e>this._t&&(e=this._t),{dx:this._x_v+this._x_a*e,dy:this._y_v+this._y_a*e}},Qg.prototype.delta=function(){return{x:-1.5*Math.pow(this._x_v,2)/this._x_a||0,y:-1.5*Math.pow(this._y_v,2)/this._y_a||0}},Qg.prototype.dt=function(){return-this._x_v/this._x_a},Qg.prototype.done=function(){var e=Gg(this.s().x,this._endPositionX)||Gg(this.s().y,this._endPositionY)||this._lastDt===this._t;return this._lastDt=null,e},Qg.prototype.setEnd=function(e,t){this._endPositionX=e,this._endPositionY=t},Qg.prototype.reconfigure=function(e,t){this._m=e,this._f=1e3*t},em.prototype._solve=function(e,t){var n=this._c,r=this._m,i=this._k,a=n*n-4*r*i;if(0===a){var o=-n/(2*r),s=e,l=t/(o*e);return{x:function(e){return(s+l*e)*Math.pow(Math.E,o*e)},dx:function(e){var t=Math.pow(Math.E,o*e);return o*(s+l*e)*t+l*t}}}if(a>0){var u=(-n-Math.sqrt(a))/(2*r),c=(-n+Math.sqrt(a))/(2*r),d=(t-u*e)/(c-u),h=e-d;return{x:function(e){var t,n;return e===this._t&&(t=this._powER1T,n=this._powER2T),this._t=e,t||(t=this._powER1T=Math.pow(Math.E,u*e)),n||(n=this._powER2T=Math.pow(Math.E,c*e)),h*t+d*n},dx:function(e){var t,n;return e===this._t&&(t=this._powER1T,n=this._powER2T),this._t=e,t||(t=this._powER1T=Math.pow(Math.E,u*e)),n||(n=this._powER2T=Math.pow(Math.E,c*e)),h*u*t+d*c*n}}}var f=Math.sqrt(4*r*i-n*n)/(2*r),p=-n/2*r,v=e,g=(t-p*e)/f;return{x:function(e){return Math.pow(Math.E,p*e)*(v*Math.cos(f*e)+g*Math.sin(f*e))},dx:function(e){var t=Math.pow(Math.E,p*e),n=Math.cos(f*e),r=Math.sin(f*e);return t*(g*f*n-v*f*r)+p*t*(g*r+v*n)}}},em.prototype.x=function(e){return void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3),this._solution?this._endPosition+this._solution.x(e):0},em.prototype.dx=function(e){return void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3),this._solution?this._solution.dx(e):0},em.prototype.setEnd=function(e,t,n){if(n||(n=(new Date).getTime()),e!==this._endPosition||!Kg(t,.1)){t=t||0;var r=this._endPosition;this._solution&&(Kg(t,.1)&&(t=this._solution.dx((n-this._startTime)/1e3)),r=this._solution.x((n-this._startTime)/1e3),Kg(t,.1)&&(t=0),Kg(r,.1)&&(r=0),r+=this._endPosition),this._solution&&Kg(r-e,.1)&&Kg(t,.1)||(this._endPosition=e,this._solution=this._solve(r-this._endPosition,t),this._startTime=n)}},em.prototype.snap=function(e){this._startTime=(new Date).getTime(),this._endPosition=e,this._solution={x:function(){return 0},dx:function(){return 0}}},em.prototype.done=function(e){return e||(e=(new Date).getTime()),Gg(this.x(),this._endPosition,.1)&&Kg(this.dx(),.1)},em.prototype.reconfigure=function(e,t,n){this._m=e,this._k=t,this._c=n,this.done()||(this._solution=this._solve(this.x()-this._endPosition,this.dx()),this._startTime=(new Date).getTime())},em.prototype.springConstant=function(){return this._k},em.prototype.damping=function(){return this._c},em.prototype.configuration=function(){return[{label:"Spring Constant",read:this.springConstant.bind(this),write:function(e,t){e.reconfigure(1,t,e.damping())}.bind(this,this),min:100,max:1e3},{label:"Damping",read:this.damping.bind(this),write:function(e,t){e.reconfigure(1,e.springConstant(),t)}.bind(this,this),min:1,max:500}]},tm.prototype.setEnd=function(e,t,n,r){var i=(new Date).getTime();this._springX.setEnd(e,r,i),this._springY.setEnd(t,r,i),this._springScale.setEnd(n,r,i),this._startTime=i},tm.prototype.x=function(){var e=((new Date).getTime()-this._startTime)/1e3;return{x:this._springX.x(e),y:this._springY.x(e),scale:this._springScale.x(e)}},tm.prototype.done=function(){var e=(new Date).getTime();return this._springX.done(e)&&this._springY.done(e)&&this._springScale.done(e)},tm.prototype.reconfigure=function(e,t,n){this._springX.reconfigure(e,t,n),this._springY.reconfigure(e,t,n),this._springScale.reconfigure(e,t,n)};const rm=Xp({name:"MovableView",props:{direction:{type:String,default:"none"},inertia:{type:[Boolean,String],default:!1},outOfBounds:{type:[Boolean,String],default:!1},x:{type:[Number,String],default:0},y:{type:[Number,String],default:0},damping:{type:[Number,String],default:20},friction:{type:[Number,String],default:2},disabled:{type:[Boolean,String],default:!1},scale:{type:[Boolean,String],default:!1},scaleMin:{type:[Number,String],default:.5},scaleMax:{type:[Number,String],default:10},scaleValue:{type:[Number,String],default:1},animation:{type:[Boolean,String],default:!0}},emits:["change","scale"],setup(e,t){var{slots:n,emit:r}=t,i=to(null),a=Kp(i,r),{setParent:o}=function(e,t,n){var r,i,a=Ds("_isMounted",to(!1)),o=Ds("addMovableViewContext",(()=>{})),s=Ds("removeMovableViewContext",(()=>{})),l=to(1),u=to(1),c=to(!1),d=to(0),h=to(0),f=null,p=null,v=!1,g=null,m=null,_=new Jg,y=new Jg,b={historyX:[0,0],historyY:[0,0],historyT:[0,0]},w=jl((()=>{var t=Number(e.friction);return isNaN(t)||t<=0?2:t})),x=new Qg(1,w.value);Vo((()=>e.disabled),(()=>{W()}));var{_updateOldScale:S,_endScale:k,_setScale:C,scaleValueSync:T,_updateBoundary:A,_updateOffset:M,_updateWH:E,_scaleOffset:O,minX:L,minY:z,maxX:N,maxY:I,FAandSFACancel:P,_getLimitXY:D,_setTransform:B,_revise:R,dampingNumber:F,xMove:q,yMove:j,xSync:V,ySync:$,_STD:H}=function(e,t,n,r,i,a,o,s,l,u){var c=jl((()=>{var t=Number(e.scaleMin);return isNaN(t)?.5:t})),d=jl((()=>{var t=Number(e.scaleMax);return isNaN(t)?10:t})),h=to(Number(e.scaleValue)||1);Vo(h,(e=>{B(e)})),Vo(c,(()=>{D()})),Vo(d,(()=>{D()})),Vo((()=>e.scaleValue),(e=>{h.value=Number(e)||0}));var{_updateBoundary:f,_updateOffset:p,_updateWH:v,_scaleOffset:g,minX:m,minY:_,maxX:y,maxY:b}=function(e,t,n){var r=Ds("movableAreaWidth",to(0)),i=Ds("movableAreaHeight",to(0)),a=Ds("movableAreaRootRef"),o={x:0,y:0},s={x:0,y:0},l=to(0),u=to(0),c=to(0),d=to(0),h=to(0),f=to(0);function p(){var e=0-o.x+s.x,t=r.value-l.value-o.x-s.x;c.value=Math.min(e,t),h.value=Math.max(e,t);var n=0-o.y+s.y,a=i.value-u.value-o.y-s.y;d.value=Math.min(n,a),f.value=Math.max(n,a)}function v(){o.x=om(e.value,a.value),o.y=sm(e.value,a.value)}function g(r){r=r||t.value,r=n(r);var i=e.value.getBoundingClientRect();u.value=i.height/t.value,l.value=i.width/t.value;var a=u.value*r,o=l.value*r;s.x=(o-l.value)/2,s.y=(a-u.value)/2}return{_updateBoundary:p,_updateOffset:v,_updateWH:g,_scaleOffset:s,minX:c,minY:d,maxX:h,maxY:f}}(t,r,P),{FAandSFACancel:w,_getLimitXY:x,_animationTo:S,_setTransform:k,_revise:C,dampingNumber:T,xMove:A,yMove:M,xSync:E,ySync:O,_STD:L}=function(e,t,n,r,i,a,o,s,l,u,c,d,h,f){var p=jl((()=>{var e=Number(t.damping);return isNaN(e)?20:e})),v=jl((()=>"all"===t.direction||"horizontal"===t.direction)),g=jl((()=>"all"===t.direction||"vertical"===t.direction)),m=to(um(t.x)),_=to(um(t.y));Vo((()=>t.x),(e=>{m.value=um(e)})),Vo((()=>t.y),(e=>{_.value=um(e)})),Vo(m,(e=>{C(e)})),Vo(_,(e=>{T(e)}));var y=new tm(1,9*Math.pow(p.value,2)/40,p.value);function b(e,t){var n=!1;return e>i.value?(e=i.value,n=!0):e<o.value&&(e=o.value,n=!0),t>a.value?(t=a.value,n=!0):t<s.value&&(t=s.value,n=!0),{x:e,y:t,outOfBounds:n}}function w(){d&&d.cancel(),c&&c.cancel()}function x(e,n,i,a,o,s){w(),v.value||(e=l.value),g.value||(n=u.value),t.scale||(i=r.value);var d=b(e,n);e=d.x,n=d.y,t.animation?(y._springX._solution=null,y._springY._solution=null,y._springScale._solution=null,y._springX._endPosition=l.value,y._springY._endPosition=u.value,y._springScale._endPosition=r.value,y.setEnd(e,n,i,1),c=lm(y,(function(){var e=y.x();S(e.x,e.y,e.scale,a,o,s)}),(function(){c.cancel()}))):S(e,n,i,a,o,s)}function S(i,a,o){var s=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"",c=arguments.length>4?arguments[4]:void 0,d=arguments.length>5?arguments[5]:void 0;null!==i&&"NaN"!==i.toString()&&"number"==typeof i||(i=l.value||0),null!==a&&"NaN"!==a.toString()&&"number"==typeof a||(a=u.value||0),i=Number(i.toFixed(1)),a=Number(a.toFixed(1)),o=Number(o.toFixed(1)),l.value===i&&u.value===a||c||f("change",{},{x:nm(i,n.x),y:nm(a,n.y),source:s}),t.scale||(o=r.value),o=+(o=h(o)).toFixed(3),d&&o!==r.value&&f("scale",{},{x:i,y:a,scale:o});var p="translateX("+i+"px) translateY("+a+"px) translateZ(0px) scale("+o+")";e.value&&(e.value.style.transform=p,e.value.style.webkitTransform=p,l.value=i,u.value=a,r.value=o)}function k(e){var t=b(l.value,u.value),n=t.x,i=t.y,a=t.outOfBounds;return a&&x(n,i,r.value,e),a}function C(e){if(v.value){if(e+n.x===l.value)return l;c&&c.cancel(),x(e+n.x,_.value+n.y,r.value)}return e}function T(e){if(g.value){if(e+n.y===u.value)return u;c&&c.cancel(),x(m.value+n.x,e+n.y,r.value)}return e}return{FAandSFACancel:w,_getLimitXY:b,_animationTo:x,_setTransform:S,_revise:k,dampingNumber:p,xMove:v,yMove:g,xSync:m,ySync:_,_STD:y}}(t,e,g,r,y,b,m,_,o,s,l,u,P,n);function z(t,n){if(e.scale){t=P(t),v(t),f();var r=x(o.value,s.value),i=r.x,a=r.y;n?S(i,a,t,"",!0,!0):am((function(){k(i,a,t,"",!0,!0)}))}}function N(){a.value=!0}function I(e){i.value=e}function P(e){return e=Math.max(.5,c.value,e),e=Math.min(10,d.value,e)}function D(){if(!e.scale)return!1;z(r.value,!0),I(r.value)}function B(t){return!!e.scale&&(z(t=P(t),!0),I(t),t)}function R(){a.value=!1,I(r.value)}function F(e){e&&(e=i.value*e,N(),z(e))}return{_updateOldScale:I,_endScale:R,_setScale:F,scaleValueSync:h,_updateBoundary:f,_updateOffset:p,_updateWH:v,_scaleOffset:g,minX:m,minY:_,maxX:y,maxY:b,FAandSFACancel:w,_getLimitXY:x,_animationTo:S,_setTransform:k,_revise:C,dampingNumber:T,xMove:A,yMove:M,xSync:E,ySync:O,_STD:L}}(e,n,t,l,u,c,d,h,f,p);function W(){c.value||e.disabled||(Vg({disable:!0}),P(),b.historyX=[0,0],b.historyY=[0,0],b.historyT=[0,0],q.value&&(r=d.value),j.value&&(i=h.value),n.value.style.willChange="transform",g=null,m=null,v=!0)}function U(t){if(!c.value&&!e.disabled&&v){var n=d.value,a=h.value;if(null===m&&(m=Math.abs(t.detail.dx/t.detail.dy)>1?"htouchmove":"vtouchmove"),q.value&&(n=t.detail.dx+r,b.historyX.shift(),b.historyX.push(n),j.value||null!==g||(g=Math.abs(t.detail.dx/t.detail.dy)<1)),j.value&&(a=t.detail.dy+i,b.historyY.shift(),b.historyY.push(a),q.value||null!==g||(g=Math.abs(t.detail.dy/t.detail.dx)<1)),b.historyT.shift(),b.historyT.push(t.detail.timeStamp),!g){t.preventDefault();var o="touch";n<L.value?e.outOfBounds?(o="touch-out-of-bounds",n=L.value-_.x(L.value-n)):n=L.value:n>N.value&&(e.outOfBounds?(o="touch-out-of-bounds",n=N.value+_.x(n-N.value)):n=N.value),a<z.value?e.outOfBounds?(o="touch-out-of-bounds",a=z.value-y.x(z.value-a)):a=z.value:a>I.value&&(e.outOfBounds?(o="touch-out-of-bounds",a=I.value+y.x(a-I.value)):a=I.value),am((function(){B(n,a,l.value,o)}))}}}function Y(){if(!c.value&&!e.disabled&&v&&(Vg({disable:!1}),n.value.style.willChange="auto",v=!1,!g&&!R("out-of-bounds")&&e.inertia)){var t=1e3*(b.historyX[1]-b.historyX[0])/(b.historyT[1]-b.historyT[0]),r=1e3*(b.historyY[1]-b.historyY[0])/(b.historyT[1]-b.historyT[0]),i=d.value,a=h.value;x.setV(t,r),x.setS(i,a);var o=x.delta().x,s=x.delta().y,u=o+i,f=s+a;u<L.value?(u=L.value,f=a+(L.value-i)*s/o):u>N.value&&(u=N.value,f=a+(N.value-i)*s/o),f<z.value?(f=z.value,u=i+(z.value-a)*o/s):f>I.value&&(f=I.value,u=i+(I.value-a)*o/s),x.setEnd(u,f),p=lm(x,(function(){var e=x.s(),t=e.x,n=e.y;B(t,n,l.value,"friction")}),(function(){p.cancel()}))}e.outOfBounds||e.inertia||P()}function X(){if(a.value){P();var t=e.scale?T.value:1;M(),E(t),A();var n=D(V.value+O.x,$.value+O.y),r=n.x,i=n.y;B(r,i,t,"",!0),S(t)}}return as((()=>{Zg(n.value,(e=>{switch(e.detail.state){case"start":W();break;case"move":U(e);break;case"end":Y()}})),X(),x.reconfigure(1,w.value),H.reconfigure(1,9*Math.pow(F.value,2)/40,F.value),n.value.style.transformOrigin="center",jg();var e={rootRef:n,setParent:X,_endScale:k,_setScale:C};o(e),us((()=>{s(e)}))})),us((()=>{P()})),{setParent:X}}(e,a,i);return()=>ml("uni-movable-view",{ref:i},[ml(uv,{onResize:o},null,8,["onResize"]),n.default&&n.default()],512)}});var im=!1;function am(e){im||(im=!0,requestAnimationFrame((function(){e(),im=!1})))}function om(e,t){if(e===t)return 0;var n=e.offsetLeft;return e.offsetParent?n+=om(e.offsetParent,t):0}function sm(e,t){if(e===t)return 0;var n=e.offsetTop;return e.offsetParent?n+=sm(e.offsetParent,t):0}function lm(e,t,n){var r={id:0,cancelled:!1};return function e(t,n,r,i){if(!t||!t.cancelled){r(n);var a=n.done();a||t.cancelled||(t.id=requestAnimationFrame(e.bind(null,t,n,r,i))),a&&i&&i(n)}}(r,e,t,n),{cancel:function(e){e&&e.id&&cancelAnimationFrame(e.id),e&&(e.cancelled=!0)}.bind(null,r),model:e}}function um(e){return/\d+[ur]px$/i.test(e)?uni.upx2px(parseFloat(e)):Number(e)||0}var cm=["navigate","redirect","switchTab","reLaunch","navigateBack"],dm=["slide-in-right","slide-in-left","slide-in-top","slide-in-bottom","fade-in","zoom-out","zoom-fade-out","pop-in","none"],hm=["slide-out-right","slide-out-left","slide-out-top","slide-out-bottom","fade-out","zoom-in","zoom-fade-in","pop-out","none"],fm={hoverClass:{type:String,default:"navigator-hover"},url:{type:String,default:""},openType:{type:String,default:"navigate",validator:e=>Boolean(~cm.indexOf(e))},delta:{type:Number,default:1},hoverStartTime:{type:[Number,String],default:50},hoverStayTime:{type:[Number,String],default:600},exists:{type:String,default:""},hoverStopPropagation:{type:Boolean,default:!1},animationType:{type:String,default:"",validator:e=>!e||dm.concat(hm).includes(e)},animationDuration:{type:[String,Number],default:300}};const pm=Xp({name:"Navigator",inheritAttrs:!1,compatConfig:{MODE:3},props:on({},fm,{renderLink:{type:Boolean,default:!0}}),setup(e,t){var{slots:n}=t,r=to(null),i=Ol(),a=i&&i.vnode.scopeId||"",{hovering:o,binding:s}=Jp(e),l=function(e){return()=>{if("navigateBack"===e.openType||e.url){var t=parseInt(e.animationDuration);switch(e.openType){case"navigate":uni.navigateTo({url:e.url,animationType:e.animationType||"pop-in",animationDuration:t});break;case"redirect":uni.redirectTo({url:e.url,exists:e.exists});break;case"switchTab":uni.switchTab({url:e.url});break;case"reLaunch":uni.reLaunch({url:e.url});break;case"navigateBack":uni.navigateBack({delta:e.delta,animationType:e.animationType||"pop-out",animationDuration:t})}}else console.error("<navigator/> should have url attribute when using navigateTo, redirectTo, reLaunch or switchTab")}}(e);return()=>{var{hoverClass:t,url:u}=e,c=e.hoverClass&&"none"!==e.hoverClass,d=e.renderLink?ml("a",{class:"navigator-wrap",href:u,onClick:Tu,onMousedown:Tu},[n.default&&n.default()],40,["href","onClick","onMousedown"]):n.default&&n.default();return ml("uni-navigator",Sl({class:c&&o.value?t:"",ref:r},c&&s,i?i.attrs:{},{[a]:""},{onClick:l}),[d],16,["onClick"])}}});const vm=Xp({name:"PickerView",props:{value:{type:Array,default:()=>[],validator:function(e){return cn(e)&&e.filter((e=>"number"==typeof e)).length===e.length}},indicatorStyle:{type:String,default:""},indicatorClass:{type:String,default:""},maskStyle:{type:String,default:""},maskClass:{type:String,default:""}},emits:["change","pickstart","pickend","update:value"],setup(e,t){var{slots:n,emit:r}=t,i=to(null),a=to(null),o=Kp(i,r),s=function(e){var t=qa([...e.value]),n=qa({value:t,height:34});return Vo((()=>e.value),((e,t)=>{(e===t||e.length!==t.length||e.findIndex(((e,n)=>e!==t[n]))>=0)&&(n.value.length=e.length,e.forEach(((e,t)=>{e!==n.value[t]&&n.value.splice(t,1,e)})))})),n}(e),l=to(null),u=to([]),c=to([]);function d(e){var t=c.value;if(t instanceof HTMLCollection)return Array.prototype.indexOf.call(t,e.el);var n=(t=t.filter((e=>e.type!==sl))).indexOf(e);return-1!==n?n:u.value.indexOf(e)}return Ps("getPickerViewColumn",(function(e){return jl({get(){var t=d(e.vnode);return s.value[t]||0},set(t){var n=d(e.vnode);if(!(n<0)&&s.value[n]!==t){s.value[n]=t;var i=s.value.map((e=>e));r("update:value",i),o("change",{},{value:i})}}})})),Ps("pickerViewProps",e),Ps("pickerViewState",s),$g((()=>{var e;(e=l.value)&&(s.height=e.$el.offsetHeight),a.value&&(c.value=a.value.children)})),()=>{var e=n.default&&n.default();return ml("uni-picker-view",{ref:i},[ml(uv,{ref:l,onResize:e=>{var{height:t}=e;return s.height=t}},null,8,["onResize"]),ml("div",{ref:a,class:"uni-picker-view-wrapper"},[e],512)],512)}}});class gm{constructor(e){this._drag=e,this._dragLog=Math.log(e),this._x=0,this._v=0,this._startTime=0}set(e,t){this._x=e,this._v=t,this._startTime=(new Date).getTime()}setVelocityByEnd(e){this._v=(e-this._x)*this._dragLog/(Math.pow(this._drag,100)-1)}x(e){void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3);var t=e===this._dt&&this._powDragDt?this._powDragDt:this._powDragDt=Math.pow(this._drag,e);return this._dt=e,this._x+this._v*t/this._dragLog-this._v/this._dragLog}dx(e){void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3);var t=e===this._dt&&this._powDragDt?this._powDragDt:this._powDragDt=Math.pow(this._drag,e);return this._dt=e,this._v*t}done(){return Math.abs(this.dx())<3}reconfigure(e){var t=this.x(),n=this.dx();this._drag=e,this._dragLog=Math.log(e),this.set(t,n)}configuration(){var e=this;return[{label:"Friction",read:function(){return e._drag},write:function(t){e.reconfigure(t)},min:.001,max:.1,step:.001}]}}function mm(e,t,n){return e>t-n&&e<t+n}function _m(e,t){return mm(e,0,t)}class ym{constructor(e,t,n){this._m=e,this._k=t,this._c=n,this._solution=null,this._endPosition=0,this._startTime=0}_solve(e,t){var n=this._c,r=this._m,i=this._k,a=n*n-4*r*i;if(0===a){var o=-n/(2*r),s=e,l=t/(o*e);return{x:function(e){return(s+l*e)*Math.pow(Math.E,o*e)},dx:function(e){var t=Math.pow(Math.E,o*e);return o*(s+l*e)*t+l*t}}}if(a>0){var u=(-n-Math.sqrt(a))/(2*r),c=(-n+Math.sqrt(a))/(2*r),d=(t-u*e)/(c-u),h=e-d;return{x:function(e){var t,n;return e===this._t&&(t=this._powER1T,n=this._powER2T),this._t=e,t||(t=this._powER1T=Math.pow(Math.E,u*e)),n||(n=this._powER2T=Math.pow(Math.E,c*e)),h*t+d*n},dx:function(e){var t,n;return e===this._t&&(t=this._powER1T,n=this._powER2T),this._t=e,t||(t=this._powER1T=Math.pow(Math.E,u*e)),n||(n=this._powER2T=Math.pow(Math.E,c*e)),h*u*t+d*c*n}}}var f=Math.sqrt(4*r*i-n*n)/(2*r),p=-n/2*r,v=e,g=(t-p*e)/f;return{x:function(e){return Math.pow(Math.E,p*e)*(v*Math.cos(f*e)+g*Math.sin(f*e))},dx:function(e){var t=Math.pow(Math.E,p*e),n=Math.cos(f*e),r=Math.sin(f*e);return t*(g*f*n-v*f*r)+p*t*(g*r+v*n)}}}x(e){return void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3),this._solution?this._endPosition+this._solution.x(e):0}dx(e){return void 0===e&&(e=((new Date).getTime()-this._startTime)/1e3),this._solution?this._solution.dx(e):0}setEnd(e,t,n){if(n||(n=(new Date).getTime()),e!==this._endPosition||!_m(t,.4)){t=t||0;var r=this._endPosition;this._solution&&(_m(t,.4)&&(t=this._solution.dx((n-this._startTime)/1e3)),r=this._solution.x((n-this._startTime)/1e3),_m(t,.4)&&(t=0),_m(r,.4)&&(r=0),r+=this._endPosition),this._solution&&_m(r-e,.4)&&_m(t,.4)||(this._endPosition=e,this._solution=this._solve(r-this._endPosition,t),this._startTime=n)}}snap(e){this._startTime=(new Date).getTime(),this._endPosition=e,this._solution={x:function(){return 0},dx:function(){return 0}}}done(e){return e||(e=(new Date).getTime()),mm(this.x(),this._endPosition,.4)&&_m(this.dx(),.4)}reconfigure(e,t,n){this._m=e,this._k=t,this._c=n,this.done()||(this._solution=this._solve(this.x()-this._endPosition,this.dx()),this._startTime=(new Date).getTime())}springConstant(){return this._k}damping(){return this._c}configuration(){return[{label:"Spring Constant",read:this.springConstant.bind(this),write:function(e,t){e.reconfigure(1,t,e.damping())}.bind(this,this),min:100,max:1e3},{label:"Damping",read:this.damping.bind(this),write:function(e,t){e.reconfigure(1,e.springConstant(),t)}.bind(this,this),min:1,max:500}]}}class bm{constructor(e,t,n){this._extent=e,this._friction=t||new gm(.01),this._spring=n||new ym(1,90,20),this._startTime=0,this._springing=!1,this._springOffset=0}snap(e,t){this._springOffset=0,this._springing=!0,this._spring.snap(e),this._spring.setEnd(t)}set(e,t){this._friction.set(e,t),e>0&&t>=0?(this._springOffset=0,this._springing=!0,this._spring.snap(e),this._spring.setEnd(0)):e<-this._extent&&t<=0?(this._springOffset=0,this._springing=!0,this._spring.snap(e),this._spring.setEnd(-this._extent)):this._springing=!1,this._startTime=(new Date).getTime()}x(e){if(!this._startTime)return 0;if(e||(e=((new Date).getTime()-this._startTime)/1e3),this._springing)return this._spring.x()+this._springOffset;var t=this._friction.x(e),n=this.dx(e);return(t>0&&n>=0||t<-this._extent&&n<=0)&&(this._springing=!0,this._spring.setEnd(0,n),t<-this._extent?this._springOffset=-this._extent:this._springOffset=0,t=this._spring.x()+this._springOffset),t}dx(e){var t;return t=this._lastTime===e?this._lastDx:this._springing?this._spring.dx(e):this._friction.dx(e),this._lastTime=e,this._lastDx=t,t}done(){return this._springing?this._spring.done():this._friction.done()}setVelocityByEnd(e){this._friction.setVelocityByEnd(e)}configuration(){var e=this._friction.configuration();return e.push.apply(e,this._spring.configuration()),e}}class wm{constructor(e,t){t=t||{},this._element=e,this._options=t,this._enableSnap=t.enableSnap||!1,this._itemSize=t.itemSize||0,this._enableX=t.enableX||!1,this._enableY=t.enableY||!1,this._shouldDispatchScrollEvent=!!t.onScroll,this._enableX?(this._extent=(t.scrollWidth||this._element.offsetWidth)-this._element.parentElement.offsetWidth,this._scrollWidth=t.scrollWidth):(this._extent=(t.scrollHeight||this._element.offsetHeight)-this._element.parentElement.offsetHeight,this._scrollHeight=t.scrollHeight),this._position=0,this._scroll=new bm(this._extent,t.friction,t.spring),this._onTransitionEnd=this.onTransitionEnd.bind(this),this.updatePosition()}onTouchStart(){this._startPosition=this._position,this._lastChangePos=this._startPosition,this._startPosition>0?this._startPosition/=.5:this._startPosition<-this._extent&&(this._startPosition=(this._startPosition+this._extent)/.5-this._extent),this._animation&&(this._animation.cancel(),this._scrolling=!1),this.updatePosition()}onTouchMove(e,t){var n=this._startPosition;this._enableX?n+=e:this._enableY&&(n+=t),n>0?n*=.5:n<-this._extent&&(n=.5*(n+this._extent)-this._extent),this._position=n,this.updatePosition(),this.dispatchScroll()}onTouchEnd(e,t,n){if(this._enableSnap&&this._position>-this._extent&&this._position<0){if(this._enableY&&(Math.abs(t)<this._itemSize&&Math.abs(n.y)<300||Math.abs(n.y)<150))return void this.snap();if(this._enableX&&(Math.abs(e)<this._itemSize&&Math.abs(n.x)<300||Math.abs(n.x)<150))return void this.snap()}var r,i,a;if(this._enableX?this._scroll.set(this._position,n.x):this._enableY&&this._scroll.set(this._position,n.y),this._enableSnap){var o=this._scroll._friction.x(100),s=o%this._itemSize;(r=Math.abs(s)>this._itemSize/2?o-(this._itemSize-Math.abs(s)):o-s)<=0&&r>=-this._extent&&this._scroll.setVelocityByEnd(r)}this._lastTime=Date.now(),this._lastDelay=0,this._scrolling=!0,this._lastChangePos=this._position,this._lastIdx=Math.floor(Math.abs(this._position/this._itemSize)),this._animation=(i=this._scroll,function e(t,n,r,i){if(!t||!t.cancelled){r(n);var a=n.done();a||t.cancelled||(t.id=requestAnimationFrame(e.bind(null,t,n,r,i))),a&&i&&i(n)}}(a={id:0,cancelled:!1},i,(()=>{var e=Date.now(),t=(e-this._scroll._startTime)/1e3,n=this._scroll.x(t);this._position=n,this.updatePosition();var r=this._scroll.dx(t);this._shouldDispatchScrollEvent&&e-this._lastTime>this._lastDelay&&(this.dispatchScroll(),this._lastDelay=Math.abs(2e3/r),this._lastTime=e)}),(()=>{this._enableSnap&&(r<=0&&r>=-this._extent&&(this._position=r,this.updatePosition()),fn(this._options.onSnap)&&this._options.onSnap(Math.floor(Math.abs(this._position)/this._itemSize))),this._shouldDispatchScrollEvent&&this.dispatchScroll(),this._scrolling=!1})),{cancel:function(e){e&&e.id&&cancelAnimationFrame(e.id),e&&(e.cancelled=!0)}.bind(null,a),model:i})}onTransitionEnd(){this._element.style.webkitTransition="",this._element.style.transition="",this._element.removeEventListener("transitionend",this._onTransitionEnd),this._snapping&&(this._snapping=!1),this.dispatchScroll()}snap(){var e=this._itemSize,t=this._position%e,n=Math.abs(t)>this._itemSize/2?this._position-(e-Math.abs(t)):this._position-t;this._position!==n&&(this._snapping=!0,this.scrollTo(-n),fn(this._options.onSnap)&&this._options.onSnap(Math.floor(Math.abs(this._position)/this._itemSize)))}scrollTo(e,t){this._animation&&(this._animation.cancel(),this._scrolling=!1),"number"==typeof e&&(this._position=-e),this._position<-this._extent?this._position=-this._extent:this._position>0&&(this._position=0);var n="transform "+(t||.2)+"s ease-out";this._element.style.webkitTransition="-webkit-"+n,this._element.style.transition=n,this.updatePosition(),this._element.addEventListener("transitionend",this._onTransitionEnd)}dispatchScroll(){if(fn(this._options.onScroll)&&Math.round(Number(this._lastPos))!==Math.round(this._position)){this._lastPos=this._position;var e={target:{scrollLeft:this._enableX?-this._position:0,scrollTop:this._enableY?-this._position:0,scrollHeight:this._scrollHeight||this._element.offsetHeight,scrollWidth:this._scrollWidth||this._element.offsetWidth,offsetHeight:this._element.parentElement.offsetHeight,offsetWidth:this._element.parentElement.offsetWidth}};this._options.onScroll(e)}}update(e,t,n){var r=0,i=this._position;this._enableX?(r=this._element.childNodes.length?(t||this._element.offsetWidth)-this._element.parentElement.offsetWidth:0,this._scrollWidth=t):(r=this._element.childNodes.length?(t||this._element.offsetHeight)-this._element.parentElement.offsetHeight:0,this._scrollHeight=t),"number"==typeof e&&(this._position=-e),this._position<-r?this._position=-r:this._position>0&&(this._position=0),this._itemSize=n||this._itemSize,this.updatePosition(),i!==this._position&&(this.dispatchScroll(),fn(this._options.onSnap)&&this._options.onSnap(Math.floor(Math.abs(this._position)/this._itemSize))),this._extent=r,this._scroll._extent=r}updatePosition(){var e="";this._enableX?e="translateX("+this._position+"px) translateZ(0)":this._enableY&&(e="translateY("+this._position+"px) translateZ(0)"),this._element.style.webkitTransform=e,this._element.style.transform=e}isScrolling(){return this._scrolling||this._snapping}}const xm=Xp({name:"PickerViewColumn",setup(e,t){var n,r,{slots:i,emit:a}=t,o=to(null),s=to(null),l=Ds("getPickerViewColumn"),u=Ol(),c=l?l(u):to(0),d=Ds("pickerViewProps"),h=Ds("pickerViewState"),f=to(34),p=to(null),v=jl((()=>(h.height-f.value)/2)),{state:g}=wg(),m=qa({current:c.value,length:0});function _(){n&&!r&&(r=!0,bo((()=>{r=!1;var e=Math.min(m.current,m.length-1);e=Math.max(e,0),n.update(e*f.value,void 0,f.value)})))}Vo((()=>c.value),(e=>{e!==m.current&&(m.current=e,_())})),Vo((()=>m.current),(e=>c.value=e)),Vo([()=>f.value,()=>m.length,()=>h.height],_);var y=0;function b(e){var t=y+e.deltaY;if(Math.abs(t)>10){y=0;var r=Math.min(m.current+(t<0?-1:1),m.length-1);m.current=r=Math.max(r,0),n.scrollTo(r*f.value)}else y=t;e.preventDefault()}function w(e){var{clientY:t}=e,r=o.value;if(!n.isScrolling()){var i=t-r.getBoundingClientRect().top-h.height/2,a=f.value/2;if(!(Math.abs(i)<=a)){var s=Math.ceil((Math.abs(i)-a)/f.value),l=i<0?-s:s,u=Math.min(m.current+l,m.length-1);m.current=u=Math.max(u,0),n.scrollTo(u*f.value)}}}var x=()=>{var e,t,r,i=o.value,a=s.value,{scroller:l,handleTouchStart:u,handleTouchMove:c,handleTouchEnd:d}=function(e,t){var n={trackingID:-1,maxDy:0,maxDx:0},r=new wm(e,t);function i(e){var t=e,r=e;return"move"===t.detail.state||"end"===t.detail.state?{x:t.detail.dx,y:t.detail.dy}:{x:r.screenX-n.x,y:r.screenY-n.y}}return{scroller:r,handleTouchStart:function(e){var t=e,i=e;"start"===t.detail.state?(n.trackingID="touch",n.x=t.detail.x,n.y=t.detail.y):(n.trackingID="mouse",n.x=i.screenX,n.y=i.screenY),n.maxDx=0,n.maxDy=0,n.historyX=[0],n.historyY=[0],n.historyTime=[t.detail.timeStamp||i.timeStamp],n.listener=r,r.onTouchStart&&r.onTouchStart(),("boolean"!=typeof e.cancelable||e.cancelable)&&e.preventDefault()},handleTouchMove:function(e){var t=e,r=e;if(-1!==n.trackingID){("boolean"!=typeof e.cancelable||e.cancelable)&&e.preventDefault();var a=i(e);if(a){for(n.maxDy=Math.max(n.maxDy,Math.abs(a.y)),n.maxDx=Math.max(n.maxDx,Math.abs(a.x)),n.historyX.push(a.x),n.historyY.push(a.y),n.historyTime.push(t.detail.timeStamp||r.timeStamp);n.historyTime.length>10;)n.historyTime.shift(),n.historyX.shift(),n.historyY.shift();n.listener&&n.listener.onTouchMove&&n.listener.onTouchMove(a.x,a.y)}}},handleTouchEnd:function(e){if(-1!==n.trackingID){e.preventDefault();var t=i(e);if(t){var r=n.listener;n.trackingID=-1,n.listener=null;var a={x:0,y:0};if(n.historyTime.length>2)for(var o=n.historyTime.length-1,s=n.historyTime[o],l=n.historyX[o],u=n.historyY[o];o>0;){o--;var c=s-n.historyTime[o];if(c>30&&c<50){a.x=(l-n.historyX[o])/(c/1e3),a.y=(u-n.historyY[o])/(c/1e3);break}}n.historyTime=[],n.historyX=[],n.historyY=[],r&&r.onTouchEnd&&r.onTouchEnd(t.x,t.y,a)}}}}}(a,{enableY:!0,enableX:!1,enableSnap:!0,itemSize:f.value,friction:new gm(1e-4),spring:new ym(2,90,20),onSnap:e=>{isNaN(e)||e===m.current||(m.current=e)}});n=l,Zg(i,(e=>{switch(e.detail.state){case"start":u(e),Vg({disable:!0});break;case"move":c(e),e.stopPropagation();break;case"end":case"cancel":d(e),Vg({disable:!1})}}),!0),t=0,r=0,(e=i).addEventListener("touchstart",(e=>{var n=e.changedTouches[0];t=n.clientX,r=n.clientY})),e.addEventListener("touchend",(e=>{var n=e.changedTouches[0];if(Math.abs(n.clientX-t)<20&&Math.abs(n.clientY-r)<20){var i={bubbles:!0,cancelable:!0,target:e.target,currentTarget:e.currentTarget},a=new CustomEvent("click",i);["screenX","screenY","clientX","clientY","pageX","pageY"].forEach((e=>{a[e]=n[e]})),e.target.dispatchEvent(a)}})),jg(),_()},S=!1;return $g((()=>{var e;s.value&&(m.length=s.value.children.length),S||(S=!0,e=p.value,f.value=e.$el.offsetHeight,x())})),()=>{var e=i.default&&i.default(),t="".concat(v.value,"px 0");return ml("uni-picker-view-column",{ref:o},[ml("div",{onWheel:b,onClick:w,class:"uni-picker-view-group"},[ml("div",Sl(g.attrs,{class:["uni-picker-view-mask",d.maskClass],style:"background-size: 100% ".concat(v.value,"px;").concat(d.maskStyle)}),null,16),ml("div",Sl(g.attrs,{class:["uni-picker-view-indicator",d.indicatorClass],style:d.indicatorStyle}),[ml(uv,{ref:p,onResize:e=>{var{height:t}=e;return f.value=t}},null,8,["onResize"])],16),ml("div",{ref:s,class:["uni-picker-view-content"],style:{padding:t,"--picker-view-column-indicator-height":"".concat(f.value,"px")}},[e],4)],40,["onWheel","onClick"])],512)}}});var Sm=er,km="backwards";const Cm=Xp({name:"Progress",props:{percent:{type:[Number,String],default:0,validator:e=>!isNaN(parseFloat(e))},fontSize:{type:[String,Number],default:16},showInfo:{type:[Boolean,String],default:!1},strokeWidth:{type:[Number,String],default:6,validator:e=>!isNaN(parseFloat(e))},color:{type:String,default:Sm},activeColor:{type:String,default:Sm},backgroundColor:{type:String,default:"#EBEBEB"},active:{type:[Boolean,String],default:!1},activeMode:{type:String,default:km},duration:{type:[Number,String],default:30,validator:e=>!isNaN(parseFloat(e))},borderRadius:{type:[Number,String],default:0}},setup(e){var t=to(null),n=function(e){var t=to(0),n=jl((()=>"background-color: ".concat(e.backgroundColor,"; height: ").concat(e.strokeWidth,"px;"))),r=jl((()=>{var n=e.color!==Sm&&e.activeColor===Sm?e.color:e.activeColor;return"width: ".concat(t.value,"%;background-color: ").concat(n)})),i=jl((()=>{if("string"==typeof e.percent&&!/^-?\d*\.?\d*$/.test(e.percent))return 0;var t=parseFloat(e.percent);return Number.isNaN(t)||t<0?t=0:t>100&&(t=100),t})),a=qa({outerBarStyle:n,innerBarStyle:r,realPercent:i,currentPercent:t,strokeTimer:0,lastPercent:0});return a}(e);return Tm(n,e),Vo((()=>n.realPercent),((t,r)=>{n.strokeTimer&&clearInterval(n.strokeTimer),n.lastPercent=r||0,Tm(n,e)})),()=>{var{showInfo:r}=e,{outerBarStyle:i,innerBarStyle:a,currentPercent:o}=n;return ml("uni-progress",{class:"uni-progress",ref:t},[ml("div",{style:i,class:"uni-progress-bar"},[ml("div",{style:a,class:"uni-progress-inner-bar"},null,4)],4),r?ml("p",{class:"uni-progress-info"},[o+"%"]):""],512)}}});function Tm(e,t){t.active?(e.currentPercent=t.activeMode===km?0:e.lastPercent,e.strokeTimer=setInterval((()=>{e.currentPercent+1>e.realPercent?(e.currentPercent=e.realPercent,e.strokeTimer&&clearInterval(e.strokeTimer)):e.currentPercent+=1}),parseFloat(t.duration))):e.currentPercent=e.realPercent}var Am=Eu("ucg");const Mm=Xp({name:"RadioGroup",props:{name:{type:String,default:""}},setup(e,t){var{emit:n,slots:r}=t,i=to(null);return function(e,t){var n=[];as((()=>{s(n.length-1)}));var r=()=>{var e;return null===(e=n.find((e=>e.value.radioChecked)))||void 0===e?void 0:e.value.value};Ps(Am,{addField(e){n.push(e)},removeField(e){n.splice(n.indexOf(e),1)},radioChange(e,i){s(n.indexOf(i),!0),t("change",e,{value:r()})}});var i=Ds(ev,!1),a={submit:()=>{var t=["",null];return""!==e.name&&(t[0]=e.name,t[1]=r()),t}};i&&(i.addField(a),ls((()=>{i.removeField(a)})));function o(e,t){e.value={radioChecked:t,value:e.value.value}}function s(e,t){n.forEach(((r,i)=>{i!==e&&(t?o(n[i],!1):n.forEach(((e,t)=>{i>=t||n[t].value.radioChecked&&o(n[i],!1)})))}))}}(e,Kp(i,n)),()=>ml("uni-radio-group",{ref:i},[r.default&&r.default()],512)}});const Em=Xp({name:"Radio",props:{checked:{type:[Boolean,String],default:!1},id:{type:String,default:""},disabled:{type:[Boolean,String],default:!1},value:{type:String,default:""},color:{type:String,default:"#007aff"},backgroundColor:{type:String,default:""},borderColor:{type:String,default:""},activeBackgroundColor:{type:String,default:""},activeBorderColor:{type:String,default:""},iconColor:{type:String,default:"#ffffff"}},setup(e,t){var{slots:n}=t,r=to(null),i=to(e.checked),a=to(e.value);var o=jl((()=>function(){if(e.disabled)return{backgroundColor:"#E1E1E1",borderColor:"#D1D1D1"};var t={};return i.value?(t.backgroundColor=e.activeBackgroundColor||e.color,t.borderColor=e.activeBorderColor||t.backgroundColor):(e.borderColor&&(t.borderColor=e.borderColor),e.backgroundColor&&(t.backgroundColor=e.backgroundColor)),t}(i.value)));Vo([()=>e.checked,()=>e.value],(e=>{var[t,n]=e;i.value=t,a.value=n}));var{uniCheckGroup:s,uniLabel:l,field:u}=function(e,t,n){var r=jl({get:()=>({radioChecked:Boolean(e.value),value:t.value}),set:t=>{var{radioChecked:n}=t;e.value=n}}),i={reset:n},a=Ds(Am,!1);a&&a.addField(r);var o=Ds(ev,!1);o&&o.addField(i);var s=Ds(rv,!1);return ls((()=>{a&&a.removeField(r),o&&o.removeField(i)})),{uniCheckGroup:a,uniForm:o,uniLabel:s,field:r}}(i,a,(()=>{i.value=!1})),c=t=>{e.disabled||i.value||(i.value=!0,s&&s.radioChange(t,u),t.stopPropagation())};return l&&(l.addHandler(c),ls((()=>{l.removeHandler(c)}))),av(e,{"label-click":c}),()=>{var t,a=Qp(e,"disabled");return t=i.value,ml("uni-radio",Sl(a,{id:e.id,onClick:c,ref:r}),[ml("div",{class:"uni-radio-wrapper",style:{"--HOVER-BD-COLOR":i.value?o.value.borderColor:e.activeBorderColor}},[ml("div",{class:["uni-radio-input",{"uni-radio-input-disabled":e.disabled}],style:o.value},[t?Pu(Nu,e.disabled?"#ADADAD":e.iconColor,18):""],6),n.default&&n.default()],4)],16,["id","onClick"])}}});var Om={a:"",abbr:"",address:"",article:"",aside:"",b:"",bdi:"",bdo:["dir"],big:"",blockquote:"",br:"",caption:"",center:"",cite:"",code:"",col:["span","width"],colgroup:["span","width"],dd:"",del:"",div:"",dl:"",dt:"",em:"",fieldset:"",font:"",footer:"",h1:"",h2:"",h3:"",h4:"",h5:"",h6:"",header:"",hr:"",i:"",img:["alt","src","height","width"],ins:"",label:"",legend:"",li:"",mark:"",nav:"",ol:["start","type"],p:"",pre:"",q:"",rt:"",ruby:"",s:"",section:"",small:"",span:"",strong:"",sub:"",sup:"",table:["width"],tbody:"",td:["colspan","height","rowspan","width"],tfoot:"",th:["colspan","height","rowspan","width"],thead:"",tr:["colspan","height","rowspan","width"],tt:"",u:"",ul:""},Lm={amp:"&",gt:">",lt:"<",nbsp:" ",quot:'"',apos:"'",ldquo:"“",rdquo:"”",yen:"￥",radic:"√",lceil:"⌈",rceil:"⌉",lfloor:"⌊",rfloor:"⌋",hellip:"…"};var zm=(e,t,n)=>!n||cn(n)&&!n.length?[]:n.map((n=>{var r;if(wn(n)){if(!un(n,"type")||"node"===n.type){var i={[e]:""},a=null==(r=n.name)?void 0:r.toLowerCase();if(!un(Om,a))return;return function(e,t){if(wn(t))for(var n in t)if(un(t,n)){var r=t[n];"img"===e&&"src"===n&&(t[n]=yc(r))}}(a,n.attrs),i=on(i,function(e,t){if(["a","img"].includes(e.name)&&t)return{onClick:n=>{t(n,{node:e}),n.stopPropagation(),n.preventDefault(),n.returnValue=!1}}}(n,t),n.attrs),Vl(n.name,i,zm(e,t,n.children))}return"text"===n.type&&pn(n.text)&&""!==n.text?yl((n.text||"").replace(/&(([a-zA-Z]+)|(#x{0,1}[\da-zA-Z]+));/gi,(function(e,t){return un(Lm,t)&&Lm[t]?Lm[t]:/^#[0-9]{1,4}$/.test(t)?String.fromCharCode(t.slice(1)):/^#x[0-9a-f]{1,4}$/i.test(t)?String.fromCharCode(0+t.slice(1)):e}))):void 0}}));function Nm(e){e=function(e){return e.replace(/<\?xml.*\?>\n/,"").replace(/<!doctype.*>\n/,"").replace(/<!DOCTYPE.*>\n/,"")}(e);var t=[],n={node:"root",children:[]};return $v(e,{start:function(e,r,i){var a={name:e};if(0!==r.length&&(a.attrs=function(e){return e.reduce((function(e,t){var n=t.value,r=t.name;return n.match(/ /)&&-1===["style","src"].indexOf(r)&&(n=n.split(" ")),e[r]?Array.isArray(e[r])?e[r].push(n):e[r]=[e[r],n]:e[r]=n,e}),{})}(r)),i){var o=t[0]||n;o.children||(o.children=[]),o.children.push(a)}else t.unshift(a)},end:function(e){var r=t.shift();if(r.name!==e&&console.error("invalid state: mismatch end tag"),0===t.length)n.children.push(r);else{var i=t[0];i.children||(i.children=[]),i.children.push(r)}},chars:function(e){var r={type:"text",text:e};if(0===t.length)n.children.push(r);else{var i=t[0];i.children||(i.children=[]),i.children.push(r)}},comment:function(e){var n={node:"comment",text:e},r=t[0];r&&(r.children||(r.children=[]),r.children.push(n))}}),n.children}const Im=Xp({name:"RichText",compatConfig:{MODE:3},props:{nodes:{type:[Array,String],default:function(){return[]}}},emits:["itemclick"],setup(e,t){var{emit:n}=t,r=Ol(),i=r&&r.vnode.scopeId||"",a=to(null),o=to([]),s=Kp(a,n);function l(e){s("itemclick",e,arguments.length>1&&void 0!==arguments[1]?arguments[1]:{})}return Vo((()=>e.nodes),(function(){var t=e.nodes;pn(t)&&(t=Nm(e.nodes)),o.value=zm(i,l,t)}),{immediate:!0}),()=>Vl("uni-rich-text",{ref:a},Vl("div",{},o.value))}}),Pm=Xp({name:"Refresher",props:{refreshState:{type:String,default:""},refresherHeight:{type:Number,default:0},refresherThreshold:{type:Number,default:45},refresherDefaultStyle:{type:String,default:"black"},refresherBackground:{type:String,default:"#fff"}},setup(e,t){var{slots:n}=t,r=to(null),i=jl((()=>{var t={backgroundColor:e.refresherBackground};switch(e.refreshState){case"pulling":t.height=e.refresherHeight+"px";break;case"refreshing":t.height=e.refresherThreshold+"px",t.transition="height 0.3s";break;case"":case"refresherabort":case"restore":t.height="0px",t.transition="height 0.3s"}return t})),a=jl((()=>{var t=e.refresherHeight/e.refresherThreshold;return 360*(t>1?1:t)}));return()=>{var{refreshState:t,refresherDefaultStyle:o,refresherThreshold:s}=e;return ml("div",{ref:r,style:i.value,class:"uni-scroll-view-refresher"},["none"!==o?ml("div",{class:"uni-scroll-view-refresh"},[ml("div",{class:"uni-scroll-view-refresh-inner"},["pulling"==t?ml("svg",{key:"refresh__icon",style:{transform:"rotate("+a.value+"deg)"},fill:"#2BD009",class:"uni-scroll-view-refresh__icon",width:"24",height:"24",viewBox:"0 0 24 24"},[ml("path",{d:"M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"},null),ml("path",{d:"M0 0h24v24H0z",fill:"none"},null)],4):null,"refreshing"==t?ml("svg",{key:"refresh__spinner",class:"uni-scroll-view-refresh__spinner",width:"24",height:"24",viewBox:"25 25 50 50"},[ml("circle",{cx:"50",cy:"50",r:"20",fill:"none",style:"color: #2bd009","stroke-width":"3"},null)]):null])]):null,"none"===o?ml("div",{class:"uni-scroll-view-refresher-container",style:{height:"".concat(s,"px")}},[n.default&&n.default()]):null],4)}}});var Dm=yr(!0);const Bm=Xp({name:"ScrollView",compatConfig:{MODE:3},props:{direction:{type:[String],default:"vertical"},scrollX:{type:[Boolean,String],default:!1},scrollY:{type:[Boolean,String],default:!1},showScrollbar:{type:[Boolean,String],default:!0},upperThreshold:{type:[Number,String],default:50},lowerThreshold:{type:[Number,String],default:50},scrollTop:{type:[Number,String],default:0},scrollLeft:{type:[Number,String],default:0},scrollIntoView:{type:String,default:""},scrollWithAnimation:{type:[Boolean,String],default:!1},enableBackToTop:{type:[Boolean,String],default:!1},refresherEnabled:{type:[Boolean,String],default:!1},refresherThreshold:{type:Number,default:45},refresherDefaultStyle:{type:String,default:"black"},refresherBackground:{type:String,default:"#fff"},refresherTriggered:{type:[Boolean,String],default:!1}},emits:["scroll","scrolltoupper","scrolltolower","refresherrefresh","refresherrestore","refresherpulling","refresherabort","update:refresherTriggered"],setup(e,t){var{emit:n,slots:r,expose:i}=t,a=to(null),o=to(null),s=to(null),l=to(null),u=Kp(a,n),{state:c,scrollTopNumber:d,scrollLeftNumber:h}=function(e){var t=jl((()=>Number(e.scrollTop)||0)),n=jl((()=>Number(e.scrollLeft)||0)),r=qa({lastScrollTop:t.value,lastScrollLeft:n.value,lastScrollToUpperTime:0,lastScrollToLowerTime:0,refresherHeight:0,refreshState:""});return{state:r,scrollTopNumber:t,scrollLeftNumber:n}}(e),{realScrollX:f,realScrollY:p,_scrollLeftChanged:v,_scrollTopChanged:g}=function(e,t,n,r,i,a,o,s,l){var u=!1,c=0,d=!1,h=()=>{},f=jl((()=>e.scrollX)),p=jl((()=>e.scrollY)),v=jl((()=>{var t=Number(e.upperThreshold);return isNaN(t)?50:t})),g=jl((()=>{var t=Number(e.lowerThreshold);return isNaN(t)?50:t}));function m(e,t){var n=o.value,r=0,i="";if(e<0?e=0:"x"===t&&e>n.scrollWidth-n.offsetWidth?e=n.scrollWidth-n.offsetWidth:"y"===t&&e>n.scrollHeight-n.offsetHeight&&(e=n.scrollHeight-n.offsetHeight),"x"===t?r=n.scrollLeft-e:"y"===t&&(r=n.scrollTop-e),0!==r){var a=s.value;a.style.transition="transform .3s ease-out",a.style.webkitTransition="-webkit-transform .3s ease-out","x"===t?i="translateX("+r+"px) translateZ(0)":"y"===t&&(i="translateY("+r+"px) translateZ(0)"),a.removeEventListener("transitionend",h),a.removeEventListener("webkitTransitionEnd",h),h=()=>x(e,t),a.addEventListener("transitionend",h),a.addEventListener("webkitTransitionEnd",h),"x"===t?n.style.overflowX="hidden":"y"===t&&(n.style.overflowY="hidden"),a.style.transform=i,a.style.webkitTransform=i}}function _(e){var n=e.target;i("scroll",e,{scrollLeft:n.scrollLeft,scrollTop:n.scrollTop,scrollHeight:n.scrollHeight,scrollWidth:n.scrollWidth,deltaX:t.lastScrollLeft-n.scrollLeft,deltaY:t.lastScrollTop-n.scrollTop}),p.value&&(n.scrollTop<=v.value&&t.lastScrollTop-n.scrollTop>0&&e.timeStamp-t.lastScrollToUpperTime>200&&(i("scrolltoupper",e,{direction:"top"}),t.lastScrollToUpperTime=e.timeStamp),n.scrollTop+n.offsetHeight+g.value>=n.scrollHeight&&t.lastScrollTop-n.scrollTop<0&&e.timeStamp-t.lastScrollToLowerTime>200&&(i("scrolltolower",e,{direction:"bottom"}),t.lastScrollToLowerTime=e.timeStamp)),f.value&&(n.scrollLeft<=v.value&&t.lastScrollLeft-n.scrollLeft>0&&e.timeStamp-t.lastScrollToUpperTime>200&&(i("scrolltoupper",e,{direction:"left"}),t.lastScrollToUpperTime=e.timeStamp),n.scrollLeft+n.offsetWidth+g.value>=n.scrollWidth&&t.lastScrollLeft-n.scrollLeft<0&&e.timeStamp-t.lastScrollToLowerTime>200&&(i("scrolltolower",e,{direction:"right"}),t.lastScrollToLowerTime=e.timeStamp)),t.lastScrollTop=n.scrollTop,t.lastScrollLeft=n.scrollLeft}function y(t){p.value&&(e.scrollWithAnimation?m(t,"y"):o.value.scrollTop=t)}function b(t){f.value&&(e.scrollWithAnimation?m(t,"x"):o.value.scrollLeft=t)}function w(t){if(t){if(!/^[_a-zA-Z][-_a-zA-Z0-9:]*$/.test(t))return void console.error("id error: scroll-into-view=".concat(t));var n=a.value.querySelector("#"+t);if(n){var r=o.value.getBoundingClientRect(),i=n.getBoundingClientRect();if(f.value){var s=i.left-r.left,l=o.value.scrollLeft+s;e.scrollWithAnimation?m(l,"x"):o.value.scrollLeft=l}if(p.value){var u=i.top-r.top,c=o.value.scrollTop+u;e.scrollWithAnimation?m(c,"y"):o.value.scrollTop=c}}}}function x(e,t){s.value.style.transition="",s.value.style.webkitTransition="",s.value.style.transform="",s.value.style.webkitTransform="";var n=o.value;"x"===t?(n.style.overflowX=f.value?"auto":"hidden",n.scrollLeft=e):"y"===t&&(n.style.overflowY=p.value?"auto":"hidden",n.scrollTop=e),s.value.removeEventListener("transitionend",h),s.value.removeEventListener("webkitTransitionEnd",h)}function S(n){if(e.refresherEnabled){switch(n){case"refreshing":t.refresherHeight=e.refresherThreshold,u||(u=!0,i("refresherpulling",{},{deltaY:t.refresherHeight,dy:t.refresherHeight}),i("refresherrefresh",{},{dy:C.y-k.y}),l("update:refresherTriggered",!0));break;case"restore":case"refresherabort":u=!1,t.refresherHeight=c=0,"restore"===n&&(d=!1,i("refresherrestore",{},{dy:C.y-k.y})),"refresherabort"===n&&d&&(d=!1,i("refresherabort",{},{dy:C.y-k.y}))}t.refreshState=n}}var k={x:0,y:0},C={x:0,y:e.refresherThreshold};return as((()=>{bo((()=>{y(n.value),b(r.value)})),w(e.scrollIntoView);var a=function(e){e.preventDefault(),e.stopPropagation(),_(e)},s=null,l=function(n){if(null!==k){var r=n.touches[0].pageX,a=n.touches[0].pageY,l=o.value;if(Math.abs(r-k.x)>Math.abs(a-k.y))if(f.value){if(0===l.scrollLeft&&r>k.x)return void(s=!1);if(l.scrollWidth===l.offsetWidth+l.scrollLeft&&r<k.x)return void(s=!1);s=!0}else s=!1;else if(p.value)if(0===l.scrollTop&&a>k.y)s=!1,e.refresherEnabled&&!1!==n.cancelable&&n.preventDefault();else{if(l.scrollHeight===l.offsetHeight+l.scrollTop&&a<k.y)return void(s=!1);s=!0}else s=!1;if(s&&n.stopPropagation(),0===l.scrollTop&&1===n.touches.length&&S("pulling"),e.refresherEnabled&&"pulling"===t.refreshState){var h=a-k.y;0===c&&(c=a),u?(t.refresherHeight=h+e.refresherThreshold,d=!1):(t.refresherHeight=a-c,t.refresherHeight>0&&(d=!0,i("refresherpulling",n,{deltaY:h,dy:h})))}}},h=function(e){1===e.touches.length&&(Vg({disable:!0}),k={x:e.touches[0].pageX,y:e.touches[0].pageY})},v=function(n){C={x:n.changedTouches[0].pageX,y:n.changedTouches[0].pageY},Vg({disable:!1}),t.refresherHeight>=e.refresherThreshold?S("refreshing"):S("refresherabort"),k={x:0,y:0},C={x:0,y:e.refresherThreshold}};o.value.addEventListener("touchstart",h,Dm),o.value.addEventListener("touchmove",l,yr(!1)),o.value.addEventListener("scroll",a,yr(!1)),o.value.addEventListener("touchend",v,Dm),jg(),ls((()=>{o.value.removeEventListener("touchstart",h),o.value.removeEventListener("touchmove",l),o.value.removeEventListener("scroll",a),o.value.removeEventListener("touchend",v)}))})),Jo((()=>{p.value&&(o.value.scrollTop=t.lastScrollTop),f.value&&(o.value.scrollLeft=t.lastScrollLeft)})),Vo(n,(e=>{y(e)})),Vo(r,(e=>{b(e)})),Vo((()=>e.scrollIntoView),(e=>{w(e)})),Vo((()=>e.refresherTriggered),(e=>{!0===e?S("refreshing"):!1===e&&S("restore")})),{realScrollX:f,realScrollY:p,_scrollTopChanged:y,_scrollLeftChanged:b}}(e,c,d,h,u,a,o,l,n),m=jl((()=>{var e="";return f.value?e+="overflow-x:auto;":e+="overflow-x:hidden;",p.value?e+="overflow-y:auto;":e+="overflow-y:hidden;",e})),_=jl((()=>{var t="uni-scroll-view";return!1===e.showScrollbar&&(t+=" uni-scroll-view-scrollbar-hidden"),t}));return i({$getMain:()=>o.value}),()=>{var{refresherEnabled:t,refresherBackground:n,refresherDefaultStyle:i,refresherThreshold:u}=e,{refresherHeight:d,refreshState:h}=c;return ml("uni-scroll-view",{ref:a},[ml("div",{ref:s,class:"uni-scroll-view"},[ml("div",{ref:o,style:m.value,class:_.value},[t?ml(Pm,{refreshState:h,refresherHeight:d,refresherThreshold:u,refresherDefaultStyle:i,refresherBackground:n},{default:()=>["none"==i?r.refresher&&r.refresher():null]},8,["refreshState","refresherHeight","refresherThreshold","refresherDefaultStyle","refresherBackground"]):null,ml("div",{ref:l,class:"uni-scroll-view-content"},[r.default&&r.default()],512)],6)],512)],512)}}});const Rm=Xp({name:"Slider",props:{name:{type:String,default:""},min:{type:[Number,String],default:0},max:{type:[Number,String],default:100},value:{type:[Number,String],default:0},step:{type:[Number,String],default:1},disabled:{type:[Boolean,String],default:!1},color:{type:String,default:"#e9e9e9"},backgroundColor:{type:String,default:"#e9e9e9"},activeColor:{type:String,default:"#007aff"},selectedColor:{type:String,default:"#007aff"},blockColor:{type:String,default:"#ffffff"},blockSize:{type:[Number,String],default:28},showValue:{type:[Boolean,String],default:!1}},emits:["changing","change"],setup(e,t){var{emit:n}=t,r=to(null),i=to(null),a=to(null),o=to(Number(e.value));Vo((()=>e.value),(e=>{o.value=Number(e)}));var s=Kp(r,n),l=function(e,t){var n=()=>Fm(t.value,e.min,e.max),r=()=>"#e9e9e9"!==e.backgroundColor?e.backgroundColor:"#007aff"!==e.color?e.color:"#007aff",i=()=>"#007aff"!==e.activeColor?e.activeColor:"#e9e9e9"!==e.selectedColor?e.selectedColor:"#e9e9e9",a={setBgColor:jl((()=>({backgroundColor:r()}))),setBlockBg:jl((()=>({left:n()}))),setActiveColor:jl((()=>({backgroundColor:i(),width:n()}))),setBlockStyle:jl((()=>({width:e.blockSize+"px",height:e.blockSize+"px",marginLeft:-e.blockSize/2+"px",marginTop:-e.blockSize/2+"px",left:n(),backgroundColor:e.blockColor})))};return a}(e,o),{_onClick:u,_onTrack:c}=function(e,t,n,r,i){var a=n=>{e.disabled||(s(n),i("change",n,{value:t.value}))},o=t=>{var n=Number(e.max),r=Number(e.min),i=Number(e.step);return t<r?r:t>n?n:qm.mul.call(Math.round((t-r)/i),i)+r},s=i=>{var a=Number(e.max),s=Number(e.min),l=r.value,u=getComputedStyle(l,null).marginLeft,c=l.offsetWidth;c+=parseInt(u);var d=n.value,h=d.offsetWidth-(e.showValue?c:0),f=d.getBoundingClientRect().left,p=(i.x-f)*(a-s)/h+s;t.value=o(p)},l=n=>{if(!e.disabled)return"move"===n.detail.state?(s({x:n.detail.x}),i("changing",n,{value:t.value}),!1):"end"===n.detail.state&&i("change",n,{value:t.value})},u=Ds(ev,!1);if(u){var c={reset:()=>t.value=Number(e.min),submit:()=>{var n=["",null];return""!==e.name&&(n[0]=e.name,n[1]=t.value),n}};u.addField(c),ls((()=>{u.removeField(c)}))}return{_onClick:a,_onTrack:l}}(e,o,r,i,s);return as((()=>{Zg(a.value,c)})),()=>{var{setBgColor:t,setBlockBg:n,setActiveColor:s,setBlockStyle:c}=l;return ml("uni-slider",{ref:r,onClick:Gp(u)},[ml("div",{class:"uni-slider-wrapper"},[ml("div",{class:"uni-slider-tap-area"},[ml("div",{style:t.value,class:"uni-slider-handle-wrapper"},[ml("div",{ref:a,style:n.value,class:"uni-slider-handle"},null,4),ml("div",{style:c.value,class:"uni-slider-thumb"},null,4),ml("div",{style:s.value,class:"uni-slider-track"},null,4)],4)]),Yo(ml("span",{ref:i,class:"uni-slider-value"},[o.value],512),[[Gl,e.showValue]])]),ml("slot",null,null)],8,["onClick"])}}});var Fm=(e,t,n)=>(n=Number(n),100*(e-(t=Number(t)))/(n-t)+"%");var qm={mul:function(e){var t=0,n=this.toString(),r=e.toString();try{t+=n.split(".")[1].length}catch(i){}try{t+=r.split(".")[1].length}catch(i){}return Number(n.replace(".",""))*Number(r.replace(".",""))/Math.pow(10,t)}};function jm(e,t,n,r,i,a){function o(){u&&(clearTimeout(u),u=null)}var s,l,u=null,c=!0,d=0,h=1,f=null,p=!1,v=0,g="",m=jl((()=>n.value.length>t.displayMultipleItems)),_=jl((()=>e.circular&&m.value));function y(i){Math.floor(2*d)===Math.floor(2*i)&&Math.ceil(2*d)===Math.ceil(2*i)||_.value&&function(r){if(!c)for(var i=n.value,a=i.length,o=r+t.displayMultipleItems,s=0;s<a;s++){var l=i[s],u=Math.floor(r/a)*a+s,d=u+a,h=u-a,f=Math.max(r-(u+1),u-o,0),p=Math.max(r-(d+1),d-o,0),v=Math.max(r-(h+1),h-o,0),g=Math.min(f,p,v),m=[u,d,h][[f,p,v].indexOf(g)];l.updatePosition(m,e.vertical)}}(i);var o="translate("+(e.vertical?"0":100*-i*h+"%")+", "+(e.vertical?100*-i*h+"%":"0")+") translateZ(0)",l=r.value;if(l&&(l.style.webkitTransform=o,l.style.transform=o),d=i,!s){if(i%1==0)return;s=i}i-=Math.floor(s);var u=n.value;i<=-(u.length-1)?i+=u.length:i>=u.length&&(i-=u.length),i=s%1>.5||s<0?i-1:i,a("transition",{},{dx:e.vertical?0:i*l.offsetWidth,dy:e.vertical?i*l.offsetHeight:0})}function b(e){var r=n.value.length;if(!r)return-1;var i=(Math.round(e)%r+r)%r;if(_.value){if(r<=t.displayMultipleItems)return 0}else if(i>r-t.displayMultipleItems)return r-t.displayMultipleItems;return i}function w(){f=null}function x(){if(f){var e=f,r=e.toPos,i=e.acc,o=e.endTime,u=e.source,c=o-Date.now();if(c<=0){y(r),f=null,p=!1,s=null;var d=n.value[t.current];if(d){var h=d.getItemId();a("animationfinish",{},{current:t.current,currentItemId:h,source:u})}}else{y(r+i*c*c/2),l=requestAnimationFrame(x)}}else p=!1}function S(e,r,i){w();var a=t.duration,o=n.value.length,s=d;if(_.value)if(i<0){for(;s<e;)s+=o;for(;s-o>e;)s-=o}else if(i>0){for(;s>e;)s-=o;for(;s+o<e;)s+=o;s+o-e<e-s&&(s+=o)}else{for(;s+o<e;)s+=o;for(;s-o>e;)s-=o;s+o-e<e-s&&(s+=o)}else"click"===r&&(e=e+t.displayMultipleItems-1<o?e:0);f={toPos:e,acc:2*(s-e)/(a*a),endTime:Date.now()+a,source:r},p||(p=!0,l=requestAnimationFrame(x))}function k(){o();var e=n.value,r=function(){u=null,g="autoplay",_.value?t.current=b(t.current+1):t.current=t.current+t.displayMultipleItems<e.length?t.current+1:0,S(t.current,"autoplay",_.value?1:0),u=setTimeout(r,t.interval)};c||e.length<=t.displayMultipleItems||(u=setTimeout(r,t.interval))}function C(e){e?k():o()}return Vo([()=>e.current,()=>e.currentItemId,()=>[...n.value]],(()=>{var r=-1;if(e.currentItemId)for(var i=0,a=n.value;i<a.length;i++){if(a[i].getItemId()===e.currentItemId){r=i;break}}r<0&&(r=Math.round(e.current)||0),r=r<0?0:r,t.current!==r&&(g="",t.current=r)})),Vo([()=>e.vertical,()=>_.value,()=>t.displayMultipleItems,()=>[...n.value]],(function(){o(),f&&(y(f.toPos),f=null);for(var i=n.value,a=0;a<i.length;a++)i[a].updatePosition(a,e.vertical);h=1;var s=r.value;if(1===t.displayMultipleItems&&i.length){var l=i[0].getBoundingClientRect(),u=s.getBoundingClientRect();(h=l.width/u.width)>0&&h<1||(h=1)}var p=d;d=-2;var g=t.current;g>=0?(c=!1,t.userTracking?(y(p+g-v),v=g):(y(g),e.autoplay&&k())):(c=!0,y(-t.displayMultipleItems-1))})),Vo((()=>t.interval),(()=>{u&&(o(),k())})),Vo((()=>t.current),((e,r)=>{!function(e,r){var i=g;g="";var o=n.value;if(!i){var s=o.length;S(e,"",_.value&&r+(s-e)%s>s/2?1:0)}var l=o[e];if(l){var u=t.currentItemId=l.getItemId();a("change",{},{current:t.current,currentItemId:u,source:i})}}(e,r),i("update:current",e)})),Vo((()=>t.currentItemId),(e=>{i("update:currentItemId",e)})),Vo((()=>e.autoplay&&!t.userTracking),C),C(e.autoplay&&!t.userTracking),as((()=>{var i=!1,a=0,s=0;function l(e){t.userTracking=!1;var n=a/Math.abs(a),r=0;!e&&Math.abs(a)>.2&&(r=.5*n);var i=b(d+r);e?y(v):(g="touch",t.current=i,S(i,"touch",0!==r?r:0===i&&_.value&&d>=1?1:0))}Zg(r.value,(u=>{if(!e.disableTouch&&!c){if("start"===u.detail.state)return t.userTracking=!0,i=!1,o(),v=d,a=0,s=Date.now(),void w();if("end"===u.detail.state)return l(!1);if("cancel"===u.detail.state)return l(!0);if(t.userTracking){if(!i){i=!0;var h=Math.abs(u.detail.dx),f=Math.abs(u.detail.dy);if((h>=f&&e.vertical||h<=f&&!e.vertical)&&(t.userTracking=!1),!t.userTracking)return void(e.autoplay&&k())}return function(i){var o=s;s=Date.now();var l=n.value.length-t.displayMultipleItems;function u(e){return.5-.25/(e+.5)}function c(e,t){var n=v+e;a=.6*a+.4*t,_.value||(n<0||n>l)&&(n<0?n=-u(-n):n>l&&(n=l+u(n-l)),a=0),y(n)}var d=s-o||1,h=r.value;e.vertical?c(-i.dy/h.offsetHeight,-i.ddy/d):c(-i.dx/h.offsetWidth,-i.ddx/d)}(u.detail),!1}}}))})),us((()=>{o(),cancelAnimationFrame(l)})),{onSwiperDotClick:function(e){S(t.current=e,g="click",_.value?1:0)},circularEnabled:_,swiperEnabled:m}}const Vm=Xp({name:"Swiper",props:{indicatorDots:{type:[Boolean,String],default:!1},vertical:{type:[Boolean,String],default:!1},autoplay:{type:[Boolean,String],default:!1},circular:{type:[Boolean,String],default:!1},interval:{type:[Number,String],default:5e3},duration:{type:[Number,String],default:500},current:{type:[Number,String],default:0},indicatorColor:{type:String,default:""},indicatorActiveColor:{type:String,default:""},previousMargin:{type:String,default:""},nextMargin:{type:String,default:""},currentItemId:{type:String,default:""},skipHiddenItemLayout:{type:[Boolean,String],default:!1},displayMultipleItems:{type:[Number,String],default:1},disableTouch:{type:[Boolean,String],default:!1},navigation:{type:[Boolean,String],default:!1},navigationColor:{type:String,default:"#fff"},navigationActiveColor:{type:String,default:"rgba(53, 53, 53, 0.6)"}},emits:["change","transition","animationfinish","update:current","update:currentItemId"],setup(e,t){var{slots:n,emit:r}=t,i=to(null),a=Kp(i,r),o=to(null),s=to(null),l=function(e){return qa({interval:jl((()=>{var t=Number(e.interval);return isNaN(t)?5e3:t})),duration:jl((()=>{var t=Number(e.duration);return isNaN(t)?500:t})),displayMultipleItems:jl((()=>{var t=Math.round(e.displayMultipleItems);return isNaN(t)?1:t})),current:Math.round(e.current)||0,currentItemId:e.currentItemId,userTracking:!1})}(e),u=jl((()=>{var t={};return(e.nextMargin||e.previousMargin)&&(t=e.vertical?{left:0,right:0,top:Lu(e.previousMargin,!0),bottom:Lu(e.nextMargin,!0)}:{top:0,bottom:0,left:Lu(e.previousMargin,!0),right:Lu(e.nextMargin,!0)}),t})),c=jl((()=>{var t=Math.abs(100/l.displayMultipleItems)+"%";return{width:e.vertical?"100%":t,height:e.vertical?t:"100%"}})),d=[],h=[],f=to([]);function p(){for(var e=[],t=function(t){var n=d[t];n instanceof Element||(n=n.el);var r=h.find((e=>n===e.rootRef.value));r&&e.push(Xa(r))},n=0;n<d.length;n++)t(n);f.value=e}$g((()=>{d=s.value.children,p()}));Ps("addSwiperContext",(function(e){h.push(e),p()}));Ps("removeSwiperContext",(function(e){var t=h.indexOf(e);t>=0&&(h.splice(t,1),p())}));var{onSwiperDotClick:v,circularEnabled:g,swiperEnabled:m}=jm(e,l,f,s,r,a);return()=>{var t=n.default&&n.default();return ml("uni-swiper",{ref:i},[ml("div",{ref:o,class:"uni-swiper-wrapper"},[ml("div",{class:"uni-swiper-slides",style:u.value},[ml("div",{ref:s,class:"uni-swiper-slide-frame",style:c.value},[t],4)],4),e.indicatorDots&&ml("div",{class:["uni-swiper-dots",e.vertical?"uni-swiper-dots-vertical":"uni-swiper-dots-horizontal"]},[f.value.map(((t,n,r)=>ml("div",{onClick:()=>v(n),class:{"uni-swiper-dot":!0,"uni-swiper-dot-active":n<l.current+l.displayMultipleItems&&n>=l.current||n<l.current+l.displayMultipleItems-r.length},style:{background:n===l.current?e.indicatorActiveColor:e.indicatorColor}},null,14,["onClick"])))],2),null],512)],512)}}});const $m=Xp({name:"SwiperItem",props:{itemId:{type:String,default:""}},setup(e,t){var{slots:n}=t,r=to(null),i={rootRef:r,getItemId:()=>e.itemId,getBoundingClientRect:()=>r.value.getBoundingClientRect(),updatePosition(e,t){var n=t?"0":100*e+"%",i=t?100*e+"%":"0",a=r.value,o="translate(".concat(n,",").concat(i,") translateZ(0)");a&&(a.style.webkitTransform=o,a.style.transform=o)}};return as((()=>{var e=Ds("addSwiperContext");e&&e(i)})),us((()=>{var e=Ds("removeSwiperContext");e&&e(i)})),()=>ml("uni-swiper-item",{ref:r,style:{position:"absolute",width:"100%",height:"100%"}},[n.default&&n.default()],512)}});const Hm=Xp({name:"Switch",props:{name:{type:String,default:""},checked:{type:[Boolean,String],default:!1},type:{type:String,default:"switch"},id:{type:String,default:""},disabled:{type:[Boolean,String],default:!1},color:{type:String,default:""}},emits:["change"],setup(e,t){var{emit:n}=t,r=to(null),i=to(e.checked),a=function(e,t){var n=Ds(ev,!1),r=Ds(rv,!1),i={submit:()=>{var n=["",null];return e.name&&(n[0]=e.name,n[1]=t.value),n},reset:()=>{t.value=!1}};n&&(n.addField(i),us((()=>{n.removeField(i)})));return r}(e,i),o=Kp(r,n);Vo((()=>e.checked),(e=>{i.value=e}));var s=t=>{e.disabled||(i.value=!i.value,o("change",t,{value:i.value}))};return a&&(a.addHandler(s),ls((()=>{a.removeHandler(s)}))),av(e,{"label-click":s}),()=>{var t,{color:n,type:a}=e,o=Qp(e,"disabled"),l={};return n&&i.value&&(l.backgroundColor=n,l.borderColor=n),t=i.value,ml("uni-switch",Sl({id:e.id,ref:r},o,{onClick:s}),[ml("div",{class:"uni-switch-wrapper"},[Yo(ml("div",{class:["uni-switch-input",[i.value?"uni-switch-input-checked":""]],style:l},null,6),[[Gl,"switch"===a]]),Yo(ml("div",{class:"uni-checkbox-input"},[t?Pu(Nu,e.color,22):""],512),[[Gl,"checkbox"===a]])])],16,["id","onClick"])}}});var Wm={ensp:" ",emsp:" ",nbsp:" "};function Um(e,t){return function(e,t){var{space:n,decode:r}=t,i="",a=!1;for(var o of e)n&&Wm[n]&&" "===o&&(o=Wm[n]),a?(i+="n"===o?Jn:"\\"===o?"\\":"\\"+o,a=!1):"\\"===o?a=!0:i+=o;return r?i.replace(/&nbsp;/g,Wm.nbsp).replace(/&ensp;/g,Wm.ensp).replace(/&emsp;/g,Wm.emsp).replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&amp;/g,"&").replace(/&quot;/g,'"').replace(/&apos;/g,"'"):i}(e,t).split(Jn)}function Ym(e,t,n,r,i,a,o){try{var s=e[a](o),l=s.value}catch(u){return void n(u)}s.done?t(l):Promise.resolve(l).then(r,i)}function Xm(e){return function(){var t=this,n=arguments;return new Promise((function(r,i){var a=e.apply(t,n);function o(e){Ym(a,r,i,o,s,"next",e)}function s(e){Ym(a,r,i,o,s,"throw",e)}o(void 0)}))}}function Zm(e,t,n){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Gm(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Km(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Gm(Object(n),!0).forEach((function(t){Zm(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Gm(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var Jm=on({},Ag,{placeholderClass:{type:String,default:"input-placeholder"},autoHeight:{type:[Boolean,String],default:!1},confirmType:{type:String,default:"return",validator:e=>e_.concat("return").includes(e)}}),Qm=!1,e_=["done","go","next","search","send"];const t_=Xp({name:"Textarea",props:Jm,emits:["confirm","linechange",...Mg],setup(e,t){var n,{emit:r,expose:i}=t,a=to(null),o=to(null),{fieldRef:s,state:l,scopedAttrsState:u,fixDisabledColor:c,trigger:d}=Lg(e,a,r),h=jl((()=>l.value.split(Jn))),f=jl((()=>e_.includes(e.confirmType))),p=to(0),v=to(null);function g(e){var{height:t}=e;p.value=t}function m(e){"Enter"===e.key&&f.value&&e.preventDefault()}function _(t){if("Enter"===t.key&&f.value){!function(e){d("confirm",e,{value:l.value})}(t);var n=t.target;!e.confirmHold&&n.blur()}}return Vo((()=>p.value),(t=>{var n=a.value,r=v.value,i=o.value,s=parseFloat(getComputedStyle(n).lineHeight);isNaN(s)&&(s=r.offsetHeight);var l=Math.round(t/s);d("linechange",{},{height:t,heightRpx:750/window.innerWidth*t,lineCount:l}),e.autoHeight&&(n.style.height="auto",i.style.height=t+"px")})),n="(prefers-color-scheme: dark)",Qm=0===String(navigator.platform).indexOf("iP")&&0===String(navigator.vendor).indexOf("Apple")&&window.matchMedia(n).media!==n,i({$triggerInput:e=>{r("update:modelValue",e.value),r("update:value",e.value),l.value=e.value}}),()=>{var t=e.disabled&&c?ml("textarea",{key:"disabled-textarea",ref:s,value:l.value,tabindex:"-1",readonly:!!e.disabled,maxlength:l.maxlength,class:{"uni-textarea-textarea":!0,"uni-textarea-textarea-fix-margin":Qm},style:Km({overflowY:e.autoHeight?"hidden":"auto"},e.cursorColor&&{caretColor:e.cursorColor}),onFocus:e=>e.target.blur()},null,46,["value","readonly","maxlength","onFocus"]):ml("textarea",{key:"textarea",ref:s,value:l.value,disabled:!!e.disabled,maxlength:l.maxlength,enterkeyhint:e.confirmType,inputmode:e.inputmode,class:{"uni-textarea-textarea":!0,"uni-textarea-textarea-fix-margin":Qm},style:Km({overflowY:e.autoHeight?"hidden":"auto"},e.cursorColor&&{caretColor:e.cursorColor}),onKeydown:m,onKeyup:_},null,46,["value","disabled","maxlength","enterkeyhint","inputmode","onKeydown","onKeyup"]);return ml("uni-textarea",{ref:a},[ml("div",{ref:o,class:"uni-textarea-wrapper"},[Yo(ml("div",Sl(u.attrs,{style:e.placeholderStyle,class:["uni-textarea-placeholder",e.placeholderClass]}),[e.placeholder],16),[[Gl,!l.value.length]]),ml("div",{ref:v,class:"uni-textarea-line"},[" "],512),ml("div",{class:"uni-textarea-compute"},[h.value.map((e=>ml("div",null,[e.trim()?e:"."]))),ml(uv,{initial:!0,onResize:g},null,8,["initial","onResize"])]),"search"===e.confirmType?ml("form",{action:"",onSubmit:()=>!1,class:"uni-input-form"},[t],40,["onSubmit"]):t],512)],512)}}});function n_(e,t){if(t||(t=e.id),t)return e.$options.name.toLowerCase()+"."+t}function r_(e,t,n){e&&bi(Bu(),e,((e,n)=>{var{type:r,data:i}=e;t(r,i,n)}))}function i_(e,t){e&&function(e,t){t=yi(e,t),delete _i[t]}(Bu(),e)}function a_(e,t,n,r){var i=Ol().proxy;as((()=>{r_(t||n_(i),e),Vo((()=>i.id),((t,n)=>{r_(n_(i,t),e),i_(n&&n_(i,n))}))})),ls((()=>{i_(t||n_(i))}))}var o_=0;function s_(e){var t=Du(),n=Ol().proxy,r=n.$options.name.toLowerCase(),i=e||n.id||"context".concat(o_++);return as((()=>{n.$el.__uniContextInfo={id:i,type:r,page:t}})),"".concat(r,".").concat(i)}class l_ extends Hp{constructor(e,t,n,r,i){super(e,t,n,r,i,[...Yp.props,...arguments.length>5&&void 0!==arguments[5]?arguments[5]:[]])}call(e){var t={animation:this.$props.animation,$el:this.$};e.call(t)}setAttribute(e,t){return"animation"===e&&(this.$animate=!0),super.setAttribute(e,t)}update(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(this.$animate)return e?this.call(Yp.mounted):void(this.$animate&&(this.$animate=!1,this.call(Yp.watch.animation.handler)))}}var u_=["space","decode"];var c_=["hover-class","hover-stop-propagation","hover-start-time","hover-stay-time"];class d_ extends l_{constructor(e,t,n,r,i){super(e,t,n,r,i,[...c_,...arguments.length>5&&void 0!==arguments[5]?arguments[5]:[]])}update(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=this.$props["hover-class"];t&&"none"!==t?(this._hover||(this._hover=new h_(this.$,this.$props)),this._hover.addEvent()):this._hover&&this._hover.removeEvent(),super.update(e)}}class h_{constructor(e,t){this._listening=!1,this._hovering=!1,this._hoverTouch=!1,this.$=e,this.props=t,this.__hoverTouchStart=this._hoverTouchStart.bind(this),this.__hoverTouchEnd=this._hoverTouchEnd.bind(this),this.__hoverTouchCancel=this._hoverTouchCancel.bind(this)}get hovering(){return this._hovering}set hovering(e){this._hovering=e;var t=this.props["hover-class"].split(" ").filter(Boolean),n=this.$.classList;e?this.$.classList.add.apply(n,t):this.$.classList.remove.apply(n,t)}addEvent(){this._listening||(this._listening=!0,this.$.addEventListener("touchstart",this.__hoverTouchStart),this.$.addEventListener("touchend",this.__hoverTouchEnd),this.$.addEventListener("touchcancel",this.__hoverTouchCancel))}removeEvent(){this._listening&&(this._listening=!1,this.$.removeEventListener("touchstart",this.__hoverTouchStart),this.$.removeEventListener("touchend",this.__hoverTouchEnd),this.$.removeEventListener("touchcancel",this.__hoverTouchCancel))}_hoverTouchStart(e){if(!e._hoverPropagationStopped){var t=this.props["hover-class"];t&&"none"!==t&&!this.$.disabled&&(e.touches.length>1||(this.props["hover-stop-propagation"]&&(e._hoverPropagationStopped=!0),this._hoverTouch=!0,this._hoverStartTimer=setTimeout((()=>{this.hovering=!0,this._hoverTouch||this._hoverReset()}),this.props["hover-start-time"])))}}_hoverTouchEnd(){this._hoverTouch=!1,this.hovering&&this._hoverReset()}_hoverReset(){requestAnimationFrame((()=>{clearTimeout(this._hoverStayTimer),this._hoverStayTimer=setTimeout((()=>{this.hovering=!1}),this.props["hover-stay-time"])}))}_hoverTouchCancel(){this._hoverTouch=!1,this.hovering=!1,clearTimeout(this._hoverStartTimer)}}function f_(){return plus.navigator.isImmersedStatusbar()?Math.round("iOS"===plus.os.name?plus.navigator.getSafeAreaInsets().top:plus.navigator.getStatusbarHeight()):0}function p_(){var e=plus.webview.currentWebview().getStyle(),t=e&&e.titleNView;return t&&"default"===t.type?Qn+f_():0}var v_=Symbol("onDraw");function g_(e,t){return jl((()=>{var n={};return Object.keys(e).forEach((r=>{if(!t||!t.includes(r)){var i=e[r];i="src"===r?yc(i):i,n[r.replace(/[A-Z]/g,(e=>"-"+e.toLowerCase()))]=i}})),n}))}function m_(e){var t=qa({top:"0px",left:"0px",width:"0px",height:"0px",position:"static"}),n=to(!1);function r(){var r=e.value,i=r.getBoundingClientRect(),a=["width","height"];n.value=0===i.width||0===i.height,n.value||(t.position=function(e){for(var t;e;){var n=getComputedStyle(e),r=n.transform||n.webkitTransform;t=(!r||"none"===r)&&t,t="fixed"===n.position||t,e=e.parentElement}return t}(r)?"absolute":"static",a.push("top","left")),a.forEach((e=>{var n=i[e];n="top"===e?n+("static"===t.position?document.documentElement.scrollTop||document.body.scrollTop||0:p_()):n,t[e]=n+"px"}))}var i=null;function a(){i&&cancelAnimationFrame(i),i=requestAnimationFrame((()=>{i=null,r()}))}window.addEventListener("updateview",a);var o=[],s=[];return Ps(v_,(function(e){o?o.push(e):e(t)})),as((()=>{r(),s.forEach((e=>e())),s=null})),ls((()=>{window.removeEventListener("updateview",a)})),{position:t,hidden:n,onParentReady:function(e){var n=Ds(v_),r=n=>{e(n),o.forEach((e=>e(t))),o=null};!function(e){s?s.push(e):e()}((()=>{n?n(r):r({top:"0px",left:"0px",width:Number.MAX_SAFE_INTEGER+"px",height:Number.MAX_SAFE_INTEGER+"px",position:"static"})}))}}}const __=Xp({name:"Ad",props:{adpid:{type:[Number,String],default:""},data:{type:Object,default:null},dataCount:{type:Number,default:5},channel:{type:String,default:""}},setup(e,t){var n,{emit:r}=t,i=to(null),a=to(null),o=Kp(i,r),s=g_(e,["id"]),{position:l,onParentReady:u}=m_(a);return u((()=>{function t(){var t={adpid:e.adpid,width:l.width,count:e.dataCount};void 0!==e.channel&&(t.ext={channel:e.channel}),UniViewJSBridge.invokeServiceMethod("getAdData",t,(e=>{var{code:t,data:r,message:i}=e;0===t?n.renderingBind(r):o("error",{},{errMsg:i})}))}n=plus.ad.createAdView(Object.assign({},s.value,l)),plus.webview.currentWebview().append(n),n.setDislikeListener((e=>{a.value.style.height="0",window.dispatchEvent(new CustomEvent("updateview")),o("close",{},e)})),n.setRenderingListener((e=>{0===e.result?(a.value.style.height=e.height+"px",window.dispatchEvent(new CustomEvent("updateview"))):o("error",{},{errCode:e.result})})),n.setAdClickedListener((()=>{o("adclicked",{},{})})),Vo((()=>l),(e=>n.setStyle(e)),{deep:!0}),Vo((()=>e.adpid),(e=>{e&&t()})),Vo((()=>e.data),(e=>{e&&n.renderingBind(e)})),e.adpid&&t()})),ls((()=>{n&&n.close()})),()=>ml("uni-ad",{ref:i},[ml("div",{ref:a,class:"uni-ad-container"},null,512)],512)}});class y_ extends _p{constructor(e,t,n,r,i,a,o){super(e,t,r);var s=document.createElement("div");s.__vueParent=function(e){for(;e&&e.pid>0;)if(e=rp(e.pid)){var{__vueParentComponent:t}=e.$;if(t)return t}return null}(this),this.$props=qa({}),this.init(a),this.$app=vu(function(e,t){return()=>Vl(e,t)}(n,this.$props)),this.$app.mount(s),this.$=s.firstElementChild,this.$.__id=e,o&&(this.$holder=this.$.querySelector(o)),un(a,"t")&&this.setText(a.t||""),a.a&&un(a.a,Ar)&&$p(this.$,a.a[Ar]),this.insert(r,i),ko()}init(e){var{a:t,e:n,w:r}=e;t&&(this.setWxsProps(t),Object.keys(t).forEach((e=>{this.setAttr(e,t[e])}))),un(e,"s")&&this.setAttr("style",e.s),n&&Object.keys(n).forEach((e=>{this.addEvent(e,n[e])})),r&&this.addWxsEvents(e.w)}setText(e){(this.$holder||this.$).textContent=e,this.updateView()}addWxsEvent(e,t,n){this.$props[e]=Vp(this,t,n)}addEvent(e,t){this.$props[e]=qp(this.id,t,Sr(e)[1])}removeEvent(e){this.$props[e]=null}setAttr(e,t){if(e===Ar)this.$&&$p(this.$,t);else if(e===Mr)this.$.__ownerId=t;else if(e===Er)op((()=>vp(this,t)),3);else if(e===Tr){var n=gp(t,this.$||rp(this.pid).$),r=this.$props.style;wn(n)&&wn(r)?Object.keys(n).forEach((e=>{r[e]=n[e]})):this.$props.style=n}else mp(e)?this.$.style.setProperty(e,bp(t)):(t=gp(t,this.$||rp(this.pid).$),this.wxsPropsInvoke(e,t,!0)||(this.$props[e]=t));this.updateView()}removeAttr(e){mp(e)?this.$.style.removeProperty(e):this.$props[e]=null,this.updateView()}remove(){this.removeUniParent(),this.isUnmounted=!0,this.$app.unmount(),ip(this.id),this.removeUniChildren(),ko(),this.updateView()}appendChild(e){var t=(this.$holder||this.$).appendChild(e);return this.updateView(!0),t}insertBefore(e,t){var n=(this.$holder||this.$).insertBefore(e,t);return this.updateView(!0),n}}class b_ extends y_{constructor(e,t,n,r,i,a,o){super(e,t,n,r,i,a,o)}getRebuildFn(){return this._rebuild||(this._rebuild=this.rebuild.bind(this)),this._rebuild}setText(e){return op(this.getRebuildFn(),2),super.setText(e)}appendChild(e){return op(this.getRebuildFn(),2),super.appendChild(e)}insertBefore(e,t){return op(this.getRebuildFn(),2),super.insertBefore(e,t)}removeUniChild(e){return op(this.getRebuildFn(),2),super.removeUniChild(e)}rebuild(){var e=this.$.__vueParentComponent;e.rebuild&&e.rebuild()}}function w_(e,t,n){e.childNodes.forEach((n=>{n instanceof Element?-1===n.className.indexOf(t)&&e.removeChild(n):e.removeChild(n)})),e.appendChild(document.createTextNode(n))}var x_=["value","modelValue"];function S_(e){x_.forEach((t=>{if(un(e,t)){var n="onUpdate:"+t;un(e,n)||(e[n]=n=>e[t]=n)}}))}class k_ extends _p{constructor(e,t,n,r){super(e,t,n),this.insert(n,r)}}var C_=0;function T_(e,t,n){var r,i,{position:a,hidden:o,onParentReady:s}=m_(e);s((s=>{var l=jl((()=>{var e={};for(var t in a){var n=a[t],r=parseFloat(n),i=parseFloat(s[t]);if("top"===t||"left"===t)n=Math.max(r,i)+"px";else if("width"===t||"height"===t){var o="width"===t?"left":"top",l=parseFloat(s[o]),u=parseFloat(a[o]),c=Math.max(l-u,0),d=Math.max(u+r-(l+i),0);n=Math.max(r-c-d,0)+"px"}e[t]=n}return e})),u=["borderRadius","borderColor","borderWidth","backgroundColor"],c=["paddingTop","paddingRight","paddingBottom","paddingLeft","color","textAlign","lineHeight","fontSize","fontWeight","textOverflow","whiteSpace"],d=[],h={start:"left",end:"right"};function f(t){var n=getComputedStyle(e.value);return u.concat(c,d).forEach((e=>{t[e]=n[e]})),t}var p=qa(f({})),v=null;i=function(){v&&cancelAnimationFrame(v),v=requestAnimationFrame((()=>{v=null,f(p)}))},window.addEventListener("updateview",i);var g=jl((()=>{var e=function(){var e={};for(var t in e){var n=e[t];"top"!==t&&"left"!==t||(n=Math.min(parseFloat(n)-parseFloat(s[t]),0)+"px"),e[t]=n}return e}(),t=[{tag:"rect",position:e,rectStyles:{color:p.backgroundColor,radius:p.borderRadius,borderColor:p.borderColor,borderWidth:p.borderWidth}}];if("src"in n)n.src&&t.push({tag:"img",position:e,src:n.src});else{var r=parseFloat(p.lineHeight)-parseFloat(p.fontSize),i=parseFloat(e.width)-parseFloat(p.paddingLeft)-parseFloat(p.paddingRight);i=i<0?0:i;var a=parseFloat(e.height)-parseFloat(p.paddingTop)-r/2-parseFloat(p.paddingBottom);a=a<0?0:a,t.push({tag:"font",position:{top:"".concat(parseFloat(e.top)+parseFloat(p.paddingTop)+r/2,"px"),left:"".concat(parseFloat(e.left)+parseFloat(p.paddingLeft),"px"),width:"".concat(i,"px"),height:"".concat(a,"px")},textStyles:{align:h[p.textAlign]||p.textAlign,color:p.color,decoration:"none",lineSpacing:"".concat(r,"px"),margin:"0px",overflow:p.textOverflow,size:p.fontSize,verticalAlign:"top",weight:p.fontWeight,whiteSpace:p.whiteSpace},text:n.text})}return t}));r=new plus.nativeObj.View("cover-".concat(Date.now(),"-").concat(C_++),l.value,g.value),plus.webview.currentWebview().append(r),o.value&&r.hide(),r.addEventListener("click",(()=>{t("click",{},{})})),Vo((()=>o.value),(e=>{r[e?"hide":"show"]()})),Vo((()=>l.value),(e=>{r.setStyle(e)}),{deep:!0}),Vo((()=>g.value),(()=>{r.reset(),r.draw(g.value)}),{deep:!0})})),ls((()=>{r&&r.close(),i&&window.removeEventListener("updateview",i)}))}const A_=Xp({name:"CoverImage",props:{src:{type:String,default:""},autoSize:{type:[Boolean,String],default:!1}},emits:["click","load","error"],setup(e,t){var{emit:n}=t,r=to(null),i=Kp(r,n),a=qa({src:""}),o=function(e,t,n){var r,i=to("");function a(){t.src="",i.value=e.autoSize?"width:0;height:0;":"";var a=e.src?yc(e.src):"";0===a.indexOf("http://")||0===a.indexOf("https://")?(r=plus.downloader.createDownload(a,{filename:"_doc/uniapp_temp//download/"},((e,t)=>{200===t?o(e.filename):n("error",{},{errMsg:"error"})}))).start():a&&o(a)}function o(r){t.src=r,plus.io.getImageInfo({src:r,success:t=>{var{width:r,height:a}=t;e.autoSize&&(i.value="width:".concat(r,"px;height:").concat(a,"px;"),window.dispatchEvent(new CustomEvent("updateview"))),n("load",{},{width:r,height:a})},fail:()=>{n("error",{},{errMsg:"error"})}})}return e.src&&a(),Vo((()=>e.src),a),ls((()=>{r&&r.abort()})),i}(e,a,i);return T_(r,i,a),()=>ml("uni-cover-image",{ref:r,style:o.value},[ml("div",{class:"uni-cover-image"},null)],4)}});const M_=Xp({name:"CoverView",emits:["click"],setup(e,t){var{emit:n}=t,r=to(null),i=to(null),a=Kp(r,n),o=qa({text:""});return T_(r,a,o),$g((()=>{var e=i.value.childNodes[0];o.text=e&&e instanceof Text?e.textContent:"",window.dispatchEvent(new CustomEvent("updateview"))})),()=>ml("uni-cover-view",{ref:r},[ml("div",{ref:i,class:"uni-cover-view"},null,512)],512)}});var E_={id:{type:String,default:""},url:{type:String,default:""},mode:{type:String,default:"SD"},muted:{type:[Boolean,String],default:!1},enableCamera:{type:[Boolean,String],default:!0},autoFocus:{type:[Boolean,String],default:!0},beauty:{type:[Number,String],default:0},whiteness:{type:[Number,String],default:0},aspect:{type:[String],default:"3:2"},minBitrate:{type:[Number],default:200}},O_=["statechange","netstatus","error"];const L_=Xp({name:"LivePusher",props:E_,emits:O_,setup(e,t){var n,{emit:r}=t,i=to(null),a=Kp(i,r),o=to(null),s=g_(e,["id"]),{position:l,hidden:u,onParentReady:c}=m_(o);return c((()=>{n=new plus.video.LivePusher("livePusher"+Date.now(),Object.assign({},s.value,l)),plus.webview.currentWebview().append(n),O_.forEach((e=>{n.addEventListener(e,(t=>{a(e,{},t.detail)}))})),Vo((()=>s.value),(e=>n.setStyles(e)),{deep:!0}),Vo((()=>l),(e=>n.setStyles(e)),{deep:!0}),Vo((()=>u.value),(e=>{e||n.setStyles(l)}))})),a_(((e,t)=>{n&&n[e](t)}),s_()),ls((()=>{n&&n.close()})),()=>ml("uni-live-pusher",{ref:i,id:e.id},[ml("div",{ref:o,class:"uni-live-pusher-container"},null,512)],8,["id"])}});function z_(e){if(0!==e.indexOf("#"))return{color:e,opacity:1};var t=e.slice(7,9);return{color:e.slice(0,7),opacity:t?Number("0x"+t)/255:1}}const N_=Xp({name:"MapDefault",props:{id:{type:String,default:""},latitude:{type:[Number,String],default:""},longitude:{type:[Number,String],default:""},scale:{type:[String,Number],default:16},markers:{type:Array,default:()=>[]},polyline:{type:Array,default:()=>[]},circles:{type:Array,default:()=>[]},polygons:{type:Array,default:()=>[]},controls:{type:Array,default:()=>[]}},emits:["click","regionchange","controltap","markertap","callouttap"],setup(e,t){var n,{emit:r}=t,i=to(null),a=Kp(i,r),o=to(null),s=g_(e,["id"]),{position:l,hidden:u,onParentReady:c}=m_(o),{_addMarkers:d,_addMapLines:h,_addMapCircles:f,_addMapPolygons:p,_setMap:v}=function(e,t){var n;function r(t){var{longitude:r,latitude:i}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};n&&(n.setCenter(new plus.maps.Point(Number(r||e.longitude),Number(i||e.latitude))),t({errMsg:"moveToLocation:ok"}))}function i(e){n&&n.getCurrentCenter(((t,n)=>{e({longitude:n.getLng(),latitude:n.getLat(),errMsg:"getCenterLocation:ok"})}))}function a(e){if(n){var t=n.getBounds();e({southwest:t.getSouthWest(),northeast:t.getNorthEast(),errMsg:"getRegion:ok"})}}function o(e){n&&e({scale:n.getZoom(),errMsg:"getScale:ok"})}function s(e){if(n){var{id:r,latitude:i,longitude:a,iconPath:o,callout:s,label:l}=e;(e=>{var i,{latitude:a,longitude:u}=e.coord,c=new plus.maps.Marker(new plus.maps.Point(u,a));o&&c.setIcon(yc(o)),l&&l.content&&c.setLabel(l.content);var d=void 0;s&&s.content&&(d=new plus.maps.Bubble(s.content)),d&&c.setBubble(d),(r||0===r)&&(c.onclick=e=>{t("markertap",{},{markerId:r,latitude:a,longitude:u})},d&&(d.onclick=()=>{t("callouttap",{},{markerId:r})})),null===(i=n)||void 0===i||i.addOverlay(c),n.__markers__.push(c)})({coord:{latitude:i,longitude:a}})}}function l(){n&&(n.__markers__.forEach((e=>{var t;null===(t=n)||void 0===t||t.removeOverlay(e)})),n.__markers__=[])}function u(e,t){t&&l(),e.forEach((e=>{s(e)}))}function c(e){n&&(n.__lines__.length>0&&(n.__lines__.forEach((e=>{var t;null===(t=n)||void 0===t||t.removeOverlay(e)})),n.__lines__=[]),e.forEach((e=>{var t,{color:r,width:i}=e,a=e.points.map((e=>new plus.maps.Point(e.longitude,e.latitude))),o=new plus.maps.Polyline(a);if(r){var s=z_(r);o.setStrokeColor(s.color),o.setStrokeOpacity(s.opacity)}i&&o.setLineWidth(i),null===(t=n)||void 0===t||t.addOverlay(o),n.__lines__.push(o)})))}function d(e){n&&(n.__circles__.length>0&&(n.__circles__.forEach((e=>{var t;null===(t=n)||void 0===t||t.removeOverlay(e)})),n.__circles__=[]),e.forEach((e=>{var t,{latitude:r,longitude:i,color:a,fillColor:o,radius:s,strokeWidth:l}=e,u=new plus.maps.Circle(new plus.maps.Point(i,r),s);if(a){var c=z_(a);u.setStrokeColor(c.color),u.setStrokeOpacity(c.opacity)}if(o){var d=z_(o);u.setFillColor(d.color),u.setFillOpacity(d.opacity)}l&&u.setLineWidth(l),null===(t=n)||void 0===t||t.addOverlay(u),n.__circles__.push(u)})))}function h(e){if(n){var t=n.__polygons__;t.forEach((e=>{var t;null===(t=n)||void 0===t||t.removeOverlay(e)})),t.length=0,e.forEach((e=>{var r,{points:i,strokeWidth:a,strokeColor:o,fillColor:s}=e,l=[];i&&i.forEach((e=>{l.push(new plus.maps.Point(e.longitude,e.latitude))}));var u=new plus.maps.Polygon(l);if(o){var c=z_(o);u.setStrokeColor(c.color),u.setStrokeOpacity(c.opacity)}if(s){var d=z_(s);u.setFillColor(d.color),u.setFillOpacity(d.opacity)}a&&u.setLineWidth(a),null===(r=n)||void 0===r||r.addOverlay(u),t.push(u)}))}}var f={moveToLocation:r,getCenterLocation:i,getRegion:a,getScale:o};return a_(((e,t,n)=>{f[e]&&f[e](n,t)}),s_()),{_addMarkers:u,_addMapLines:c,_addMapCircles:d,_addMapPolygons:h,_setMap(e){n=e}}}(e,a);c((()=>{(n=on(plus.maps.create(Bu()+"-map-"+(e.id||Date.now()),Object.assign({},s.value,l,(()=>{if(e.latitude&&e.longitude)return{center:new plus.maps.Point(Number(e.longitude),Number(e.latitude))}})())),{__markers__:[],__lines__:[],__circles__:[],__polygons__:[]})).setZoom(parseInt(String(e.scale))),plus.webview.currentWebview().append(n),u.value&&n.hide(),n.onclick=e=>{a("tap",{},e),a("click",{},e)},n.onstatuschanged=e=>{a("regionchange",{},{})},v(n),d(e.markers),h(e.polyline),f(e.circles),p(e.polygons),Vo((()=>s.value),(e=>n&&n.setStyles(e)),{deep:!0}),Vo((()=>l),(e=>n&&n.setStyles(e)),{deep:!0}),Vo(u,(e=>{n&&n[e?"hide":"show"]()})),Vo((()=>e.scale),(e=>{n&&n.setZoom(parseInt(String(e)))})),Vo([()=>e.latitude,()=>e.longitude],(e=>{var[t,r]=e;n&&n.setStyles({center:new plus.maps.Point(Number(r),Number(t))})})),Vo((()=>e.markers),(e=>{d(e,!0)}),{deep:!0}),Vo((()=>e.polyline),(e=>{h(e)}),{deep:!0}),Vo((()=>e.circles),(e=>{f(e)}),{deep:!0}),Vo((()=>e.polygons),(e=>{p(e)}),{deep:!0})}));var g=jl((()=>e.controls.map((e=>{var t={position:"absolute"};return["top","left","width","height"].forEach((n=>{e.position[n]&&(t[n]=e.position[n]+"px")})),{id:e.id,iconPath:yc(e.iconPath),position:t,clickable:e.clickable}}))));return ls((()=>{n&&(n.close(),v(null))})),()=>ml("uni-map",{ref:i,id:e.id},[ml("div",{ref:o,class:"uni-map-container"},null,512),g.value.map(((e,t)=>ml(A_,{key:t,src:e.iconPath,style:e.position,"auto-size":!0,onClick:()=>e.clickable&&a("controltap",{},{controlId:e.id})},null,8,["src","style","auto-size","onClick"]))),ml("div",{class:"uni-map-slot"},null)],8,["id"])}});function I_(e){function t(){var e=this.div;this.getPanes().floatPane.appendChild(e)}function n(){var e=this.div.parentNode;e&&e.removeChild(this.div)}function r(){var t=this.option;this.Text=new e.Text({text:t.content,anchor:"bottom-center",offset:new e.Pixel(0,t.offsetY-16),style:{padding:(t.padding||8)+"px","line-height":(t.fontSize||14)+"px","border-radius":(t.borderRadius||0)+"px","border-color":"".concat(t.bgColor||"#fff"," transparent transparent"),"background-color":t.bgColor||"#fff","box-shadow":"0 2px 6px 0 rgba(114, 124, 245, .5)","text-align":"center","font-size":(t.fontSize||14)+"px",color:t.color||"#000"},position:t.position}),(e.event||e.Event).addListener(this.Text,"click",(()=>{this.callback()})),this.Text.setMap(t.map)}function i(){}function a(){this.Text&&this.option.map.remove(this.Text)}function o(){this.Text&&this.option.map.remove(this.Text)}class s{constructor(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},s=arguments.length>1?arguments[1]:void 0;this.createAMapText=r,this.removeAMapText=a,this.createBMapText=i,this.removeBMapText=o,this.onAdd=t,this.construct=t,this.onRemove=n,this.destroy=n,this.option=e||{};var l=this.visible=this.alwaysVisible="ALWAYS"===e.display;if(Z_())this.callback=s,this.visible&&this.createAMapText();else{var u=e.map;this.position=e.position,this.index=1;var c=this.div=document.createElement("div"),d=c.style;d.position="absolute",d.whiteSpace="nowrap",d.transform="translateX(-50%) translateY(-100%)",d.zIndex="1",d.boxShadow=e.boxShadow||"none",d.display=l?"block":"none";var h=this.triangle=document.createElement("div");h.setAttribute("style","position: absolute;white-space: nowrap;border-width: 4px;border-style: solid;border-color: #fff transparent transparent;border-image: initial;font-size: 12px;padding: 0px;background-color: transparent;width: 0px;height: 0px;transform: translate(-50%, 100%);left: 50%;bottom: 0;"),this.setStyle(e),c.appendChild(h),u&&this.setMap(u)}}set onclick(e){this.div.onclick=e}get onclick(){return this.div.onclick}setOption(e){this.option=e,"ALWAYS"===e.display?this.alwaysVisible=this.visible=!0:this.alwaysVisible=!1,Z_()?this.visible&&this.createAMapText():(this.setPosition(e.position),this.setStyle(e))}setStyle(e){var t=this.div,n=t.style;t.innerText=e.content||"",n.lineHeight=(e.fontSize||14)+"px",n.fontSize=(e.fontSize||14)+"px",n.padding=(e.padding||8)+"px",n.color=e.color||"#000",n.borderRadius=(e.borderRadius||0)+"px",n.backgroundColor=e.bgColor||"#fff",n.marginTop="-"+((e.top||0)+5)+"px",this.triangle.style.borderColor="".concat(e.bgColor||"#fff"," transparent transparent")}setPosition(e){this.position=e,this.draw()}draw(){var e=this.getProjection();if(this.position&&this.div&&e){var t=e.fromLatLngToDivPixel(this.position),n=this.div.style;n.left=t.x+"px",n.top=t.y+"px"}}changed(){this.div.style.display=this.visible?"block":"none"}}if(!Z_()&&!G_()){var l=new(e.OverlayView||e.Overlay);s.prototype.setMap=l.setMap,s.prototype.getMap=l.getMap,s.prototype.getPanes=l.getPanes,s.prototype.getProjection=l.getProjection,s.prototype.map_changed=l.map_changed,s.prototype.set=l.set,s.prototype.get=l.get,s.prototype.setOptions=l.setValues,s.prototype.bindTo=l.bindTo,s.prototype.bindsTo=l.bindsTo,s.prototype.notify=l.notify,s.prototype.setValues=l.setValues,s.prototype.unbind=l.unbind,s.prototype.unbindAll=l.unbindAll,s.prototype.addListener=l.addListener}return s}var P_,D_=(e,t,n)=>new Promise(((r,i)=>{var a=e=>{try{s(n.next(e))}catch(t){i(t)}},o=e=>{try{s(n.throw(e))}catch(t){i(t)}},s=e=>e.done?r(e.value):Promise.resolve(e.value).then(a,o);s((n=n.apply(e,t)).next())})),B_={},R_="__map_callback__";function F_(e,t){return D_(this,null,(function*(){var n=yield U_();if(n.key){var r=B_[n.type]=B_[n.type]||[];if(P_)t(P_);else if(window[n.type]&&window[n.type].maps)(P_=Z_()||G_()?window[n.type]:window[n.type].maps).Callout=P_.Callout||I_(P_),t(P_);else if(r.length)r.push(t);else{r.push(t);var i=window,a=R_+n.type;i[a]=function(){delete i[a],(P_=Z_()||G_()?window[n.type]:window[n.type].maps).Callout=I_(P_),r.forEach((e=>e(P_))),r.length=0},Z_()&&function(e){window._AMapSecurityConfig={securityJsCode:e.securityJsCode||"",serviceHost:e.serviceHost||""}}(n);var o=document.createElement("script"),s=q_(n.type);n.type===W_.QQ&&e.push("geometry"),e.length&&(s+="libraries=".concat(e.join("%2C"),"&")),n.type===W_.BMAP?o.src="".concat(s,"ak=").concat(n.key,"&callback=").concat(a):o.src="".concat(s,"key=").concat(n.key,"&callback=").concat(a),o.onerror=function(){console.error("Map load failed.")},document.body.appendChild(o)}}else console.error("Map key not configured.")}))}var q_=e=>({qq:"https://map.qq.com/api/js?v=2.exp&",google:"https://maps.googleapis.com/maps/api/js?",AMap:"https://webapi.amap.com/maps?v=2.0&",BMapGL:"https://api.map.baidu.com/api?type=webgl&v=1.0&"}[e]);var j_=(e,t,n)=>new Promise(((r,i)=>{var a=e=>{try{s(n.next(e))}catch(t){i(t)}},o=e=>{try{s(n.throw(e))}catch(t){i(t)}},s=e=>e.done?r(e.value):Promise.resolve(e.value).then(a,o);s((n=n.apply(e,t)).next())})),V_="M13.3334375 16 q0.033125 1.1334375 0.783125 1.8834375 q0.75 0.75 1.8834375 0.75 q1.1334375 0 1.8834375 -0.75 q0.75 -0.75 0.75 -1.8834375 q0 -1.1334375 -0.75 -1.8834375 q-0.75 -0.75 -1.8834375 -0.75 q-1.1334375 0 -1.8834375 0.75 q-0.75 0.75 -0.783125 1.8834375 ZM30.9334375 14.9334375 l-1.1334375 0 q-0.5 -5.2 -4.0165625 -8.716875 q-3.516875 -3.5165625 -8.716875 -4.0165625 l0 -1.1334375 q0 -0.4665625 -0.3 -0.7665625 q-0.3 -0.3 -0.7665625 -0.3 q-0.4665625 0 -0.7665625 0.3 q-0.3 0.3 -0.3 0.7665625 l0 1.1334375 q-5.2 0.5 -8.716875 4.0165625 q-3.5165625 3.516875 -4.0165625 8.716875 l-1.1334375 0 q-0.4665625 0 -0.7665625 0.3 q-0.3 0.3 -0.3 0.7665625 q0 0.4665625 0.3 0.7665625 q0.3 0.3 0.7665625 0.3 l1.1334375 0 q0.5 5.2 4.0165625 8.716875 q3.516875 3.5165625 8.716875 4.0165625 l0 1.1334375 q0 0.4665625 0.3 0.7665625 q0.3 0.3 0.7665625 0.3 q0.4665625 0 0.7665625 -0.3 q0.3 -0.3 0.3 -0.7665625 l0 -1.1334375 q5.2 -0.5 8.716875 -4.0165625 q3.5165625 -3.516875 4.0165625 -8.716875 l1.1334375 0 q0.4665625 0 0.7665625 -0.3 q0.3 -0.3 0.3 -0.7665625 q0 -0.4665625 -0.3 -0.7665625 q-0.3 -0.3 -0.7665625 -0.3 ZM17.0665625 27.6665625 l0 -2.0665625 q0 -0.4665625 -0.3 -0.7665625 q-0.3 -0.3 -0.7665625 -0.3 q-0.4665625 0 -0.7665625 0.3 q-0.3 0.3 -0.3 0.7665625 l0 2.0665625 q-4.3 -0.4665625 -7.216875 -3.383125 q-2.916875 -2.916875 -3.3834375 -7.216875 l2.0665625 0 q0.4665625 0 0.7665625 -0.3 q0.3 -0.3 0.3 -0.7665625 q0 -0.4665625 -0.3 -0.7665625 q-0.3 -0.3 -0.7665625 -0.3 l-2.0665625 0 q0.4665625 -4.3 3.3834375 -7.216875 q2.9165625 -2.916875 7.216875 -3.3834375 l0 2.0665625 q0 0.4665625 0.3 0.7665625 q0.3 0.3 0.7665625 0.3 q0.4665625 0 0.7665625 -0.3 q0.3 -0.3 0.3 -0.7665625 l0 -2.0665625 q4.3 0.4665625 7.216875 3.3834375 q2.9165625 2.9165625 3.383125 7.216875 l-2.0665625 0 q-0.4665625 0 -0.7665625 0.3 q-0.3 0.3 -0.3 0.7665625 q0 0.4665625 0.3 0.7665625 q0.3 0.3 0.7665625 0.3 l2.0665625 0 q-0.4665625 4.3 -3.383125 7.216875 q-2.916875 2.9165625 -7.216875 3.383125 Z",$_="data:image/png;base64,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",H_="data:image/png;base64,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",W_=(e=>(e.QQ="qq",e.GOOGLE="google",e.AMAP="AMap",e.BMAP="BMapGL",e.UNKNOWN="",e))(W_||{});function U_(){return j_(this,null,(function*(){return __uniConfig.qqMapKey?{type:"qq",key:__uniConfig.qqMapKey}:{type:"",key:""}}))}var Y_=!1,X_=!1,Z_=()=>X_?Y_:(X_=!0,Y_=!1),G_=()=>!1;const K_=Zp({name:"MapMarker",props:{id:{type:[Number,String],default:""},latitude:{type:[Number,String],require:!0},longitude:{type:[Number,String],require:!0},title:{type:String,default:""},iconPath:{type:String,require:!0},rotate:{type:[Number,String],default:0},alpha:{type:[Number,String],default:1},width:{type:[Number,String],default:""},height:{type:[Number,String],default:""},callout:{type:Object,default:null},label:{type:Object,default:null},anchor:{type:Object,default:null},clusterId:{type:[Number,String],default:""},customCallout:{type:Object,default:null},ariaLabel:{type:String,default:""}},setup(e){var t,n=String(isNaN(Number(e.id))?"":e.id),r=Ds("onMapReady"),i=function(e){var t="uni-map-marker-label-"+e,n=document.createElement("style");return n.id=t,document.head.appendChild(n),us((()=>{n.remove()})),function(e){var r=Object.assign({},e,{position:"absolute",top:"70px",borderStyle:"solid"}),i=document.createElement("div");return Object.keys(r).forEach((e=>{i.style[e]=r[e]||""})),n.innerText=".".concat(t,"{").concat(i.getAttribute("style"),"}"),t}}(n);function a(e){Z_()?e.removeAMapText():e.setMap(null)}if(r(((r,o,s)=>{function l(e){var l,u=e.title;l=Z_()?new o.LngLat(e.longitude,e.latitude):new o.LatLng(e.latitude,e.longitude);var c=new Image,d=0;c.onload=Xm((function*(){var h,f,p,v,g=e.anchor||{},m="number"==typeof g.x?g.x:.5,_="number"==typeof g.y?g.y:1;e.iconPath&&(e.width||e.height)?(f=e.width||c.width/c.height*e.height,p=e.height||c.height/c.width*e.width):(f=c.width/2,p=c.height/2),d=p,v=p-(p-_*p),h="MarkerImage"in o?new o.MarkerImage(c.src,null,null,new o.Point(m*f,_*p),new o.Size(f,p)):"Icon"in o?new o.Icon({image:c.src,size:new o.Size(f,p),imageSize:new o.Size(f,p),imageOffset:new o.Pixel(m*f,_*p)}):{url:c.src,anchor:new o.Point(m,_),size:new o.Size(f,p)},t.setPosition(l),t.setIcon(h),"setRotation"in t&&t.setRotation(e.rotate||0);var y,b=e.label||{};if("label"in t&&(t.label.setMap(null),delete t.label),b.content){var w={borderColor:b.borderColor,borderWidth:(Number(b.borderWidth)||0)+"px",padding:(Number(b.padding)||0)+"px",borderRadius:(Number(b.borderRadius)||0)+"px",backgroundColor:b.bgColor,color:b.color,fontSize:(b.fontSize||14)+"px",lineHeight:(b.fontSize||14)+"px",marginLeft:(Number(b.anchorX||b.x)||0)+"px",marginTop:(Number(b.anchorY||b.y)||0)+"px"};if("Label"in o)y=new o.Label({position:l,map:r,clickable:!1,content:b.content,style:w}),t.label=y;else if("setLabel"in t)if(Z_()){var x='<div style="\n                  margin-left:'.concat(w.marginLeft,";\n                  margin-top:").concat(w.marginTop,";\n                  padding:").concat(w.padding,";\n                  background-color:").concat(w.backgroundColor,";\n                  border-radius:").concat(w.borderRadius,";\n                  line-height:").concat(w.lineHeight,";\n                  color:").concat(w.color,";\n                  font-size:").concat(w.fontSize,';\n\n                  ">\n                  ').concat(b.content,"\n                <div>");t.setLabel({content:x,direction:"bottom-right"})}else{var S=i(w);t.setLabel({text:b.content,color:w.color,fontSize:w.fontSize,className:S})}}var k,C=e.callout||{},T=t.callout;if(C.content||u){Z_()&&C.content&&(C.content=C.content.replaceAll("\n","<br/>"));var A="0px 0px 3px 1px rgba(0,0,0,0.5)",M=-d/2;if((e.width||e.height)&&(M+=14-d/2),k=C.content?{position:l,map:r,top:v,offsetY:M,content:C.content,color:C.color,fontSize:C.fontSize,borderRadius:C.borderRadius,bgColor:C.bgColor,padding:C.padding,boxShadow:C.boxShadow||A,display:C.display}:{position:l,map:r,top:v,offsetY:M,content:u,boxShadow:A},T)T.setOption(k);else if(Z_()){T=t.callout=new o.Callout(k,(e=>{""!==e&&s("callouttap",{},{markerId:Number(e)})}))}else{(T=t.callout=new o.Callout(k)).div.onclick=function(e){""!==n&&s("callouttap",e,{markerId:Number(n)}),e.stopPropagation(),e.preventDefault()},(yield U_()).type===W_.GOOGLE&&(T.div.ontouchstart=function(e){e.stopPropagation()},T.div.onpointerdown=function(e){e.stopPropagation()})}}else T&&(a(T),delete t.callout)})),e.iconPath?c.src=yc(e.iconPath):console.error("Marker.iconPath is required.")}var u;u=e,t=new o.Marker({map:r,flat:!0,autoRotation:!1}),l(u),(o.event||o.Event).addListener(t,"click",(()=>{var e=t.callout;if(e&&!e.alwaysVisible)if(Z_())e.visible=!e.visible,e.visible?t.callout.createAMapText():t.callout.removeAMapText();else if(e.set("visible",!e.visible),e.visible){var r=e.div,i=r.parentNode;i.removeChild(r),i.appendChild(r)}n&&s("markertap",{},{markerId:Number(n),latitude:u.latitude,longitude:u.longitude})})),Vo(e,l)})),n){var o=Ds("addMapChidlContext"),s=Ds("removeMapChidlContext"),l={id:n,translate(e){r(((n,r,i)=>{var a=e.destination,o=e.duration,s=!!e.autoRotate,l=Number(e.rotate)||0,u=0;"getRotation"in t&&(u=t.getRotation());var c=t.getPosition(),d=new r.LatLng(a.latitude,a.longitude),h=r.geometry.spherical.computeDistanceBetween(c,d)/1e3/(("number"==typeof o?o:1e3)/36e5),f=r.event||r.Event,p=f.addListener(t,"moving",(e=>{var n=e.latLng,r=t.label;r&&r.setPosition(n);var i=t.callout;i&&i.setPosition(n)})),v=f.addListener(t,"moveend",(()=>{v.remove(),p.remove(),t.lastPosition=c,t.setPosition(d);var n=t.label;n&&n.setPosition(d);var r=t.callout;r&&r.setPosition(d);var i=e.animationEnd;fn(i)&&i()})),g=0;s&&(t.lastPosition&&(g=r.geometry.spherical.computeHeading(t.lastPosition,c)),l=r.geometry.spherical.computeHeading(c,d)-g),"setRotation"in t&&t.setRotation(u+l),"moveTo"in t?t.moveTo(d,h):(t.setPosition(d),f.trigger(t,"moveend",{}))}))}};o(l),us((()=>s(l)))}return us((function(){t&&(t.label&&"setMap"in t.label&&t.label.setMap(null),t.callout&&a(t.callout),t.setMap(null))})),()=>null}});function J_(e){if(!e)return{r:0,g:0,b:0,a:0};var t=e.slice(1),n=t.length;if(![3,4,6,8].includes(n))return{r:0,g:0,b:0,a:0};3!==n&&4!==n||(t=t.replace(/(\w{1})/g,"$1$1"));var[r,i,a,o]=t.match(/(\w{2})/g),s=parseInt(r,16),l=parseInt(i,16),u=parseInt(a,16);return o?{r:s,g:l,b:u,a:("0x100".concat(o)-65536)/255}:{r:s,g:l,b:u,a:1}}var Q_={points:{type:Array,require:!0},color:{type:String,default:"#000000"},width:{type:[Number,String],default:""},dottedLine:{type:[Boolean,String],default:!1},arrowLine:{type:[Boolean,String],default:!1},arrowIconPath:{type:String,default:""},borderColor:{type:String,default:"#000000"},borderWidth:{type:[Number,String],default:""},colorList:{type:Array,default:()=>[]},level:{type:String,default:""}};const ey=Zp({name:"MapPolyline",props:Q_,setup(e){var t,n;function r(){t&&t.setMap(null),n&&n.setMap(null)}return Ds("onMapReady")(((i,a)=>{function o(e){var r=[];e.points.forEach((e=>{var t;t=Z_()?[e.longitude,e.latitude]:new a.LatLng(e.latitude,e.longitude),r.push(t)}));var o=Number(e.width)||1,{r:s,g:l,b:u,a:c}=J_(e.color),{r:d,g:h,b:f,a:p}=J_(e.borderColor),v={map:i,clickable:!1,path:r,strokeWeight:o,strokeColor:e.color||void 0,strokeDashStyle:e.dottedLine?"dash":"solid"},g=Number(e.borderWidth)||0,m={map:i,clickable:!1,path:r,strokeWeight:o+2*g,strokeColor:e.borderColor||void 0,strokeDashStyle:e.dottedLine?"dash":"solid"};"Color"in a?(v.strokeColor=new a.Color(s,l,u,c),m.strokeColor=new a.Color(d,h,f,p)):(v.strokeColor="rgb(".concat(s,", ").concat(l,", ").concat(u,")"),v.strokeOpacity=c,m.strokeColor="rgb(".concat(d,", ").concat(h,", ").concat(f,")"),m.strokeOpacity=p),g&&(n=new a.Polyline(m)),t=new a.Polyline(v)}o(e),Vo(e,(function(e){r(),o(e)}))})),us(r),()=>null}});const ty=Zp({name:"MapCircle",props:{latitude:{type:[Number,String],require:!0},longitude:{type:[Number,String],require:!0},color:{type:String,default:"#000000"},fillColor:{type:String,default:"#00000000"},radius:{type:[Number,String],require:!0},strokeWidth:{type:[Number,String],default:""},level:{type:String,default:""}},setup(e){var t;function n(){t&&t.setMap(null)}return Ds("onMapReady")(((r,i)=>{function a(e){var n=Z_()||G_()?[e.longitude,e.latitude]:new i.LatLng(e.latitude,e.longitude),a={map:r,center:n,clickable:!1,radius:e.radius,strokeWeight:Number(e.strokeWidth)||1,strokeDashStyle:"solid"},{r:o,g:s,b:l,a:u}=J_(e.fillColor),{r:c,g:d,b:h,a:f}=J_(e.color);"Color"in i?(a.fillColor=new i.Color(o,s,l,u),a.strokeColor=new i.Color(c,d,h,f)):(a.fillColor="rgb(".concat(o,", ").concat(s,", ").concat(l,")"),a.fillOpacity=u,a.strokeColor="rgb(".concat(c,", ").concat(d,", ").concat(h,")"),a.strokeOpacity=f),t=new i.Circle(a),Z_()&&r.add(t)}a(e),Vo(e,(function(e){n(),a(e)}))})),us(n),()=>null}});var ny={id:{type:[Number,String],default:""},position:{type:Object,required:!0},iconPath:{type:String,required:!0},clickable:{type:[Boolean,String],default:""},trigger:{type:Function,required:!0}};const ry=Zp({name:"MapControl",props:ny,setup(e){var t=jl((()=>yc(e.iconPath))),n=jl((()=>{var t="top:".concat(e.position.top||0,"px;left:").concat(e.position.left||0,"px;");return e.position.width&&(t+="width:".concat(e.position.width,"px;")),e.position.height&&(t+="height:".concat(e.position.height,"px;")),t})),r=t=>{e.clickable&&e.trigger("controltap",t,{controlId:e.id})};return()=>ml("div",{class:"uni-map-control"},[ml("img",{src:t.value,style:n.value,class:"uni-map-control-icon",onClick:r},null,12,["src","onClick"])])}});var iy=A,ay=le,oy=S("species"),sy={},ly=le;function uy(e){var t,n;this.promise=new e((function(e,r){if(void 0!==t||void 0!==n)throw TypeError("Bad Promise constructor");t=e,n=r})),this.resolve=ly(t),this.reject=ly(n)}sy.f=function(e){return new uy(e)};var cy=A,dy=C,hy=sy,fy=be,py=a,vy=l,gy=function(e,t){var n,r=iy(e).constructor;return void 0===r||null==(n=iy(r)[oy])?t:ay(n)},my=function(e,t){if(cy(e),dy(t)&&t.constructor===e)return t;var n=hy.f(e);return(0,n.resolve)(t),n.promise};function _y(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return new Promise(((t,n)=>{UniViewJSBridge.invokeServiceMethod("getLocation",e,(e=>{e.latitude&&e.longitude?t(e):n(e&&e.errMsg||"getLocation:fail")}))}))}fy(fy.P+fy.R,"Promise",{finally:function(e){var t=gy(this,py.Promise||vy.Promise),n="function"==typeof e;return this.then(n?function(n){return my(t,e()).then((function(){return n}))}:e,n?function(n){return my(t,e()).then((function(){throw n}))}:e)}});var yy="MAP_LOCATION";const by=Zp({name:"MapLocation",setup(){var e,t=qa({latitude:0,longitude:0,rotate:0}),n=function(){_y({type:"gcj02",isHighAccuracy:!0}).then((e=>{t.latitude=e.latitude,t.longitude=e.longitude})).finally((()=>{e=setTimeout(n,3e4)}))};Ds("onMapReady")(n),us((function(){e&&clearTimeout(e)}));var r=Ds("addMapChidlContext"),i=Ds("removeMapChidlContext"),a={id:yy,state:t};return r(a),us((()=>i(a))),()=>t.latitude?ml(K_,Sl({anchor:{x:.5,y:.5},width:"44",height:"44",iconPath:$_},t),null,16,["iconPath"]):null}}),wy=Zp({name:"MapPolygon",props:{dashArray:{type:Array,default:()=>[0,0]},points:{type:Array,required:!0},strokeWidth:{type:Number,default:1},strokeColor:{type:String,default:"#000000"},fillColor:{type:String,default:"#00000000"},zIndex:{type:Number,default:0}},setup(e){var t;return Ds("onMapReady")(((n,r,i)=>{function a(){var{points:i,strokeWidth:a,strokeColor:o,dashArray:s,fillColor:l,zIndex:u}=e,c=i.map((e=>{var{latitude:t,longitude:n}=e;return Z_()?[n,t]:new r.LatLng(t,n)})),{r:d,g:h,b:f,a:p}=J_(l),{r:v,g:g,b:m,a:_}=J_(o),y={clickable:!0,cursor:"crosshair",editable:!1,map:n,fillColor:"",path:c,strokeColor:"",strokeDashStyle:s.some((e=>e>0))?"dash":"solid",strokeWeight:a,visible:!0,zIndex:u};r.Color?(y.fillColor=new r.Color(d,h,f,p),y.strokeColor=new r.Color(v,g,m,_)):(y.fillColor="rgb(".concat(d,", ").concat(h,", ").concat(f,")"),y.fillOpacity=p,y.strokeColor="rgb(".concat(v,", ").concat(g,", ").concat(m,")"),y.strokeOpacity=_),t?t.setOptions(y):t=new r.Polygon(y)}a(),Vo(e,a)})),us((()=>{t.setMap(null)})),()=>null}});function xy(e){var t=[];return cn(e)&&e.forEach((e=>{e&&e.latitude&&e.longitude&&t.push({latitude:e.latitude,longitude:e.longitude})})),t}function Sy(e,t,n){return Z_()?function(e,t,n){return new e.LngLat(n,t)}(e,t,n):function(e,t,n){return new e.LatLng(t,n)}(e,t,n)}function ky(e){return"getLat"in e?e.getLat():e.lat()}function Cy(e){return"getLng"in e?e.getLng():e.lng()}function Ty(e,t,n){var r,i,a,o,s=Kp(t,n),l=to(null),u=qa({latitude:Number(e.latitude),longitude:Number(e.longitude),includePoints:xy(e.includePoints)}),c=[];function d(e){a?e(i,r,s):c.push(e)}var h=[];function f(e){o?e():c.push(e)}var p={};function v(){var e=i.getCenter();return{scale:i.getZoom(),centerLocation:{latitude:ky(e),longitude:Cy(e)}}}function g(){if(Z_()){var e=[];u.includePoints.forEach((t=>{e.push([t.longitude,t.latitude])}));var t=new r.Bounds(...e);i.setBounds(t)}else{var n=new r.LatLngBounds;u.includePoints.forEach((e=>{var{latitude:t,longitude:i}=e,a=new r.LatLng(t,i);n.extend(a)})),i.fitBounds(n)}}function m(){var t=l.value,a=Sy(r,u.latitude,u.longitude),c=r.event||r.Event,d=new r.Map(t,{center:a,zoom:Number(e.scale),disableDoubleClickZoom:!0,mapTypeControl:!1,zoomControl:!1,scaleControl:!1,panControl:!1,fullscreenControl:!1,streetViewControl:!1,keyboardShortcuts:!1,minZoom:5,maxZoom:18,draggable:!0});Vo((()=>e.scale),(e=>{d.setZoom(Number(e)||16)})),f((()=>{var e;u.includePoints.length&&(g(),e=Sy(r,u.latitude,u.longitude),i.setCenter(e))}));var p=c.addListener(d,"bounds_changed",(()=>{p.remove(),o=!0,h.forEach((e=>e())),h.length=0}));c.addListener(d,"click",(()=>{s("tap",{},{}),s("click",{},{})})),c.addListener(d,"dragstart",(()=>{s("regionchange",{},{type:"begin",causedBy:"gesture"})})),c.addListener(d,"dragend",(()=>{s("regionchange",{},on({type:"end",causedBy:"drag"},v()))}));var m=()=>{n("update:scale",d.getZoom()),s("regionchange",{},on({type:"end",causedBy:"scale"},v()))};return c.addListener(d,"zoom_changed",m),c.addListener(d,"zoomend",m),c.addListener(d,"center_changed",(()=>{var e=d.getCenter(),t=ky(e),r=Cy(e);n("update:latitude",t),n("update:longitude",r)})),d}Vo([()=>e.latitude,()=>e.longitude],(e=>{var[t,n]=e,a=Number(t),o=Number(n);if((a!==u.latitude||o!==u.longitude)&&(u.latitude=a,u.longitude=o,i)){var s=Sy(r,u.latitude,u.longitude);i.setCenter(s)}})),Vo((()=>e.includePoints),(e=>{u.includePoints=xy(e),o&&g()}),{deep:!0});try{a_((function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2?arguments[2]:void 0;switch(e){case"getCenterLocation":d((()=>{var t=i.getCenter();n({latitude:ky(t),longitude:Cy(t),errMsg:"".concat(e,":ok")})}));break;case"moveToLocation":var a=Number(t.latitude),s=Number(t.longitude);if(!a||!s){var l=p[yy];l&&(a=l.state.latitude,s=l.state.longitude)}if(a&&s){if(u.latitude=a,u.longitude=s,i){var c=Sy(r,a,s);i.setCenter(c)}d((()=>{n({errMsg:"".concat(e,":ok")})}))}else n({errMsg:"".concat(e,":fail")});break;case"translateMarker":d((()=>{var r=p[t.markerId];if(r){try{r.translate(t)}catch(i){return void n({errMsg:"".concat(e,":fail ").concat(i.message)})}n({errMsg:"".concat(e,":ok")})}else n({errMsg:"".concat(e,":fail not found")})}));break;case"includePoints":u.includePoints=xy(t.includePoints),(o||Z_())&&g(),f((()=>{n({errMsg:"".concat(e,":ok")})}));break;case"getRegion":f((()=>{var t=i.getBounds(),r=t.getSouthWest(),a=t.getNorthEast();n({southwest:{latitude:ky(r),longitude:Cy(r)},northeast:{latitude:ky(a),longitude:Cy(a)},errMsg:"".concat(e,":ok")})}));break;case"getScale":d((()=>{n({scale:i.getZoom(),errMsg:"".concat(e,":ok")})}))}}),s_())}catch(_){}return as((()=>{F_(e.libraries,(e=>{r=e,i=m(),a=!0,c.forEach((e=>e(i,r,s))),c.length=0,s("updated",{},{})}))})),Ps("onMapReady",d),Ps("addMapChidlContext",(function(e){p[e.id]=e})),Ps("removeMapChidlContext",(function(e){delete p[e.id]})),{state:u,mapRef:l,trigger:s}}const Ay=Xp({name:"MapWeb",props:{id:{type:String,default:""},latitude:{type:[String,Number],default:0},longitude:{type:[String,Number],default:0},scale:{type:[String,Number],default:16},markers:{type:Array,default:()=>[]},includePoints:{type:Array,default:()=>[]},polyline:{type:Array,default:()=>[]},circles:{type:Array,default:()=>[]},controls:{type:Array,default:()=>[]},showLocation:{type:[Boolean,String],default:!1},libraries:{type:Array,default:()=>[]},polygons:{type:Array,default:()=>[]}},emits:["markertap","labeltap","callouttap","controltap","regionchange","tap","click","updated","update:scale","update:latitude","update:longitude"],setup(e,t){var{emit:n,slots:r}=t,i=to(null),{mapRef:a,trigger:o}=Ty(e,i,n);return()=>ml("uni-map",{class:"web",ref:i,id:e.id},[ml("div",{ref:a,style:"width: 100%; height: 100%; position: relative; overflow: hidden"},null,512),e.markers.map((e=>ml(K_,Sl({key:e.id},e),null,16))),e.polyline.map((e=>ml(ey,e,null,16))),e.circles.map((e=>ml(ty,e,null,16))),e.controls.map((e=>ml(ry,Sl(e,{trigger:o}),null,16,["trigger"]))),e.showLocation&&ml(by,null,null),e.polygons.map((e=>ml(wy,e,null,16))),ml("div",{style:"position: absolute;top: 0;width: 100%;height: 100%;overflow: hidden;pointer-events: none;"},[r.default&&r.default()])],8,["id"])}});const My=Xp({name:"Map",props:{id:{type:String,default:""},latitude:{type:[Number,String],default:""},longitude:{type:[Number,String],default:""},scale:{type:[String,Number],default:16},markers:{type:Array,default:()=>[]},polyline:{type:Array,default:()=>[]},circles:{type:Array,default:()=>[]},polygons:{type:Array,default:()=>[]},controls:{type:Array,default:()=>[]}},components:{MapDefault:N_,MapWeb:Ay},emits:["click","regionchange","controltap","markertap","callouttap"],setup(e,t){var{emit:n}=t;function r(e){n("click",e)}function i(e){n("regionchange",e)}function a(e){n("controltap",e)}function o(e){n("markertap",e)}function s(e){n("callouttap",e)}return()=>__uniConfig.qqMapKey?ml(Ay,{id:e.id,latitude:e.latitude,longitude:e.longitude,scale:e.scale,markers:e.markers,polyline:e.polyline,circles:e.circles,polygons:e.polygons,controls:e.controls,onClick:r,onRegionchange:i,onControltap:a,onMarkertap:o,onCallouttap:s},null,8,["id","latitude","longitude","scale","markers","polyline","circles","polygons","controls","onClick","onRegionchange","onControltap","onMarkertap","onCallouttap"]):ml(N_,{id:e.id,latitude:e.latitude,longitude:e.longitude,scale:e.scale,markers:e.markers,polyline:e.polyline,circles:e.circles,polygons:e.polygons,controls:e.controls,onClick:r,onRegionchange:i,onControltap:a,onMarkertap:o,onCallouttap:s},null,8,["id","latitude","longitude","scale","markers","polyline","circles","polygons","controls","onClick","onRegionchange","onControltap","onMarkertap","onCallouttap"])}});var Ey={latitude:{type:Number},longitude:{type:Number},keyword:{type:String,default:""},useSecureNetwork:{type:Boolean,default:!1}};function Oy(e){var t=qa([]),n=to(-1),r=jl((()=>t[n.value])),i=qa({loading:!0,pageSize:20,pageIndex:1,hasNextPage:!0,nextPage:null,selectedIndex:n,selected:r});function a(e){e.forEach((e=>{t.push({name:e.title||e.name,address:e.address,distance:e._distance||e.distance,latitude:e.location.lat,longitude:e.location.lng})}))}function o(){return s.apply(this,arguments)}function s(){return s=Xm((function*(){i.loading=!0;var n=yield U_();if(n.type===W_.GOOGLE){if(i.pageIndex>1&&i.nextPage)return void i.nextPage();new google.maps.places.PlacesService(document.createElement("div"))[e.keyword?"textSearch":"nearbySearch"]({location:{lat:e.latitude,lng:e.longitude},query:e.keyword,radius:5e3},((e,n,r)=>{i.loading=!1,e&&e.length&&e.forEach((e=>{t.push({name:e.name||"",address:e.vicinity||e.formatted_address||"",distance:0,latitude:e.geometry.location.lat(),longitude:e.geometry.location.lng()})})),r&&(r.hasNextPage?i.nextPage=()=>{r.nextPage()}:i.hasNextPage=!1)}))}else n.type===W_.QQ?function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return new Promise(((t,n)=>{UniViewJSBridge.invokeServiceMethod("mapPlaceSearch",e,(e=>{e&&e.errMsg?n(e):t(e)}))}))}({keyword:e.keyword,latitude:e.latitude,longitude:e.longitude,pageIndex:i.pageIndex,pageSize:i.pageSize,secure:e.useSecureNetwork}).then((e=>{a(e),i.loading=!1})).catch((e=>{i.loading=!1})):n.type===W_.AMAP&&window.AMap.plugin("AMap.PlaceSearch",(function(){var t=new window.AMap.PlaceSearch({city:"全国",pageSize:10,pageIndex:i.pageIndex}),n=e.keyword||"",r=e.keyword?5e4:5e3;t.searchNearBy(n,[e.longitude,e.latitude],r,(function(e,t){"error"===e?console.error(t):"no_data"===e?i.hasNextPage=!1:a(t.poiList.pois)})),i.loading=!1}))})),s.apply(this,arguments)}return{listState:i,list:t,loadMore:function(){!i.loading&&i.hasNextPage&&(i.pageIndex++,o())},reset:function(){i.selectedIndex=-1,i.pageIndex=1,i.hasNextPage=!0,i.nextPage=null,t.splice(0,t.length)},getList:o}}const Ly=Zp({name:"LoctaionPicker",props:Ey,emits:["close"],setup(e,t){var{emit:n}=t,r=to(null),i=Kp(r,n);fi();var{t:a}=ui(),o=function(e){var t=qa({latitude:0,longitude:0,keyword:"",searching:!1,useSecureNetwork:e.useSecureNetwork});function n(){e.latitude&&e.longitude&&(t.latitude=e.latitude,t.longitude=e.longitude)}return e.keyword&&(t.keyword=e.keyword,t.searching=!0),Vo([()=>e.latitude,()=>e.longitude],n),n(),t}(e),{list:s,listState:l,loadMore:u,reset:c,getList:d}=Oy(o),h=Hr((()=>{c(),o.keyword&&d()}),1e3,{setTimeout:setTimeout,clearTimeout:clearTimeout});function f(e){o.keyword=e.detail.value,h()}function p(e){if(l.selected){var t=new CustomEvent("close",{detail:on({},l.selected)});i("close",t,t.detail)}}function v(e){var t=new CustomEvent("close",{detail:{}});i("close",t,t.detail)}function g(e){var t=e.detail.centerLocation;t&&_(t)}function m(){_y({type:"gcj02",isHighAccuracy:!0}).then((e=>{var{latitude:t,longitude:n}=e;_({latitude:t,longitude:n})}))}function _(e){var{latitude:t,longitude:n}=e;o.latitude=t,o.longitude=n,c(),d()}return Vo((()=>o.searching),(e=>{c(),e||d()})),o.latitude&&o.longitude||m(),()=>{var e,t=s.map(((e,t)=>{return ml("div",{key:t,class:{"list-item":!0,selected:l.selectedIndex===t},onClick:()=>{l.selectedIndex=t,o.latitude=e.latitude,o.longitude=e.longitude}},[Pu(Iu,"#007aff",24),ml("div",{class:"list-item-title"},[e.name]),ml("div",{class:"list-item-detail"},[(n=e.distance,n>100?"".concat(n>1e3?(n/1e3).toFixed(1)+"k":n.toFixed(0),"m | "):n>0?"<100m | ":""),e.address])],10,["onClick"]);var n}));return l.loading&&t.unshift(ml("div",{class:"list-loading"},[ml("i",{class:"uni-loading"},null)])),ml("div",{class:"uni-system-choose-location",ref:r},[ml(My,{latitude:o.latitude,longitude:o.longitude,class:"map","show-location":!0,libraries:["places"],onUpdated:d,onRegionchange:g},{default:()=>[ml("div",{class:"map-location",style:'background-image: url("'.concat(H_,'")')},null),ml("div",{class:"map-move",onClick:m},[Pu(V_,"#000000",24)],8,["onClick"])],_:1},8,["latitude","longitude","show-location","onUpdated","onRegionchange"]),ml("div",{class:"nav"},[ml("div",{class:"nav-btn back",onClick:v},[Pu("M17.25 16.156l7.375-7.313q0.281-0.281 0.281-0.641t-0.281-0.641q-0.25-0.25-0.625-0.25t-0.625 0.25l-7.375 7.344-7.313-7.344q-0.25-0.25-0.625-0.25t-0.625 0.25q-0.281 0.25-0.281 0.625t0.281 0.625l7.313 7.344-7.375 7.344q-0.281 0.25-0.281 0.625t0.281 0.625q0.125 0.125 0.281 0.188t0.344 0.063q0.156 0 0.328-0.063t0.297-0.188l7.375-7.344 7.375 7.406q0.125 0.156 0.297 0.219t0.328 0.063q0.188 0 0.344-0.078t0.281-0.203q0.281-0.25 0.281-0.609t-0.281-0.641l-7.375-7.406z","#ffffff",26)],8,["onClick"]),ml("div",{class:{"nav-btn":!0,confirm:!0,disable:!l.selected},onClick:p},[Pu(Iu,"#ffffff",26)],10,["onClick"])]),ml("div",{class:"menu"},[ml("div",{class:"search"},[ml(Pg,{value:o.keyword,class:"search-input",placeholder:a("uni.chooseLocation.search"),onFocus:()=>o.searching=!0,onInput:f},null,8,["value","placeholder","onFocus","onInput"]),o.searching&&ml("div",{class:"search-btn",onClick:()=>{o.searching=!1,o.keyword=""}},[a("uni.chooseLocation.cancel")],8,["onClick"])]),ml(Bm,{"scroll-y":!0,class:"list",onScrolltolower:u},(e=t,"function"==typeof e||"[object Object]"===Object.prototype.toString.call(e)&&!hl(e)?t:{default:()=>[t],_:2}),8,["scroll-y","onScrolltolower"])])],512)}}});var zy={latitude:{type:Number},longitude:{type:Number},scale:{type:Number,default:18},name:{type:String,default:""},address:{type:String,default:""},showNav:{type:Boolean,default:!1}};const Ny=Zp({name:"LocationView",props:zy,emits:["close","navChange"],setup(e,t){var{emit:n}=t,r=to(null),i=Kp(r,n),a=function(e){var t=qa({center:{latitude:0,longitude:0},marker:{id:1,latitude:0,longitude:0,iconPath:H_,width:32,height:52},location:{id:2,latitude:0,longitude:0,iconPath:$_,width:44,height:44}});function n(){e.latitude&&e.longitude&&(t.center.latitude=e.latitude,t.center.longitude=e.longitude,t.marker.latitude=e.latitude,t.marker.longitude=e.longitude)}return Vo([()=>e.latitude,()=>e.longitude],n),n(),t}(e);function o(e){var t=e.detail.centerLocation;t&&(a.center.latitude=t.latitude,a.center.longitude=t.longitude)}function s(){i("navClick",new CustomEvent("navClick",{}))}function l(e){var t=new CustomEvent("close",{});i("close",t,t.detail)}function u(e){var{latitude:t,longitude:n}=e;a.center.latitude=t,a.center.longitude=n}return _y({type:"gcj02",isHighAccuracy:!0}).then((e=>{var{latitude:t,longitude:n}=e;a.location.latitude=t,a.location.longitude=n})),()=>ml("div",{class:"uni-system-open-location",ref:r},[ml(My,{latitude:a.center.latitude,longitude:a.center.longitude,class:"map",markers:[a.marker,a.location],onRegionchange:o},{default:()=>[ml("div",{class:"map-move",onClick:()=>u(a.location)},[Pu(V_,"#000000",24)],8,["onClick"])]},8,["latitude","longitude","markers","onRegionchange"]),ml("div",{class:"info"},[ml("div",{class:"name",onClick:()=>u(a.marker)},[e.name],8,["onClick"]),ml("div",{class:"address",onClick:()=>u(a.marker)},[e.address],8,["onClick"]),ml("div",{class:"nav",onClick:s},[Pu("M28 17c-6.49396875 0-12.13721875 2.57040625-15 6.34840625V5.4105l6.29859375 6.29859375c0.387875 0.387875 1.02259375 0.387875 1.4105 0 0.387875-0.387875 0.387875-1.02259375 0-1.4105L12.77853125 2.36803125a0.9978125 0.9978125 0 0 0-0.0694375-0.077125c-0.1944375-0.1944375-0.45090625-0.291375-0.70721875-0.290875l-0.00184375-0.0000625-0.00184375 0.0000625c-0.2563125-0.0005-0.51278125 0.09640625-0.70721875 0.290875a0.9978125 0.9978125 0 0 0-0.0694375 0.077125l-7.930625 7.9305625c-0.387875 0.387875-0.387875 1.02259375 0 1.4105 0.387875 0.387875 1.02259375 0.387875 1.4105 0L11 5.4105V29c0 0.55 0.45 1 1 1s1-0.45 1-1c0-5.52284375 6.71571875-10 15-10 0.55228125 0 1-0.44771875 1-1 0-0.55228125-0.44771875-1-1-1z","#ffffff",26)],8,["onClick"])]),ml("div",{class:"nav-btn-back",onClick:l},[Pu("M21.781 7.844l-9.063 8.594 9.063 8.594q0.25 0.25 0.25 0.609t-0.25 0.578q-0.25 0.25-0.578 0.25t-0.578-0.25l-9.625-9.125q-0.156-0.125-0.203-0.297t-0.047-0.359q0-0.156 0.047-0.328t0.203-0.297l9.625-9.125q0.25-0.25 0.578-0.25t0.578 0.25q0.25 0.219 0.25 0.578t-0.25 0.578z","#ffffff",26)],8,["onClick"])],512)}});var Iy={SELECTOR:"selector",MULTISELECTOR:"multiSelector",TIME:"time",DATE:"date"},Py={YEAR:"year",MONTH:"month",DAY:"day"};function Dy(e){return e>9?e:"0".concat(e)}function By(e,t){e=String(e||"");var n=new Date;if(t===Iy.TIME){var r=e.split(":");2===r.length&&n.setHours(parseInt(r[0]),parseInt(r[1]))}else{var i=e.split("-");3===i.length&&n.setFullYear(parseInt(i[0]),parseInt(String(parseFloat(i[1])-1)),parseInt(i[2]))}return n}const Ry=Xp({name:"Picker",props:{name:{type:String,default:""},range:{type:Array,default:()=>[]},rangeKey:{type:String,default:""},value:{type:[Number,String,Array],default:0},mode:{type:String,default:Iy.SELECTOR,validator:e=>Object.values(Iy).indexOf(e)>=0},fields:{type:String,default:""},start:{type:String,default:function(e){if(e.mode===Iy.TIME)return"00:00";if(e.mode===Iy.DATE){var t=(new Date).getFullYear()-100;switch(e.fields){case Py.YEAR:return t;case Py.MONTH:return t+"-01";default:return t+"-01-01"}}return""}},end:{type:String,default:function(e){if(e.mode===Iy.TIME)return"23:59";if(e.mode===Iy.DATE){var t=(new Date).getFullYear()+100;switch(e.fields){case Py.YEAR:return t;case Py.MONTH:return t+"-12";default:return t+"-12-31"}}return""}},disabled:{type:[Boolean,String],default:!1}},emits:["change","cancel","columnchange"],setup(e,t){var{emit:n}=t;di();var{t:r,getLocale:i}=ui(),a=to(null),o=Kp(a,n),s=to(null),l=to(null),u=__uniConfig.darkmode?plus.navigator.getUIStyle():"light";function c(e){u=e.theme}UniViewJSBridge.subscribe(sr,c),ls((()=>{UniViewJSBridge.unsubscribe(sr,c)}));var d=()=>{var t=e.value;switch(e.mode){case Iy.MULTISELECTOR:cn(t)||(t=[]),cn(s.value)||(s.value=[]);for(var n=s.value.length=Math.max(t.length,e.range.length),r=0;r<n;r++){var i=Number(t[r]),a=Number(s.value[r]),o=isNaN(i)?isNaN(a)?0:a:i;s.value.splice(r,1,o<0?0:o)}break;case Iy.TIME:case Iy.DATE:s.value=String(t);break;default:var l=Number(t);s.value=l<0?0:l}},h=e=>{l.value&&l.value.sendMessage(e)},f=(t,n)=>{t.mode!==Iy.TIME&&t.mode!==Iy.DATE||t.fields?(t.fields=Object.values(Py).includes(t.fields)?t.fields:Py.DAY,(e=>{var t={event:"cancel"};l.value=Ju({url:"__uniapppicker",data:on({},e,{theme:u}),style:{titleNView:!1,animationType:"none",animationDuration:0,background:"rgba(0,0,0,0)",popGesture:"none"},onMessage:n=>{var r=n.event;if("created"!==r)return"columnchange"===r?(delete n.event,void o(r,{},n)):void(t=n);h(e)},onClose:()=>{l.value=null;var e=t.event;delete t.event,e&&o(e,{},t)}})})(t)):((t,n)=>{plus.nativeUI[e.mode===Iy.TIME?"pickTime":"pickDate"]((t=>{var n=t.date;o("change",{},{value:e.mode===Iy.TIME?"".concat(Dy(n.getHours()),":").concat(Dy(n.getMinutes())):"".concat(n.getFullYear(),"-").concat(Dy(n.getMonth()+1),"-").concat(Dy(n.getDate()))})}),(()=>{o("cancel",{},{})}),e.mode===Iy.TIME?{time:By(e.value,Iy.TIME),popover:n}:{date:By(e.value,Iy.DATE),minDate:By(e.start,Iy.DATE),maxDate:By(e.end,Iy.DATE),popover:n})})(0,n)},p=t=>{if(!e.disabled){var n=t.currentTarget.getBoundingClientRect();f(Object.assign({},e,{value:s.value,locale:i(),messages:{done:r("uni.picker.done"),cancel:r("uni.picker.cancel")}}),{top:n.top+p_(),left:n.left,width:n.width,height:n.height})}},v=Ds(ev,!1),g={submit:()=>[e.name,s.value],reset:()=>{switch(e.mode){case Iy.SELECTOR:s.value=0;break;case Iy.MULTISELECTOR:cn(e.value)&&(s.value=e.value.map((e=>0)));break;case Iy.DATE:case Iy.TIME:s.value=""}}};return v&&(v.addField(g),ls((()=>v.removeField(g)))),Object.keys(e).forEach((t=>{"name"!==t&&Vo((()=>e[t]),(e=>{var n={};n[t]=e,h(n)}),{deep:!0})})),Vo((()=>e.value),d,{deep:!0}),d(),()=>ml("uni-picker",{ref:a,onClick:p},[ml("slot",null,null)],8,["onClick"])}});var Fy={id:{type:String,default:""},src:{type:String,default:""},duration:{type:[Number,String],default:""},controls:{type:[Boolean,String],default:!0},danmuList:{type:Array,default:()=>[]},danmuBtn:{type:[Boolean,String],default:!1},enableDanmu:{type:[Boolean,String],default:!1},autoplay:{type:[Boolean,String],default:!1},loop:{type:[Boolean,String],default:!1},muted:{type:[Boolean,String],default:!1},objectFit:{type:String,default:"contain"},poster:{type:String,default:""},direction:{type:[String,Number],default:""},showProgress:{type:Boolean,default:!0},initialTime:{type:[String,Number],default:0},showFullscreenBtn:{type:[Boolean,String],default:!0},pageGesture:{type:[Boolean,String],default:!1},enableProgressGesture:{type:[Boolean,String],default:!0},vslideGesture:{type:[Boolean,String],default:!1},vslideGestureInFullscreen:{type:[Boolean,String],default:!1},showPlayBtn:{type:[Boolean,String],default:!0},showMuteBtn:{type:[Boolean,String],default:!1},enablePlayGesture:{type:[Boolean,String],default:!0},showCenterPlayBtn:{type:[Boolean,String],default:!0},showLoading:{type:[Boolean,String],default:!0},codec:{type:String,default:"hardware"},httpCache:{type:[Boolean,String],default:!1},playStrategy:{type:[Number,String],default:0},header:{type:Object,default:()=>({})},advanced:{type:Array,default:()=>[]},title:{type:String,default:""},isLive:{type:Boolean,default:!1}},qy=["play","pause","ended","timeupdate","fullscreenchange","fullscreenclick","waiting","error"],jy=["play","pause","stop","seek","sendDanmu","playbackRate","requestFullScreen","exitFullScreen"];const Vy=Xp({name:"Video",props:Fy,emits:qy,setup(e,t){var n,{emit:r}=t,i=to(null),a=Kp(i,r),o=to(null),s=g_(e,["id"]),{position:l,hidden:u,onParentReady:c}=m_(o),d=Number(e.isLive?3:e.playStrategy);return c((()=>{n=plus.video.createVideoPlayer("video"+Date.now(),Object.assign({},s.value,l,{playStrategy:isNaN(d)?0:d})),plus.webview.currentWebview().append(n),u.value&&n.hide(),qy.forEach((e=>{n.addEventListener(e,(t=>{a(e,{},t.detail)}))})),Vo((()=>s.value),(e=>n.setStyles(e)),{deep:!0}),Vo((()=>l),(e=>n.setStyles(e)),{deep:!0}),Vo((()=>u.value),(e=>{n[e?"hide":"show"](),e||n.setStyles(l)}))})),a_(((e,t)=>{if(jy.includes(e)){var r;switch(e){case"seek":r=t.position;break;case"sendDanmu":r=t;break;case"playbackRate":r=t.rate;break;case"requestFullScreen":r=t.direction}n&&n[e](r)}}),s_()),ls((()=>{n&&n.close()})),()=>ml("uni-video",{ref:i,id:e.id},[ml("div",{ref:o,class:"uni-video-container"},null,512),ml("div",{class:"uni-video-slot"},null)],8,["id"])}});var $y,Hy={src:{type:String,default:""},updateTitle:{type:Boolean,default:!0},webviewStyles:{type:Object,default:()=>({})}};const Wy=Xp({name:"WebView",props:Hy,setup(e){var t=Bu(),n=to(null),{hidden:r,onParentReady:i}=m_(n),a=jl((()=>e.webviewStyles));return i((()=>{var n;(e=>{var{htmlId:t,src:n,webviewStyles:r,props:i}=e,a=plus.webview.currentWebview(),o=on({"uni-app":"none",isUniH5:!0,contentAdjust:!1},r),s=a.getTitleNView();if(s){var l=Qn+parseFloat(o.top||"0");plus.navigator.isImmersedStatusbar()&&(l+=f_()),o.top=String(l),o.bottom=o.bottom||"0"}$y=plus.webview.create(n,t,o),s&&$y.addEventListener("titleUpdate",(function(){var e;if(i.updateTitle){var t=null===(e=$y)||void 0===e?void 0:e.getTitle();a.setStyle({titleNView:{titleText:t&&"null"!==t?t:" "}})}})),plus.webview.currentWebview().append($y)})({htmlId:to("webviewId"+t).value,src:yc(e.src),webviewStyles:a.value,props:e}),UniViewJSBridge.publishHandler("webviewInserted",{},t),r.value&&(null===(n=$y)||void 0===n||n.hide())})),ls((()=>{var e;plus.webview.currentWebview().remove($y),null===(e=$y)||void 0===e||e.close("none"),$y=null,UniViewJSBridge.publishHandler("webviewRemoved",{},t)})),Vo((()=>e.src),(t=>{var n,r=yc(t)||"";if(r){var i;if(/^(http|https):\/\//.test(r)&&e.webviewStyles.progress)null===(i=$y)||void 0===i||i.setStyle({progress:{color:e.webviewStyles.progress.color}});null===(n=$y)||void 0===n||n.loadURL(r)}})),Vo(a,(e=>{var t;null===(t=$y)||void 0===t||t.setStyle(e)})),Vo(r,(e=>{$y&&$y[e?"hide":"show"]()})),()=>ml("uni-web-view",{ref:n},null,512)}});var Uy={"#text":class extends _p{constructor(e,t,n,r){super(e,"#text",t,document.createTextNode("")),this._text="",this.init(r),this.insert(t,n)}init(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];this._text=e.t||"",t&&this.update()}setText(e){this._text=e,this.update(),this.updateView()}update(){var{space:e,decode:t}=this.$parent&&this.$parent.$props||{};this.$.textContent=Um(this._text,{space:e,decode:t}).join(Jn)}},"#comment":class extends _p{constructor(e,t,n){super(e,"#comment",t,document.createComment("")),this.insert(t,n)}},VIEW:class extends d_{constructor(e,t,n,r){super(e,document.createElement("uni-view"),t,n,r)}},IMAGE:class extends y_{constructor(e,t,n,r){super(e,"uni-image",fg,t,n,r)}},TEXT:class extends l_{constructor(e,t,n,r){super(e,document.createElement("uni-text"),t,n,r,u_),this._text=""}init(e){this._text=e.t||"",super.init(e)}setText(e){this._text=e,this.update(),this.updateView()}update(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],{$props:{space:t,decode:n}}=this;this.$.textContent=Um(this._text,{space:t,decode:n}).join(Jn),super.update(e)}},NAVIGATOR:class extends y_{constructor(e,t,n,r){super(e,"uni-navigator",pm,t,n,r,"uni-navigator")}},FORM:class extends y_{constructor(e,t,n,r){super(e,"uni-form",tv,t,n,r,"span")}},BUTTON:class extends y_{constructor(e,t,n,r){super(e,"uni-button",lv,t,n,r)}},INPUT:class extends y_{constructor(e,t,n,r){super(e,"uni-input",Pg,t,n,r)}init(e){super.init(e),S_(this.$props)}},LABEL:class extends y_{constructor(e,t,n,r){super(e,"uni-label",iv,t,n,r)}},RADIO:class extends y_{constructor(e,t,n,r){super(e,"uni-radio",Em,t,n,r,".uni-radio-wrapper")}setText(e){w_(this.$holder,"uni-radio-input",e)}},CHECKBOX:class extends y_{constructor(e,t,n,r){super(e,"uni-checkbox",xv,t,n,r,".uni-checkbox-wrapper")}setText(e){w_(this.$holder,"uni-checkbox-input",e)}},"CHECKBOX-GROUP":class extends y_{constructor(e,t,n,r){super(e,"uni-checkbox-group",wv,t,n,r)}},AD:class extends y_{constructor(e,t,n,r){super(e,"uni-ad",__,t,n,r)}},CAMERA:class extends k_{constructor(e,t,n){super(e,"uni-camera",t,n)}},CANVAS:class extends y_{constructor(e,t,n,r){super(e,"uni-canvas",yv,t,n,r,"uni-canvas > div")}},"COVER-IMAGE":class extends y_{constructor(e,t,n,r){super(e,"uni-cover-image",A_,t,n,r)}},"COVER-VIEW":class extends b_{constructor(e,t,n,r){super(e,"uni-cover-view",M_,t,n,r,".uni-cover-view")}},EDITOR:class extends y_{constructor(e,t,n,r){super(e,"uni-editor",ag,t,n,r)}},"FUNCTIONAL-PAGE-NAVIGATOR":class extends k_{constructor(e,t,n){super(e,"uni-functional-page-navigator",t,n)}},ICON:class extends y_{constructor(e,t,n,r){super(e,"uni-icon",ug,t,n,r)}},"RADIO-GROUP":class extends y_{constructor(e,t,n,r){super(e,"uni-radio-group",Mm,t,n,r)}},"LIVE-PLAYER":class extends k_{constructor(e,t,n){super(e,"uni-live-player",t,n)}},"LIVE-PUSHER":class extends y_{constructor(e,t,n,r){super(e,"uni-live-pusher",L_,t,n,r,".uni-live-pusher-slot")}},MAP:class extends y_{constructor(e,t,n,r){super(e,"uni-map",My,t,n,r,__uniConfig.qqMapKey?void 0:".uni-map-slot")}},"LOCATION-PICKER":class extends y_{constructor(e,t,n,r){super(e,"uni-location-picker",Ly,t,n,r)}},"LOCATION-VIEW":class extends y_{constructor(e,t,n,r){super(e,"uni-location-view",Ny,t,n,r)}},"MOVABLE-AREA":class extends b_{constructor(e,t,n,r){super(e,"uni-movable-area",Hg,t,n,r)}},"MOVABLE-VIEW":class extends y_{constructor(e,t,n,r){super(e,"uni-movable-view",rm,t,n,r)}},"OFFICIAL-ACCOUNT":class extends k_{constructor(e,t,n){super(e,"uni-official-account",t,n)}},"OPEN-DATA":class extends k_{constructor(e,t,n){super(e,"uni-open-data",t,n)}},PICKER:class extends y_{constructor(e,t,n,r){super(e,"uni-picker",Ry,t,n,r)}},"PICKER-VIEW":class extends b_{constructor(e,t,n,r){super(e,"uni-picker-view",vm,t,n,r,".uni-picker-view-wrapper")}},"PICKER-VIEW-COLUMN":class extends b_{constructor(e,t,n,r){super(e,"uni-picker-view-column",xm,t,n,r,".uni-picker-view-content")}},PROGRESS:class extends y_{constructor(e,t,n,r){super(e,"uni-progress",Cm,t,n,r)}},"RICH-TEXT":class extends y_{constructor(e,t,n,r){super(e,"uni-rich-text",Im,t,n,r)}},"SCROLL-VIEW":class extends y_{constructor(e,t,n,r){super(e,"uni-scroll-view",Bm,t,n,r,".uni-scroll-view-content")}setText(e){w_(this.$holder,"uni-scroll-view-refresher",e)}},SLIDER:class extends y_{constructor(e,t,n,r){super(e,"uni-slider",Rm,t,n,r)}},SWIPER:class extends b_{constructor(e,t,n,r){super(e,"uni-swiper",Vm,t,n,r,".uni-swiper-slide-frame")}},"SWIPER-ITEM":class extends y_{constructor(e,t,n,r){super(e,"uni-swiper-item",$m,t,n,r)}},SWITCH:class extends y_{constructor(e,t,n,r){super(e,"uni-switch",Hm,t,n,r)}},TEXTAREA:class extends y_{constructor(e,t,n,r){super(e,"uni-textarea",t_,t,n,r)}init(e){super.init(e),S_(this.$props)}},VIDEO:class extends y_{constructor(e,t,n,r){super(e,"uni-video",Vy,t,n,r,".uni-video-slot")}},"WEB-VIEW":class extends y_{constructor(e,t,n,r){super(e,"uni-web-view",Wy,t,n,r)}}};function Yy(e,t,n,r){var i,a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:{};if(0===e)i=new _p(e,t,n,document.createElement(t));else{var o=Uy[t];i=o?new o(e,n,r,a):new Hp(e,document.createElement(t),n,r,a)}return function(e,t){np.set(e,t)}(e,i),i}var Xy=[],Zy=!1;function Gy(e){if(Zy)return e();Xy.push(e)}function Ky(){Zy=!0,Xy.forEach((e=>{try{e()}catch(t){console.error(t)}})),Xy.length=0}function Jy(e){var{css:t,route:n,platform:r,pixelRatio:i,windowWidth:a,disableScroll:o,statusbarHeight:s,windowTop:l,windowBottom:u}=e;!function(e){window.__PAGE_INFO__={route:e}}(n),function(e,t,n){window.__SYSTEM_INFO__={platform:e,pixelRatio:t,windowWidth:n}}(r,i,a),Yy(0,"div",-1,-1).$=document.getElementById("app");var c=plus.webview.currentWebview().id;window.__id__=c,document.title="".concat(n,"[").concat(c,"]"),function(e,t,n){var r={"--window-left":"0px","--window-right":"0px","--window-top":t+"px","--window-bottom":n+"px","--status-bar-height":e+"px"};!function(e){var t=document.documentElement.style;Object.keys(e).forEach((n=>{t.setProperty(n,e[n])}))}(r)}(s,l,u),o&&document.addEventListener("touchmove",Ru),t?function(e){var t=document.createElement("link");t.type="text/css",t.rel="stylesheet",t.href=e+".css",t.onload=Ky,t.onerror=Ky,document.head.appendChild(t)}(n):Ky()}var Qy=!1;function eb(e,t){var{scrollTop:n,selector:r,duration:i}=e;!function(e,t){if(pn(e)){var n=document.querySelector(e);if(n){var{top:r}=n.getBoundingClientRect();e=r+window.pageYOffset;var i=document.querySelector("uni-page-head");i&&(e-=i.offsetHeight)}}e<0&&(e=0);var a=document.documentElement,{clientHeight:o,scrollHeight:s}=a;if(e=Math.min(e,s-o),0!==t){if(window.scrollY!==e){var l=t=>{if(t<=0)window.scrollTo(0,e);else{var n=e-window.scrollY;requestAnimationFrame((function(){window.scrollTo(0,window.scrollY+n/t*10),l(t-10)}))}};l(t)}}else a.scrollTop=document.body.scrollTop=e}(r||n||0,i),t()}function tb(e){var t=e[0];t[0]===Lr?Jy(t[1]):Gy((()=>function(e){var t=e[0],n=function(e){if(!e.length)return e=>e;var t=function(n){var r=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];if("number"==typeof n)return e[n];var i={};return n.forEach((e=>{var[n,a]=e;i[t(n)]=r?t(a):a})),i};return t}(t[0]===pc?t[1]:[]);e.forEach((e=>{switch(e[0]){case Lr:return Jy(e[1]);case zr:return;case Nr:var t=e[3];return Yy(e[1],n(e[2]),-1===t?0:t,e[4],tp(n,e[5]));case Ir:return rp(e[1]).insert(e[2],e[3],tp(n,e[4]));case Pr:return rp(e[1]).remove();case Dr:return rp(e[1]).setAttr(n(e[2]),n(e[3]));case Br:return rp(e[1]).removeAttr(n(e[2]));case Rr:return rp(e[1]).addEvent(n(e[2]),e[3]);case jr:return rp(e[1]).addWxsEvent(n(e[2]),n(e[3]),e[4]);case Fr:return rp(e[1]).removeEvent(n(e[2]));case qr:return rp(e[1]).setText(n(e[2]));case Vr:return function(e){if(!Qy){Qy=!0;var t={onReachBottomDistance:e,onPageScroll(e){UniViewJSBridge.publishHandler(lr,{scrollTop:e})},onReachBottom(){UniViewJSBridge.publishHandler(ur)}};requestAnimationFrame((()=>document.addEventListener("scroll",Wu(t))))}}(e[1])}})),function(){try{[...ap].sort(((e,t)=>e.priority-t.priority)).forEach((e=>e()))}finally{ap.clear()}}()}(e)))}function nb(){UniViewJSBridge.publishHandler(fc)}function rb(e){return window.__$__(e).$}function ib(e,t){var n={},{top:r,topWindowHeight:i}=function(){var e=document.documentElement.style,t=Mu(),n=Au(e,"--window-bottom"),r=Au(e,"--window-left"),i=Au(e,"--window-right"),a=Au(e,"--top-window-height");return{top:t,bottom:n?n+Cu.bottom:0,left:r?r+Cu.left:0,right:i?i+Cu.right:0,topWindowHeight:a||0}}();if(t.node){var a=e.tagName.split("-")[1];a&&(n.node=e.querySelector(a))}if(t.id&&(n.id=e.id),t.dataset&&(n.dataset=mr(e)),t.rect||t.size){var o=e.getBoundingClientRect();t.rect&&(n.left=o.left,n.right=o.right,n.top=o.top-r-i,n.bottom=o.bottom-r-i),t.size&&(n.width=o.width,n.height=o.height)}if(cn(t.properties)&&t.properties.forEach((e=>{e=e.replace(/-([a-z])/g,(function(e,t){return t.toUpperCase()}))})),t.scrollOffset)if("UNI-SCROLL-VIEW"===e.tagName){var s=e.children[0].children[0];n.scrollLeft=s.scrollLeft,n.scrollTop=s.scrollTop,n.scrollHeight=s.scrollHeight,n.scrollWidth=s.scrollWidth}else n.scrollLeft=0,n.scrollTop=0,n.scrollHeight=0,n.scrollWidth=0;if(cn(t.computedStyle)){var l=getComputedStyle(e);t.computedStyle.forEach((e=>{n[e]=l[e]}))}return t.context&&(n.contextInfo=function(e){return e.__uniContextInfo}(e)),n}function ab(e,t){return(e.matches||e.matchesSelector||e.mozMatchesSelector||e.msMatchesSelector||e.oMatchesSelector||e.webkitMatchesSelector||function(e){for(var t=this.parentElement.querySelectorAll(e),n=t.length;--n>=0&&t.item(n)!==this;);return n>-1}).call(e,t)}function ob(e,t,n,r,i){var a=function(e,t){return e?window.__$__(e).$:t.$el}(t,e),o=a.parentElement;if(!o)return r?null:[];var{nodeType:s}=a,l=3===s||8===s;if(r){var u=l?o.querySelector(n):ab(a,n)?a:a.querySelector(n);return u?ib(u,i):null}var c=[],d=(l?o:a).querySelectorAll(n);return d&&d.length&&[].forEach.call(d,(e=>{c.push(ib(e,i))})),!l&&ab(a,n)&&c.unshift(ib(a,i)),c}function sb(e,t,n){var r=[];t.forEach((t=>{var{component:n,selector:i,single:a,fields:o}=t;null===n?r.push(function(e){var t={};if(e.id&&(t.id=""),e.dataset&&(t.dataset={}),e.rect&&(t.left=0,t.right=0,t.top=0,t.bottom=0),e.size&&(t.width=document.documentElement.clientWidth,t.height=document.documentElement.clientHeight),e.scrollOffset){var n=document.documentElement,r=document.body;t.scrollLeft=n.scrollLeft||r.scrollLeft||0,t.scrollTop=n.scrollTop||r.scrollTop||0,t.scrollHeight=n.scrollHeight||r.scrollHeight||0,t.scrollWidth=n.scrollWidth||r.scrollWidth||0}return t}(o)):r.push(ob(e,n,i,a,o))})),n(r)}function lb(e,t){var{reqId:n,component:r,options:i,callback:a}=e,o=rb(r);(o.__io||(o.__io={}))[n]=function(e,t,n){Kf();var r=t.relativeToSelector?e.querySelector(t.relativeToSelector):null,i=new IntersectionObserver((e=>{e.forEach((e=>{n({intersectionRatio:Qf(e),intersectionRect:Jf(e.intersectionRect),boundingClientRect:Jf(e.boundingClientRect),relativeRect:Jf(e.rootBounds),time:Date.now(),dataset:mr(e.target),id:e.target.id})}))}),{root:r,rootMargin:t.rootMargin,threshold:t.thresholds});if(t.observeAll){i.USE_MUTATION_OBSERVER=!0;for(var a=e.querySelectorAll(t.selector),o=0;o<a.length;o++)i.observe(a[o])}else{i.USE_MUTATION_OBSERVER=!1;var s=e.querySelector(t.selector);s?i.observe(s):console.warn("Node ".concat(t.selector," is not found. Intersection observer will not trigger."))}return i}(o,i,a)}var ub={},cb={};function db(e){return e.replace(/([A-Z])/g,"-$1").toLowerCase()}function hb(e,t){var{reqId:n,component:r,options:i,callback:a}=e,o=ub[n]=window.matchMedia(function(e){var t=[];for(var n of["width","minWidth","maxWidth","height","minHeight","maxHeight","orientation"])"orientation"!==n&&e[n]&&Number(e[n]>=0)&&t.push("(".concat(db(n),": ").concat(Number(e[n]),"px)")),"orientation"===n&&e[n]&&t.push("(".concat(db(n),": ").concat(e[n],")"));return t.join(" and ")}(i)),s=cb[n]=e=>a(e.matches);s(o),o.addListener(s)}function fb(e,t){var{family:n,source:r,desc:i}=e;(function(e,t,n){var r=document.fonts;if(r){var i=new FontFace(e,t,n);return i.load().then((()=>{r.add&&r.add(i)}))}return new Promise((r=>{var i=document.createElement("style"),a=[];if(n){var{style:o,weight:s,stretch:l,unicodeRange:u,variant:c,featureSettings:d}=n;o&&a.push("font-style:".concat(o)),s&&a.push("font-weight:".concat(s)),l&&a.push("font-stretch:".concat(l)),u&&a.push("unicode-range:".concat(u)),c&&a.push("font-variant:".concat(c)),d&&a.push("font-feature-settings:".concat(d))}i.innerText='@font-face{font-family:"'.concat(e,'";src:').concat(t,";").concat(a.join(";"),"}"),document.head.appendChild(i),r()}))})(n,r=r.startsWith('url("')||r.startsWith("url('")?"url('".concat(yc(r.substring(5,r.length-2)),"')"):r.startsWith("url(")?"url('".concat(yc(r.substring(4,r.length-1)),"')"):yc(r),i).then((()=>{t()})).catch((e=>{t(e.toString())}))}var pb={$el:document.body};function vb(){var e=Bu();!function(e,t){UniViewJSBridge.subscribe(yi(e,vi),t?t(wi):wi)}(e,(e=>function(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];Gy((()=>{e.apply(null,n)}))})),bi(e,"requestComponentInfo",((e,t)=>{sb(pb,e.reqs,t)})),bi(e,"addIntersectionObserver",(e=>{lb(on({},e,{callback(t){UniViewJSBridge.publishHandler(e.eventName,t)}}))})),bi(e,"removeIntersectionObserver",(e=>{!function(e){var{reqId:t,component:n}=e,r=rb(n),i=r.__io&&r.__io[t];i&&(i.disconnect(),delete r.__io[t])}(e)})),bi(e,"addMediaQueryObserver",(e=>{hb(on({},e,{callback(t){UniViewJSBridge.publishHandler(e.eventName,t)}}))})),bi(e,"removeMediaQueryObserver",(e=>{!function(e){var{reqId:t,component:n}=e,r=cb[t],i=ub[t];i&&(i.removeListener(r),delete cb[t],delete ub[t])}(e)})),bi(e,Gf,eb),bi(e,Zf,fb),bi(e,Xf,(e=>{!function(e,t){var{pageStyle:n,rootFontSize:r}=t;n&&(document.querySelector("uni-page-body")||document.body).setAttribute("style",n),r&&document.documentElement.style.fontSize!==r&&(document.documentElement.style.fontSize=r)}(0,e)}))}function gb(){Di(),vb(),function(){var{subscribe:e}=UniViewJSBridge;e(hc,tb),e(vc,(e=>ui().setLocale(e))),e(fc,nb)}(),function(){if(0===String(navigator.vendor).indexOf("Apple")){var e,t=null;document.documentElement.addEventListener("click",(n=>{clearTimeout(e),t&&Math.abs(n.pageX-t.pageX)<=44&&Math.abs(n.pageY-t.pageY)<=44&&n.timeStamp-t.timeStamp<=450&&n.preventDefault(),t=n,e=setTimeout((()=>{t=null}),450)}))}}(),gc.publishHandler(fc)}window.uni=ep,window.UniViewJSBridge=gc,window.rpx2px=qf,window.normalizeStyleName=Np,window.normalizeStyleValue=bp,window.__$__=rp,window.__f__=function(e,t){for(var n=arguments.length,r=new Array(n>2?n-2:0),i=2;i<n;i++)r[i-2]=arguments[i];uni.__log__?uni.__log__(e,t,...r):console[e].apply(console,[...r,t])},"undefined"!=typeof plus?gb():document.addEventListener("plusready",gb)}));
