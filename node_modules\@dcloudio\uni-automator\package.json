{"name": "@dcloudio/uni-automator", "version": "3.0.0-4030620241128001", "description": "@dcloudio/uni-automator", "main": "dist/index.js", "files": ["dist", "lib"], "sideEffects": false, "repository": {"type": "git", "url": "git+https://github.com/dcloudio/uni-app.git", "directory": "packages/uni-automator"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/dcloudio/uni-app/issues"}, "uni-app": {"name": "uniAutomator", "apply": ["app", "h5", "mp-weixin"], "uvue": true}, "gitHead": "33e807d66e1fe47e2ee08ad9c59247e37b8884da", "dependencies": {"@dcloudio/uni-cli-shared": "3.0.0-4030620241128001", "address": "^1.1.2", "cross-env": "^7.0.3", "debug": "^4.3.3", "default-gateway": "^6.0.3", "fs-extra": "^10.0.0", "jsonc-parser": "^3.2.0", "licia": "^1.29.0", "merge": "^2.1.1", "qrcode-reader": "^1.0.4", "qrcode-terminal": "^0.12.0", "ws": "^8.4.2"}, "devDependencies": {"@types/debug": "^4.1.7", "@types/fs-extra": "^9.0.13"}, "peerDependencies": {"jest": "27.0.4", "jest-environment-node": "27.5.1"}}