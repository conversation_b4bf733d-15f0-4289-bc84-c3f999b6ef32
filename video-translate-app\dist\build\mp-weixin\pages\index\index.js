"use strict";const e=require("../../common/vendor.js"),t=require("../../common/assets.js"),a=e.defineComponent({__name:"index",setup(a){const s=e.ref([]);e.onMounted(()=>{r()});const r=async()=>{try{const t=await e.wx$1.cloud.callFunction({name:"get-recent-tasks",data:{limit:3}});s.value=t.result||[]}catch(t){console.error("加载最近任务失败:",t)}},o=()=>{e.index.navigateTo({url:"/pages/upload/upload"})},n=()=>{e.index.switchTab({url:"/pages/history/history"})},i=e=>{switch(e){case"processing":return"处理中";case"completed":return"已完成";case"failed":return"失败";default:return"未知"}},c=e=>{if(!e)return"";const t=new Date(e),a=(new Date).getTime()-t.getTime();return a<6e4?"刚刚":a<36e5?`${Math.floor(a/6e4)}分钟前`:a<864e5?`${Math.floor(a/36e5)}小时前`:`${t.getMonth()+1}-${t.getDate()}`};return(a,r)=>e.e({a:t._imports_0,b:e.o(o),c:s.value.length>0},s.value.length>0?{d:e.o(n),e:e.f(s.value,(t,a,s)=>({a:e.t(t.fileName||"未命名视频"),b:e.t(c(t.createTime)),c:e.t(i(t.status)),d:e.n(t.status),e:t._id,f:e.o(a=>(t=>{"processing"===t.status?e.index.navigateTo({url:`/pages/process/process?fileId=${t.originalVideoFileId}`}):"completed"===t.status&&e.index.navigateTo({url:`/pages/result/result?taskId=${t._id}`})})(t),t._id)}))}:{})}}),s=e._export_sfc(a,[["__scopeId","data-v-85454300"]]);wx.createPage(s);
