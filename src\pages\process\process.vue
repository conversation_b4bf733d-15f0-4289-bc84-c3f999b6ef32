<template>
  <view class="process-container">
    <!-- 处理状态卡片 -->
    <view class="status-card">
      <view class="status-icon">
        <text v-if="taskStatus === 'processing'" class="processing-icon">⏳</text>
        <text v-else-if="taskStatus === 'completed'" class="success-icon">✅</text>
        <text v-else-if="taskStatus === 'failed'" class="error-icon">❌</text>
      </view>
      
      <view class="status-text">
        <text class="status-title">{{ getStatusTitle() }}</text>
        <text class="status-desc">{{ getStatusDesc() }}</text>
      </view>
    </view>
    
    <!-- 处理步骤 -->
    <view class="steps-container">
      <view class="step-item" :class="{ active: currentStep >= 1, completed: currentStep > 1 }">
        <view class="step-number">1</view>
        <view class="step-content">
          <text class="step-title">视频上传</text>
          <text class="step-desc">视频文件上传到云端</text>
        </view>
      </view>
      
      <view class="step-item" :class="{ active: currentStep >= 2, completed: currentStep > 2 }">
        <view class="step-number">2</view>
        <view class="step-content">
          <text class="step-title">语音识别</text>
          <text class="step-desc">AI识别视频中的语音内容</text>
        </view>
      </view>
      
      <view class="step-item" :class="{ active: currentStep >= 3, completed: currentStep > 3 }">
        <view class="step-number">3</view>
        <view class="step-content">
          <text class="step-title">字幕生成</text>
          <text class="step-desc">生成SRT字幕文件</text>
        </view>
      </view>
      
      <view class="step-item" :class="{ active: currentStep >= 4, completed: currentStep > 4 }">
        <view class="step-number">4</view>
        <view class="step-content">
          <text class="step-title">视频合成</text>
          <text class="step-desc">将字幕烧录到视频中</text>
        </view>
      </view>
    </view>
    
    <!-- 操作按钮 -->
    <view class="action-buttons">
      <button 
        v-if="taskStatus === 'completed'" 
        @click="viewResult" 
        class="result-btn"
        type="primary"
      >
        查看结果
      </button>
      <button 
        v-if="taskStatus === 'failed'" 
        @click="retryProcess" 
        class="retry-btn"
        type="primary"
      >
        重新处理
      </button>
      <button @click="goBack" class="back-btn">返回首页</button>
    </view>
    
    <!-- 错误信息 -->
    <view v-if="errorMsg" class="error-message">
      <text>{{ errorMsg }}</text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'

// 响应式数据
const taskStatus = ref('processing') // processing, completed, failed
const currentStep = ref(1)
const errorMsg = ref('')
const fileId = ref('')
const taskId = ref('')

// 轮询定时器
let pollTimer: any = null

// 页面加载时获取参数
onMounted(() => {
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  const options = currentPage.options || {}
  
  fileId.value = options.fileId || ''
  
  if (fileId.value) {
    startPolling()
  } else {
    uni.showToast({
      title: '参数错误',
      icon: 'none'
    })
  }
})

// 页面卸载时清除定时器
onUnmounted(() => {
  if (pollTimer) {
    clearInterval(pollTimer)
  }
})

// 开始轮询任务状态
const startPolling = () => {
  // 立即查询一次
  checkTaskStatus()
  
  // 每3秒轮询一次
  pollTimer = setInterval(() => {
    if (taskStatus.value === 'processing') {
      checkTaskStatus()
    } else {
      clearInterval(pollTimer)
    }
  }, 3000)
}

// 检查任务状态
const checkTaskStatus = async () => {
  try {
    const res = await wx.cloud.callFunction({
      name: 'get-task-status',
      data: { fileId: fileId.value }
    })
    
    const task = res.result
    if (task) {
      taskId.value = task._id
      taskStatus.value = task.status
      errorMsg.value = task.errorMsg || ''
      
      // 根据状态更新步骤
      updateCurrentStep(task.status)
    }
  } catch (error) {
    console.error('查询任务状态失败:', error)
  }
}

// 更新当前步骤
const updateCurrentStep = (status: string) => {
  switch (status) {
    case 'processing':
      // 可以根据更详细的状态来设置步骤
      currentStep.value = 2
      break
    case 'completed':
      currentStep.value = 4
      break
    case 'failed':
      // 保持当前步骤不变
      break
  }
}

// 获取状态标题
const getStatusTitle = (): string => {
  switch (taskStatus.value) {
    case 'processing':
      return '正在处理中...'
    case 'completed':
      return '处理完成'
    case 'failed':
      return '处理失败'
    default:
      return '未知状态'
  }
}

// 获取状态描述
const getStatusDesc = (): string => {
  switch (taskStatus.value) {
    case 'processing':
      return '请耐心等待，通常需要1-3分钟'
    case 'completed':
      return '视频字幕已生成完成'
    case 'failed':
      return '处理过程中出现错误'
    default:
      return ''
  }
}

// 查看结果
const viewResult = () => {
  uni.navigateTo({
    url: `/pages/result/result?taskId=${taskId.value}`
  })
}

// 重新处理
const retryProcess = () => {
  uni.showModal({
    title: '确认重新处理',
    content: '是否重新处理该视频？',
    success: (res) => {
      if (res.confirm) {
        // 重新开始处理
        taskStatus.value = 'processing'
        currentStep.value = 1
        errorMsg.value = ''
        startPolling()
      }
    }
  })
}

// 返回首页
const goBack = () => {
  uni.switchTab({
    url: '/pages/index/index'
  })
}
</script>

<style scoped>
.process-container {
  padding: 40rpx;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.status-card {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  display: flex;
  align-items: center;
}

.status-icon {
  font-size: 80rpx;
  margin-right: 30rpx;
}

.processing-icon {
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.success-icon {
  color: #52c41a;
}

.error-icon {
  color: #ff4d4f;
}

.status-text .status-title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.status-text .status-desc {
  display: block;
  font-size: 28rpx;
  color: #666;
}

.steps-container {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
}

.step-item {
  display: flex;
  align-items: center;
  margin-bottom: 40rpx;
  position: relative;
}

.step-item:last-child {
  margin-bottom: 0;
}

.step-item:not(:last-child)::after {
  content: '';
  position: absolute;
  left: 35rpx;
  top: 70rpx;
  width: 2rpx;
  height: 40rpx;
  background-color: #e8e8e8;
}

.step-item.active::after {
  background-color: #007AFF;
}

.step-number {
  width: 70rpx;
  height: 70rpx;
  border-radius: 50%;
  background-color: #e8e8e8;
  color: #999;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: bold;
  margin-right: 30rpx;
}

.step-item.active .step-number {
  background-color: #007AFF;
  color: white;
}

.step-item.completed .step-number {
  background-color: #52c41a;
  color: white;
}

.step-content .step-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.step-content .step-desc {
  display: block;
  font-size: 26rpx;
  color: #666;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.result-btn, .retry-btn {
  background-color: #007AFF;
  color: white;
  border: none;
  border-radius: 10rpx;
  height: 88rpx;
  font-size: 32rpx;
}

.back-btn {
  background-color: white;
  color: #007AFF;
  border: 2rpx solid #007AFF;
  border-radius: 10rpx;
  height: 88rpx;
  font-size: 32rpx;
}

.error-message {
  background: #fff2f0;
  border: 2rpx solid #ffccc7;
  border-radius: 10rpx;
  padding: 20rpx;
  margin-top: 20rpx;
  color: #ff4d4f;
  font-size: 28rpx;
}
</style>
