<view class="result-container data-v-8f447252"><view class="video-section data-v-8f447252"><video wx:if="{{a}}" src="{{b}}" controls class="result-video data-v-8f447252" poster="{{c}}" show-fullscreen-btn show-play-btn show-center-play-btn></video><view wx:else class="video-loading data-v-8f447252"><text class="data-v-8f447252">视频加载中...</text></view></view><view class="video-info data-v-8f447252"><view class="info-item data-v-8f447252"><text class="info-label data-v-8f447252">处理时间:</text><text class="info-value data-v-8f447252">{{d}}</text></view><view class="info-item data-v-8f447252"><text class="info-label data-v-8f447252">视频时长:</text><text class="info-value data-v-8f447252">{{e}}</text></view><view class="info-item data-v-8f447252"><text class="info-label data-v-8f447252">文件大小:</text><text class="info-value data-v-8f447252">{{f}}</text></view></view><view class="action-buttons data-v-8f447252"><button bindtap="{{g}}" class="download-btn data-v-8f447252" type="primary"> 下载视频 </button><button bindtap="{{h}}" class="share-btn data-v-8f447252"> 分享视频 </button><button bindtap="{{i}}" class="subtitle-btn data-v-8f447252"> 查看字幕 </button></view><view wx:if="{{j}}" class="subtitle-modal data-v-8f447252" bindtap="{{p}}"><view class="subtitle-content data-v-8f447252" catchtap="{{o}}"><view class="subtitle-header data-v-8f447252"><text class="subtitle-title data-v-8f447252">字幕内容</text><text class="close-btn data-v-8f447252" bindtap="{{k}}">×</text></view><scroll-view class="subtitle-list data-v-8f447252" scroll-y><view wx:for="{{l}}" wx:for-item="item" wx:key="d" class="subtitle-item data-v-8f447252"><text class="subtitle-time data-v-8f447252">{{item.a}} --> {{item.b}}</text><text class="subtitle-text data-v-8f447252">{{item.c}}</text></view></scroll-view><view class="subtitle-actions data-v-8f447252"><button bindtap="{{m}}" class="copy-btn data-v-8f447252" size="mini">复制字幕</button><button bindtap="{{n}}" class="download-subtitle-btn data-v-8f447252" size="mini">下载SRT</button></view></view></view></view>