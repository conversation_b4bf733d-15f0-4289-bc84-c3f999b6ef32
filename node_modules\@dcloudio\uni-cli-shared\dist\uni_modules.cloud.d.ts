export declare function genEncryptEasyComModuleIndex(platform: typeof process.env.UNI_UTS_PLATFORM, components: Record<string, '.vue' | '.uvue'>): string;
export declare function parseUniModulesWithComponents(inputDir: string): Record<string, Record<string, ".vue" | ".uvue">>;
/**
 * 解析 easyCom 组件列表
 * @param pluginId
 * @param inputDir
 * @returns
 */
export declare function parseEasyComComponents(pluginId: string, inputDir: string, detectBinary?: boolean): Record<string, ".vue" | ".uvue">;
export declare function findEncryptUniModules(inputDir: string, cacheDir?: string): Record<string, EncryptPackageJson | undefined>;
export declare function findUploadEncryptUniModulesFiles(uniModules: Record<string, EncryptPackageJson | undefined>, platform: typeof process.env.UNI_UTS_PLATFORM, inputDir: string): Record<string, string[]>;
export declare function packUploadEncryptUniModules(uniModules: Record<string, EncryptPackageJson | undefined>, platform: typeof process.env.UNI_UTS_PLATFORM, inputDir: string, cacheDir: string): {
    zipFile: string;
    modules: string[];
};
interface EncryptPackageJson {
    id: string;
    version: string;
    uni_modules: {
        dependencies: string[];
        artifacts: {
            env: {
                compilerVersion: string;
            } & Record<string, any>;
            apis: string[];
            components: string[];
            scopedSlots: string[];
            declaration: string;
        };
    };
}
export declare function initCheckEnv(): Record<string, string>;
export declare function resolveEncryptUniModule(id: string, platform: typeof process.env.UNI_UTS_PLATFORM, isX?: boolean): string | undefined;
export declare function checkEncryptUniModules(inputDir: string, params: {
    mode: 'development' | 'production';
    packType: 'debug' | 'release';
    compilerVersion: string;
    appid: string;
    appname: string;
    platform: typeof process.env.UNI_UTS_PLATFORM;
    'uni-app-x': boolean;
}): Promise<{} | undefined>;
export declare function parseUniModulesArtifacts(): {
    name: string;
    package: string;
    scopedSlots: string[];
    declaration: string;
}[];
export {};
