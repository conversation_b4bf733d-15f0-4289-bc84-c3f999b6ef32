{"name": "@dcloudio/uni-app-vite", "version": "3.0.0-4030620241128001", "description": "uni-app-vite", "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist", "lib"], "repository": {"type": "git", "url": "git+https://github.com/dcloudio/uni-app.git", "directory": "packages/uni-app-vite"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "license": "Apache-2.0", "gitHead": "33e807d66e1fe47e2ee08ad9c59247e37b8884da", "dependencies": {"@dcloudio/uni-cli-shared": "3.0.0-4030620241128001", "@dcloudio/uni-i18n": "3.0.0-4030620241128001", "@dcloudio/uni-nvue-styler": "3.0.0-4030620241128001", "@dcloudio/uni-shared": "3.0.0-4030620241128001", "@rollup/pluginutils": "^5.0.5", "@vitejs/plugin-vue": "5.1.0", "@vue/compiler-dom": "3.4.21", "@vue/compiler-sfc": "3.4.21", "debug": "^4.3.3", "fs-extra": "^10.0.0", "picocolors": "^1.0.0"}, "devDependencies": {"@types/debug": "^4.1.7", "@types/fs-extra": "^9.0.13", "@vue/compiler-core": "3.4.21", "esbuild": "^0.20.1", "postcss": "^8.4.21", "rollup": "^4.21.2", "vite": "^5.2.8", "vue": "3.4.21"}}