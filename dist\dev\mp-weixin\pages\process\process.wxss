
.process-container.data-v-dfd768e4 {
  padding: 40rpx;
  min-height: 100vh;
  background-color: #f5f5f5;
}
.status-card.data-v-dfd768e4 {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  display: flex;
  align-items: center;
}
.status-icon.data-v-dfd768e4 {
  font-size: 80rpx;
  margin-right: 30rpx;
}
.processing-icon.data-v-dfd768e4 {
  animation: rotate-dfd768e4 2s linear infinite;
}
@keyframes rotate-dfd768e4 {
from { transform: rotate(0deg);
}
to { transform: rotate(360deg);
}
}
.success-icon.data-v-dfd768e4 {
  color: #52c41a;
}
.error-icon.data-v-dfd768e4 {
  color: #ff4d4f;
}
.status-text .status-title.data-v-dfd768e4 {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}
.status-text .status-desc.data-v-dfd768e4 {
  display: block;
  font-size: 28rpx;
  color: #666;
}
.steps-container.data-v-dfd768e4 {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
}
.step-item.data-v-dfd768e4 {
  display: flex;
  align-items: center;
  margin-bottom: 40rpx;
  position: relative;
}
.step-item.data-v-dfd768e4:last-child {
  margin-bottom: 0;
}
.step-item.data-v-dfd768e4:not(:last-child)::after {
  content: '';
  position: absolute;
  left: 35rpx;
  top: 70rpx;
  width: 2rpx;
  height: 40rpx;
  background-color: #e8e8e8;
}
.step-item.active.data-v-dfd768e4::after {
  background-color: #007AFF;
}
.step-number.data-v-dfd768e4 {
  width: 70rpx;
  height: 70rpx;
  border-radius: 50%;
  background-color: #e8e8e8;
  color: #999;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: bold;
  margin-right: 30rpx;
}
.step-item.active .step-number.data-v-dfd768e4 {
  background-color: #007AFF;
  color: white;
}
.step-item.completed .step-number.data-v-dfd768e4 {
  background-color: #52c41a;
  color: white;
}
.step-content .step-title.data-v-dfd768e4 {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}
.step-content .step-desc.data-v-dfd768e4 {
  display: block;
  font-size: 26rpx;
  color: #666;
}
.action-buttons.data-v-dfd768e4 {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}
.result-btn.data-v-dfd768e4, .retry-btn.data-v-dfd768e4 {
  background-color: #007AFF;
  color: white;
  border: none;
  border-radius: 10rpx;
  height: 88rpx;
  font-size: 32rpx;
}
.back-btn.data-v-dfd768e4 {
  background-color: white;
  color: #007AFF;
  border: 2rpx solid #007AFF;
  border-radius: 10rpx;
  height: 88rpx;
  font-size: 32rpx;
}
.error-message.data-v-dfd768e4 {
  background: #fff2f0;
  border: 2rpx solid #ffccc7;
  border-radius: 10rpx;
  padding: 20rpx;
  margin-top: 20rpx;
  color: #ff4d4f;
  font-size: 28rpx;
}
