"use strict";const _export_sfc=(e,t)=>{const n=e.__vccOpts||e;for(const[o,r]of t)n[o]=r;return n};
/**
* @vue/shared v3.4.21
* (c) 2018-present <PERSON><PERSON> (<PERSON>) You and Vue contributors
* @license MIT
**/function makeMap(e,t){const n=new Set(e.split(","));return e=>n.has(e)}const EMPTY_OBJ={},EMPTY_ARR=[],NOOP=()=>{},NO=()=>!1,isOn=e=>111===e.charCodeAt(0)&&110===e.charCodeAt(1)&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),isModelListener=e=>e.startsWith("onUpdate:"),extend=Object.assign,remove=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},hasOwnProperty$1=Object.prototype.hasOwnProperty,hasOwn=(e,t)=>hasOwnProperty$1.call(e,t),isArray=Array.isArray,isMap=e=>"[object Map]"===toTypeString(e),isSet=e=>"[object Set]"===toTypeString(e),isFunction=e=>"function"==typeof e,isString=e=>"string"==typeof e,isSymbol=e=>"symbol"==typeof e,isObject=e=>null!==e&&"object"==typeof e,isPromise=e=>(isObject(e)||isFunction(e))&&isFunction(e.then)&&isFunction(e.catch),objectToString=Object.prototype.toString,toTypeString=e=>objectToString.call(e),toRawType=e=>toTypeString(e).slice(8,-1),isPlainObject=e=>"[object Object]"===toTypeString(e),isIntegerKey=e=>isString(e)&&"NaN"!==e&&"-"!==e[0]&&""+parseInt(e,10)===e,isReservedProp=makeMap(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),cacheStringFunction=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},camelizeRE=/-(\w)/g,camelize=cacheStringFunction(e=>e.replace(camelizeRE,(e,t)=>t?t.toUpperCase():"")),hyphenateRE=/\B([A-Z])/g,hyphenate=cacheStringFunction(e=>e.replace(hyphenateRE,"-$1").toLowerCase()),capitalize=cacheStringFunction(e=>e.charAt(0).toUpperCase()+e.slice(1)),toHandlerKey=cacheStringFunction(e=>e?`on${capitalize(e)}`:""),hasChanged=(e,t)=>!Object.is(e,t),invokeArrayFns$1=(e,t)=>{for(let n=0;n<e.length;n++)e[n](t)},def=(e,t,n)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,value:n})},looseToNumber=e=>{const t=parseFloat(e);return isNaN(t)?e:t};let _globalThis;const getGlobalThis=()=>_globalThis||(_globalThis="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:{});function normalizeClass(e){let t="";if(isString(e))t=e;else if(isArray(e))for(let n=0;n<e.length;n++){const o=normalizeClass(e[n]);o&&(t+=o+" ")}else if(isObject(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const toDisplayString=e=>isString(e)?e:null==e?"":isArray(e)||isObject(e)&&(e.toString===objectToString||!isFunction(e.toString))?JSON.stringify(e,replacer,2):String(e),replacer=(e,t)=>t&&t.__v_isRef?replacer(e,t.value):isMap(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((e,[t,n],o)=>(e[stringifySymbol(t,o)+" =>"]=n,e),{})}:isSet(t)?{[`Set(${t.size})`]:[...t.values()].map(e=>stringifySymbol(e))}:isSymbol(t)?stringifySymbol(t):!isObject(t)||isArray(t)||isPlainObject(t)?t:String(t),stringifySymbol=(e,t="")=>{var n;return isSymbol(e)?`Symbol(${null!=(n=e.description)?n:t})`:e},SLOT_DEFAULT_NAME="d",ON_SHOW="onShow",ON_HIDE="onHide",ON_LAUNCH="onLaunch",ON_ERROR="onError",ON_THEME_CHANGE="onThemeChange",ON_PAGE_NOT_FOUND="onPageNotFound",ON_UNHANDLE_REJECTION="onUnhandledRejection",ON_EXIT="onExit",ON_LOAD="onLoad",ON_READY="onReady",ON_UNLOAD="onUnload",ON_INIT="onInit",ON_SAVE_EXIT_STATE="onSaveExitState",ON_RESIZE="onResize",ON_BACK_PRESS="onBackPress",ON_PAGE_SCROLL="onPageScroll",ON_TAB_ITEM_TAP="onTabItemTap",ON_REACH_BOTTOM="onReachBottom",ON_PULL_DOWN_REFRESH="onPullDownRefresh",ON_SHARE_TIMELINE="onShareTimeline",ON_SHARE_CHAT="onShareChat",ON_ADD_TO_FAVORITES="onAddToFavorites",ON_SHARE_APP_MESSAGE="onShareAppMessage",ON_NAVIGATION_BAR_BUTTON_TAP="onNavigationBarButtonTap",ON_NAVIGATION_BAR_SEARCH_INPUT_CLICKED="onNavigationBarSearchInputClicked",ON_NAVIGATION_BAR_SEARCH_INPUT_CHANGED="onNavigationBarSearchInputChanged",ON_NAVIGATION_BAR_SEARCH_INPUT_CONFIRMED="onNavigationBarSearchInputConfirmed",ON_NAVIGATION_BAR_SEARCH_INPUT_FOCUS_CHANGED="onNavigationBarSearchInputFocusChanged";function hasLeadingSlash(e){return 0===e.indexOf("/")}function addLeadingSlash(e){return hasLeadingSlash(e)?e:"/"+e}const invokeArrayFns=(e,t)=>{let n;for(let o=0;o<e.length;o++)n=e[o](t);return n};function once(e,t=null){let n;return(...o)=>(e&&(n=e.apply(t,o),e=null),n)}function getValueByDataPath(e,t){if(!isString(t))return;const n=(t=t.replace(/\[(\d+)\]/g,".$1")).split(".");let o=n[0];return e||(e={}),1===n.length?e[o]:getValueByDataPath(e[o],n.slice(1).join("."))}function sortObject(e){let t={};return isPlainObject(e)&&Object.keys(e).sort().forEach(n=>{const o=n;t[o]=e[o]}),Object.keys(t)?t:e}const customizeRE=/:/g;function customizeEvent(e){return camelize(e.replace(customizeRE,"-"))}const encode=encodeURIComponent;function stringifyQuery(e,t=encode){const n=e?Object.keys(e).map(n=>{let o=e[n];return void 0===typeof o||null===o?o="":isPlainObject(o)&&(o=JSON.stringify(o)),t(n)+"="+t(o)}).filter(e=>e.length>0).join("&"):null;return n?`?${n}`:""}const PAGE_HOOKS=[ON_INIT,ON_LOAD,ON_SHOW,ON_HIDE,ON_UNLOAD,ON_BACK_PRESS,ON_PAGE_SCROLL,ON_TAB_ITEM_TAP,ON_REACH_BOTTOM,ON_PULL_DOWN_REFRESH,ON_SHARE_TIMELINE,ON_SHARE_APP_MESSAGE,ON_SHARE_CHAT,ON_ADD_TO_FAVORITES,ON_SAVE_EXIT_STATE,ON_NAVIGATION_BAR_BUTTON_TAP,ON_NAVIGATION_BAR_SEARCH_INPUT_CLICKED,ON_NAVIGATION_BAR_SEARCH_INPUT_CHANGED,ON_NAVIGATION_BAR_SEARCH_INPUT_CONFIRMED,ON_NAVIGATION_BAR_SEARCH_INPUT_FOCUS_CHANGED];function isRootHook(e){return PAGE_HOOKS.indexOf(e)>-1}const UniLifecycleHooks=[ON_SHOW,ON_HIDE,ON_LAUNCH,ON_ERROR,ON_THEME_CHANGE,ON_PAGE_NOT_FOUND,ON_UNHANDLE_REJECTION,ON_EXIT,ON_INIT,ON_LOAD,ON_READY,ON_UNLOAD,ON_RESIZE,ON_BACK_PRESS,ON_PAGE_SCROLL,ON_TAB_ITEM_TAP,ON_REACH_BOTTOM,ON_PULL_DOWN_REFRESH,ON_SHARE_TIMELINE,ON_ADD_TO_FAVORITES,ON_SHARE_APP_MESSAGE,ON_SHARE_CHAT,ON_SAVE_EXIT_STATE,ON_NAVIGATION_BAR_BUTTON_TAP,ON_NAVIGATION_BAR_SEARCH_INPUT_CLICKED,ON_NAVIGATION_BAR_SEARCH_INPUT_CHANGED,ON_NAVIGATION_BAR_SEARCH_INPUT_CONFIRMED,ON_NAVIGATION_BAR_SEARCH_INPUT_FOCUS_CHANGED],MINI_PROGRAM_PAGE_RUNTIME_HOOKS=(()=>({onPageScroll:1,onShareAppMessage:2,onShareTimeline:4}))();function isUniLifecycleHook(e,t,n=!0){return!(n&&!isFunction(t))&&(UniLifecycleHooks.indexOf(e)>-1||0===e.indexOf("on"))}let vueApp;const createVueAppHooks=[];function onCreateVueApp(e){if(vueApp)return e(vueApp);createVueAppHooks.push(e)}function invokeCreateVueAppHook(e){vueApp=e,createVueAppHooks.forEach(t=>t(e))}const invokeCreateErrorHandler=once((e,t)=>{if(isFunction(e._component.onError))return t(e)}),E=function(){};E.prototype={_id:1,on:function(e,t,n){var o=this.e||(this.e={});return(o[e]||(o[e]=[])).push({fn:t,ctx:n,_id:this._id}),this._id++},once:function(e,t,n){var o=this;function r(){o.off(e,r),t.apply(n,arguments)}return r._=t,this.on(e,r,n)},emit:function(e){for(var t=[].slice.call(arguments,1),n=((this.e||(this.e={}))[e]||[]).slice(),o=0,r=n.length;o<r;o++)n[o].fn.apply(n[o].ctx,t);return this},off:function(e,t){var n=this.e||(this.e={}),o=n[e],r=[];if(o&&t){for(var i=o.length-1;i>=0;i--)if(o[i].fn===t||o[i].fn._===t||o[i]._id===t){o.splice(i,1);break}r=o}return r.length?n[e]=r:delete n[e],this}};var E$1=E;const LOCALE_ZH_HANS="zh-Hans",LOCALE_ZH_HANT="zh-Hant",LOCALE_EN="en",LOCALE_FR="fr",LOCALE_ES="es";function include(e,t){return!!t.find(t=>-1!==e.indexOf(t))}function startsWith(e,t){return t.find(t=>0===e.indexOf(t))}function normalizeLocale(e,t){if(!e)return;if("chinese"===(e=(e=e.trim().replace(/_/g,"-")).toLowerCase()))return LOCALE_ZH_HANS;if(0===e.indexOf("zh"))return e.indexOf("-hans")>-1?LOCALE_ZH_HANS:e.indexOf("-hant")>-1||include(e,["-tw","-hk","-mo","-cht"])?LOCALE_ZH_HANT:LOCALE_ZH_HANS;const n=startsWith(e,[LOCALE_EN,LOCALE_FR,LOCALE_ES]);return n||void 0}function getBaseSystemInfo(){return wx.getSystemInfoSync()}function tryCatch(e){return function(){try{return e.apply(e,arguments)}catch(t){console.error(t)}}}let invokeCallbackId=1;const invokeCallbacks={};function addInvokeCallback(e,t,n,o=!1){return invokeCallbacks[e]={name:t,keepAlive:o,callback:n},e}function invokeCallback(e,t,n){if("number"==typeof e){const o=invokeCallbacks[e];if(o)return o.keepAlive||delete invokeCallbacks[e],o.callback(t,n)}return t}const API_SUCCESS="success",API_FAIL="fail",API_COMPLETE="complete";function getApiCallbacks(e){const t={};for(const n in e){const o=e[n];isFunction(o)&&(t[n]=tryCatch(o),delete e[n])}return t}function normalizeErrMsg(e,t){return e&&-1!==e.indexOf(":fail")?t+e.substring(e.indexOf(":fail")):t+":ok"}function createAsyncApiCallback(e,t={},{beforeAll:n,beforeSuccess:o}={}){isPlainObject(t)||(t={});const{success:r,fail:i,complete:a}=getApiCallbacks(t),s=isFunction(r),c=isFunction(i),l=isFunction(a),u=invokeCallbackId++;return addInvokeCallback(u,e,u=>{(u=u||{}).errMsg=normalizeErrMsg(u.errMsg,e),isFunction(n)&&n(u),u.errMsg===e+":ok"?(isFunction(o)&&o(u,t),s&&r(u)):c&&i(u),l&&a(u)}),u}const HOOK_SUCCESS="success",HOOK_FAIL="fail",HOOK_COMPLETE="complete",globalInterceptors={},scopedInterceptors={};function wrapperHook(e,t){return function(n){return e(n,t)||n}}function queue$1(e,t,n){let o=!1;for(let r=0;r<e.length;r++){const i=e[r];if(o)o=Promise.resolve(wrapperHook(i,n));else{const e=i(t,n);if(isPromise(e)&&(o=Promise.resolve(e)),!1===e)return{then(){},catch(){}}}}return o||{then:e=>e(t),catch(){}}}function wrapperOptions(e,t={}){return[HOOK_SUCCESS,HOOK_FAIL,HOOK_COMPLETE].forEach(n=>{const o=e[n];if(!isArray(o))return;const r=t[n];t[n]=function(e){queue$1(o,e,t).then(e=>isFunction(r)&&r(e)||e)}}),t}function wrapperReturnValue(e,t){const n=[];isArray(globalInterceptors.returnValue)&&n.push(...globalInterceptors.returnValue);const o=scopedInterceptors[e];return o&&isArray(o.returnValue)&&n.push(...o.returnValue),n.forEach(e=>{t=e(t)||t}),t}function getApiInterceptorHooks(e){const t=Object.create(null);Object.keys(globalInterceptors).forEach(e=>{"returnValue"!==e&&(t[e]=globalInterceptors[e].slice())});const n=scopedInterceptors[e];return n&&Object.keys(n).forEach(e=>{"returnValue"!==e&&(t[e]=(t[e]||[]).concat(n[e]))}),t}function invokeApi(e,t,n,o){const r=getApiInterceptorHooks(e);if(r&&Object.keys(r).length){if(isArray(r.invoke)){return queue$1(r.invoke,n).then(n=>t(wrapperOptions(getApiInterceptorHooks(e),n),...o))}return t(wrapperOptions(r,n),...o)}return t(n,...o)}function hasCallback(e){return!(!isPlainObject(e)||![API_SUCCESS,API_FAIL,API_COMPLETE].find(t=>isFunction(e[t])))}function handlePromise(e){return e}function promisify$1(e,t){return(n={},...o)=>hasCallback(n)?wrapperReturnValue(e,invokeApi(e,t,n,o)):wrapperReturnValue(e,handlePromise(new Promise((r,i)=>{invokeApi(e,t,extend(n,{success:r,fail:i}),o)})))}function formatApiArgs(e,t){e[0]}function invokeSuccess(e,t,n){return invokeCallback(e,extend(n||{},{errMsg:t+":ok"}))}function invokeFail(e,t,n,o={}){const r=t+":fail";let i="";return i=n?0===n.indexOf(r)?n:r+" "+n:r,delete o.errCode,invokeCallback(e,extend({errMsg:i},o))}function beforeInvokeApi(e,t,n,o){const r=formatApiArgs(t);if(r)return r}function parseErrMsg(e){return!e||isString(e)?e:e.stack?("undefined"!=typeof globalThis&&globalThis.harmonyChannel||console.error(e.message+"\n"+e.stack),e.message):e}function wrapperTaskApi(e,t,n,o){return n=>{const r=createAsyncApiCallback(e,n,o),i=beforeInvokeApi(e,[n]);return i?invokeFail(r,e,i):t(n,{resolve:t=>invokeSuccess(r,e,t),reject:(t,n)=>invokeFail(r,e,parseErrMsg(t),n)})}}function wrapperSyncApi(e,t,n,o){return(...n)=>{const o=beforeInvokeApi(e,n);if(o)throw new Error(o);return t.apply(null,n)}}function wrapperAsyncApi(e,t,n,o){return wrapperTaskApi(e,t,n,o)}function defineSyncApi(e,t,n,o){return wrapperSyncApi(e,t)}function defineAsyncApi(e,t,n,o){return promisify$1(e,wrapperAsyncApi(e,t,void 0,o))}const API_UPX2PX="upx2px",EPS=1e-4,BASE_DEVICE_WIDTH=750;let isIOS=!1,deviceWidth=0,deviceDPR=0;function checkDeviceWidth(){const{platform:e,pixelRatio:t,windowWidth:n}=getBaseSystemInfo();deviceWidth=n,deviceDPR=t,isIOS="ios"===e}const upx2px=defineSyncApi(API_UPX2PX,(e,t)=>{if(0===deviceWidth&&checkDeviceWidth(),0===(e=Number(e)))return 0;let n=e/BASE_DEVICE_WIDTH*(t||deviceWidth);return n<0&&(n=-n),n=Math.floor(n+EPS),0===n&&(n=1!==deviceDPR&&isIOS?.5:1),e<0?-n:n}),API_ADD_INTERCEPTOR="addInterceptor",API_REMOVE_INTERCEPTOR="removeInterceptor";function mergeInterceptorHook(e,t){Object.keys(t).forEach(n=>{isFunction(t[n])&&(e[n]=mergeHook(e[n],t[n]))})}function removeInterceptorHook(e,t){e&&t&&Object.keys(t).forEach(n=>{const o=e[n],r=t[n];isArray(o)&&isFunction(r)&&remove(o,r)})}function mergeHook(e,t){const n=t?e?e.concat(t):isArray(t)?t:[t]:e;return n?dedupeHooks(n):n}function dedupeHooks(e){const t=[];for(let n=0;n<e.length;n++)-1===t.indexOf(e[n])&&t.push(e[n]);return t}const addInterceptor=defineSyncApi(API_ADD_INTERCEPTOR,(e,t)=>{isString(e)&&isPlainObject(t)?mergeInterceptorHook(scopedInterceptors[e]||(scopedInterceptors[e]={}),t):isPlainObject(e)&&mergeInterceptorHook(globalInterceptors,e)}),removeInterceptor=defineSyncApi(API_REMOVE_INTERCEPTOR,(e,t)=>{isString(e)?isPlainObject(t)?removeInterceptorHook(scopedInterceptors[e],t):delete scopedInterceptors[e]:isPlainObject(e)&&removeInterceptorHook(globalInterceptors,e)}),interceptors={},API_ON="$on",API_ONCE="$once",API_OFF="$off",API_EMIT="$emit";class EventBus{constructor(){this.$emitter=new E$1}on(e,t){return this.$emitter.on(e,t)}once(e,t){return this.$emitter.once(e,t)}off(e,t){e?this.$emitter.off(e,t):this.$emitter.e={}}emit(e,...t){this.$emitter.emit(e,...t)}}const eventBus=new EventBus,$on=defineSyncApi(API_ON,(e,t)=>(eventBus.on(e,t),()=>eventBus.off(e,t))),$once=defineSyncApi(API_ONCE,(e,t)=>(eventBus.once(e,t),()=>eventBus.off(e,t))),$off=defineSyncApi(API_OFF,(e,t)=>{isArray(e)||(e=e?[e]:[]),e.forEach(e=>eventBus.off(e,t))}),$emit=defineSyncApi(API_EMIT,(e,...t)=>{eventBus.emit(e,...t)});let cid,cidErrMsg,enabled;function normalizePushMessage(e){try{return JSON.parse(e)}catch(t){}return e}function invokePushCallback(e){if("enabled"===e.type)enabled=!0;else if("clientId"===e.type)cid=e.cid,cidErrMsg=e.errMsg,invokeGetPushCidCallbacks(cid,e.errMsg);else if("pushMsg"===e.type){const t={type:"receive",data:normalizePushMessage(e.message)};for(let e=0;e<onPushMessageCallbacks.length;e++){if((0,onPushMessageCallbacks[e])(t),t.stopped)break}}else"click"===e.type&&onPushMessageCallbacks.forEach(t=>{t({type:"click",data:normalizePushMessage(e.message)})})}const getPushCidCallbacks=[];function invokeGetPushCidCallbacks(e,t){getPushCidCallbacks.forEach(n=>{n(e,t)}),getPushCidCallbacks.length=0}const API_GET_PUSH_CLIENT_ID="getPushClientId",getPushClientId=defineAsyncApi(API_GET_PUSH_CLIENT_ID,(e,{resolve:t,reject:n})=>{Promise.resolve().then(()=>{void 0===enabled&&(enabled=!1,cid="",cidErrMsg="uniPush is not enabled"),getPushCidCallbacks.push((e,o)=>{e?t({cid:e}):n(o)}),void 0!==cid&&invokeGetPushCidCallbacks(cid,cidErrMsg)})}),onPushMessageCallbacks=[],onPushMessage=e=>{-1===onPushMessageCallbacks.indexOf(e)&&onPushMessageCallbacks.push(e)},offPushMessage=e=>{if(e){const t=onPushMessageCallbacks.indexOf(e);t>-1&&onPushMessageCallbacks.splice(t,1)}else onPushMessageCallbacks.length=0},SYNC_API_RE=/^\$|getLocale|setLocale|sendNativeEvent|restoreGlobal|requireGlobal|getCurrentSubNVue|getMenuButtonBoundingClientRect|^report|interceptors|Interceptor$|getSubNVueById|requireNativePlugin|upx2px|hideKeyboard|canIUse|^create|Sync$|Manager$|base64ToArrayBuffer|arrayBufferToBase64|getDeviceInfo|getAppBaseInfo|getWindowInfo|getSystemSetting|getAppAuthorizeSetting/,CONTEXT_API_RE=/^create|Manager$/,CONTEXT_API_RE_EXC=["createBLEConnection"],ASYNC_API=["createBLEConnection"],CALLBACK_API_RE=/^on|^off/;function isContextApi(e){return CONTEXT_API_RE.test(e)&&-1===CONTEXT_API_RE_EXC.indexOf(e)}function isSyncApi(e){return SYNC_API_RE.test(e)&&-1===ASYNC_API.indexOf(e)}function isCallbackApi(e){return CALLBACK_API_RE.test(e)&&"onPush"!==e}function shouldPromise(e){return!(isContextApi(e)||isSyncApi(e)||isCallbackApi(e))}function promisify(e,t){return shouldPromise(e)&&isFunction(t)?function(n={},...o){return isFunction(n.success)||isFunction(n.fail)||isFunction(n.complete)?wrapperReturnValue(e,invokeApi(e,t,n,o)):wrapperReturnValue(e,handlePromise(new Promise((r,i)=>{invokeApi(e,t,extend({},n,{success:r,fail:i}),o)})))}:t}Promise.prototype.finally||(Promise.prototype.finally=function(e){const t=this.constructor;return this.then(n=>t.resolve(e&&e()).then(()=>n),n=>t.resolve(e&&e()).then(()=>{throw n}))});const CALLBACKS=["success","fail","cancel","complete"];function initWrapper(e){function t(e,t,n){return function(r){return t(o(e,r,n))}}function n(e,n,o={},r={},i=!1){if(isPlainObject(n)){const a=!0===i?n:{};isFunction(o)&&(o=o(n,a)||{});for(const s in n)if(hasOwn(o,s)){let t=o[s];isFunction(t)&&(t=t(n[s],n,a)),t?isString(t)?a[t]=n[s]:isPlainObject(t)&&(a[t.name?t.name:s]=t.value):console.warn(`微信小程序 ${e} 暂不支持 ${s}`)}else if(-1!==CALLBACKS.indexOf(s)){const o=n[s];isFunction(o)&&(a[s]=t(e,o,r))}else i||hasOwn(a,s)||(a[s]=n[s]);return a}return isFunction(n)&&(n=t(e,n,r)),n}function o(t,o,r,i=!1){return isFunction(e.returnValue)&&(o=e.returnValue(t,o)),n(t,o,r,{},i)}return function(t,r){if(!hasOwn(e,t))return r;const i=e[t];return i?function(e,r){let a=i;isFunction(i)&&(a=i(e));const s=[e=n(t,e,a.args,a.returnValue)];void 0!==r&&s.push(r);const c=wx[a.name||t].apply(wx,s);return isSyncApi(t)?o(t,c,a.returnValue,isContextApi(t)):c}:function(){console.error(`微信小程序 暂不支持${t}`)}}}const getLocale=()=>{const e=isFunction(getApp)&&getApp({allowDefault:!0});return e&&e.$vm?e.$vm.$locale:normalizeLocale(wx.getSystemInfoSync().language)||LOCALE_EN},setLocale=e=>{const t=isFunction(getApp)&&getApp();if(!t)return!1;return t.$vm.$locale!==e&&(t.$vm.$locale=e,onLocaleChangeCallbacks.forEach(t=>t({locale:e})),!0)},onLocaleChangeCallbacks=[],onLocaleChange=e=>{-1===onLocaleChangeCallbacks.indexOf(e)&&onLocaleChangeCallbacks.push(e)};"undefined"!=typeof global&&(global.getLocale=getLocale);const UUID_KEY="__DC_STAT_UUID";let deviceId;function useDeviceId(e=wx){return function(t,n){deviceId=deviceId||e.getStorageSync(UUID_KEY),deviceId||(deviceId=Date.now()+""+Math.floor(1e7*Math.random()),wx.setStorage({key:UUID_KEY,data:deviceId})),n.deviceId=deviceId}}function addSafeAreaInsets(e,t){if(e.safeArea){const n=e.safeArea;t.safeAreaInsets={top:n.top,left:n.left,right:e.windowWidth-n.right,bottom:e.screenHeight-n.bottom}}}function getOSInfo(e,t){let n="",o="";return n=e.split(" ")[0]||"",o=e.split(" ")[1]||"",{osName:n.toLocaleLowerCase(),osVersion:o}}function populateParameters(e,t){const{brand:n="",model:o="",system:r="",language:i="",theme:a,version:s,platform:c,fontSizeSetting:l,SDKVersion:u,pixelRatio:p,deviceOrientation:d}=e,{osName:f,osVersion:h}=getOSInfo(r,c);let m=s,g=getGetDeviceType(e,o),v=getDeviceBrand(n),y=getHostName(e),x=d,b=p,_=u;const k=i.replace(/_/g,"-"),C={appId:"",appName:"视语翻译",appVersion:"1.0.0",appVersionCode:"100",appLanguage:getAppLanguage(k),uniCompileVersion:"4.36",uniCompilerVersion:"4.36",uniRuntimeVersion:"4.36",uniPlatform:"mp-weixin",deviceBrand:v,deviceModel:o,deviceType:g,devicePixelRatio:b,deviceOrientation:x,osName:f,osVersion:h,hostTheme:a,hostVersion:m,hostLanguage:k,hostName:y,hostSDKVersion:_,hostFontSizeSetting:l,windowTop:0,windowBottom:0,osLanguage:void 0,osTheme:void 0,ua:void 0,hostPackageName:void 0,browserName:void 0,browserVersion:void 0,isUniAppX:!1};extend(t,C)}function getGetDeviceType(e,t){let n=e.deviceType||"phone";{const e={ipad:"pad",windows:"pc",mac:"pc"},o=Object.keys(e),r=t.toLocaleLowerCase();for(let t=0;t<o.length;t++){const i=o[t];if(-1!==r.indexOf(i)){n=e[i];break}}}return n}function getDeviceBrand(e){let t=e;return t&&(t=t.toLocaleLowerCase()),t}function getAppLanguage(e){return getLocale?getLocale():e}function getHostName(e){let t=e.hostName||"WeChat";return e.environment?t=e.environment:e.host&&e.host.env&&(t=e.host.env),t}const getSystemInfo={returnValue:(e,t)=>{addSafeAreaInsets(e,t),useDeviceId()(e,t),populateParameters(e,t)}},getSystemInfoSync=getSystemInfo,redirectTo={},previewImage={args(e,t){let n=parseInt(e.current);if(isNaN(n))return;const o=e.urls;if(!isArray(o))return;const r=o.length;return r?(n<0?n=0:n>=r&&(n=r-1),n>0?(t.current=o[n],t.urls=o.filter((e,t)=>!(t<n)||e!==o[n])):t.current=o[0],{indicator:!1,loop:!1}):void 0}},showActionSheet={args(e,t){t.alertText=e.title}},getDeviceInfo={returnValue:(e,t)=>{const{brand:n,model:o,system:r="",platform:i=""}=e;let a=getGetDeviceType(e,o),s=getDeviceBrand(n);useDeviceId()(e,t);const{osName:c,osVersion:l}=getOSInfo(r,i);t=sortObject(extend(t,{deviceType:a,deviceBrand:s,deviceModel:o,osName:c,osVersion:l}))}},getAppBaseInfo={returnValue:(e,t)=>{const{version:n,language:o,SDKVersion:r,theme:i}=e;let a=getHostName(e),s=o.replace(/_/g,"-");t=sortObject(extend(t,{hostVersion:n,hostLanguage:s,hostName:a,hostSDKVersion:r,hostTheme:i,appId:"",appName:"视语翻译",appVersion:"1.0.0",appVersionCode:"100",appLanguage:getAppLanguage(s),isUniAppX:!1,uniPlatform:"mp-weixin",uniCompileVersion:"4.36",uniCompilerVersion:"4.36",uniRuntimeVersion:"4.36"}))}},getWindowInfo={returnValue:(e,t)=>{addSafeAreaInsets(e,t),t=sortObject(extend(t,{windowTop:0,windowBottom:0}))}},getAppAuthorizeSetting={returnValue:function(e,t){const{locationReducedAccuracy:n}=e;t.locationAccuracy="unsupported",!0===n?t.locationAccuracy="reduced":!1===n&&(t.locationAccuracy="full")}},baseApis={$on:$on,$off:$off,$once:$once,$emit:$emit,upx2px:upx2px,interceptors:interceptors,addInterceptor:addInterceptor,removeInterceptor:removeInterceptor,onCreateVueApp:onCreateVueApp,invokeCreateVueAppHook:invokeCreateVueAppHook,getLocale:getLocale,setLocale:setLocale,onLocaleChange:onLocaleChange,getPushClientId:getPushClientId,onPushMessage:onPushMessage,offPushMessage:offPushMessage,invokePushCallback:invokePushCallback};function initUni(e,t,n=wx){const o=initWrapper(t);return new Proxy({},{get:(t,r)=>hasOwn(t,r)?t[r]:hasOwn(e,r)?promisify(r,e[r]):hasOwn(baseApis,r)?promisify(r,baseApis[r]):promisify(r,o(r,n[r]))})}function initGetProvider(e){return function({service:t,success:n,fail:o,complete:r}){let i;e[t]?(i={errMsg:"getProvider:ok",service:t,provider:e[t]},isFunction(n)&&n(i)):(i={errMsg:"getProvider:fail:服务["+t+"]不存在"},isFunction(o)&&o(i)),isFunction(r)&&r(i)}}const objectKeys=["qy","env","error","version","lanDebug","cloud","serviceMarket","router","worklet","__webpack_require_UNI_MP_PLUGIN__"],singlePageDisableKey=["lanDebug","router","worklet"],launchOption=wx.getLaunchOptionsSync?wx.getLaunchOptionsSync():null;function isWxKey(e){return(!launchOption||1154!==launchOption.scene||!singlePageDisableKey.includes(e))&&(objectKeys.indexOf(e)>-1||"function"==typeof wx[e])}function initWx(){const e={};for(const t in wx)isWxKey(t)&&(e[t]=wx[t]);return"undefined"!=typeof globalThis&&"undefined"==typeof requireMiniProgram&&(globalThis.wx=e),e}const mocks$1=["__route__","__wxExparserNodeId__","__wxWebviewId__"],getProvider=initGetProvider({oauth:["weixin"],share:["weixin"],payment:["wxpay"],push:["weixin"]});function initComponentMocks(e){const t=Object.create(null);return mocks$1.forEach(n=>{t[n]=e[n]}),t}function createSelectorQuery(){const e=wx$2.createSelectorQuery(),t=e.in;return e.in=function(e){return t.call(this,initComponentMocks(e))},e}const wx$2=initWx();let baseInfo=wx$2.getAppBaseInfo&&wx$2.getAppBaseInfo();baseInfo||(baseInfo=wx$2.getSystemInfoSync());const host=baseInfo?baseInfo.host:null,shareVideoMessage=host&&"SAAASDK"===host.env?wx$2.miniapp.shareVideoMessage:wx$2.shareVideoMessage;var shims=Object.freeze({__proto__:null,createSelectorQuery:createSelectorQuery,getProvider:getProvider,shareVideoMessage:shareVideoMessage});const compressImage={args(e,t){e.compressedHeight&&!t.compressHeight&&(t.compressHeight=e.compressedHeight),e.compressedWidth&&!t.compressWidth&&(t.compressWidth=e.compressedWidth)}};var protocols=Object.freeze({__proto__:null,compressImage:compressImage,getAppAuthorizeSetting:getAppAuthorizeSetting,getAppBaseInfo:getAppBaseInfo,getDeviceInfo:getDeviceInfo,getSystemInfo:getSystemInfo,getSystemInfoSync:getSystemInfoSync,getWindowInfo:getWindowInfo,previewImage:previewImage,redirectTo:redirectTo,showActionSheet:showActionSheet});const wx$1=initWx();var index=initUni(shims,protocols,wx$1);new Set(Object.getOwnPropertyNames(Symbol).filter(e=>"arguments"!==e&&"caller"!==e).map(e=>Symbol[e]).filter(isSymbol));{const e=getGlobalThis(),t=(t,n)=>{let o;return(o=e[t])||(o=e[t]=[]),o.push(n),e=>{o.length>1?o.forEach(t=>t(e)):o[0](e)}};t("__VUE_INSTANCE_SETTERS__",e=>e),t("__VUE_SSR_SETTERS__",e=>e)}let activeEffectScope,activeEffect;class EffectScope{constructor(e=!1){this.detached=e,this._active=!0,this.effects=[],this.cleanups=[],this.parent=activeEffectScope,!e&&activeEffectScope&&(this.index=(activeEffectScope.scopes||(activeEffectScope.scopes=[])).push(this)-1)}get active(){return this._active}run(e){if(this._active){const t=activeEffectScope;try{return activeEffectScope=this,e()}finally{activeEffectScope=t}}}on(){activeEffectScope=this}off(){activeEffectScope=this.parent}stop(e){if(this._active){let t,n;for(t=0,n=this.effects.length;t<n;t++)this.effects[t].stop();for(t=0,n=this.cleanups.length;t<n;t++)this.cleanups[t]();if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].stop(!0);if(!this.detached&&this.parent&&!e){const e=this.parent.scopes.pop();e&&e!==this&&(this.parent.scopes[this.index]=e,e.index=this.index)}this.parent=void 0,this._active=!1}}}function recordEffectScope(e,t=activeEffectScope){t&&t.active&&t.effects.push(e)}function getCurrentScope(){return activeEffectScope}class ReactiveEffect2{constructor(e,t,n,o){this.fn=e,this.trigger=t,this.scheduler=n,this.active=!0,this.deps=[],this._dirtyLevel=4,this._trackId=0,this._runnings=0,this._shouldSchedule=!1,this._depsLength=0,recordEffectScope(this,o)}get dirty(){if(2===this._dirtyLevel||3===this._dirtyLevel){this._dirtyLevel=1,pauseTracking();for(let e=0;e<this._depsLength;e++){const t=this.deps[e];if(t.computed&&(triggerComputed(t.computed),this._dirtyLevel>=4))break}1===this._dirtyLevel&&(this._dirtyLevel=0),resetTracking()}return this._dirtyLevel>=4}set dirty(e){this._dirtyLevel=e?4:0}run(){if(this._dirtyLevel=0,!this.active)return this.fn();let e=shouldTrack,t=activeEffect;try{return shouldTrack=!0,activeEffect=this,this._runnings++,preCleanupEffect(this),this.fn()}finally{postCleanupEffect(this),this._runnings--,activeEffect=t,shouldTrack=e}}stop(){var e;this.active&&(preCleanupEffect(this),postCleanupEffect(this),null==(e=this.onStop)||e.call(this),this.active=!1)}}function triggerComputed(e){return e.value}function preCleanupEffect(e){e._trackId++,e._depsLength=0}function postCleanupEffect(e){if(e.deps.length>e._depsLength){for(let t=e._depsLength;t<e.deps.length;t++)cleanupDepEffect(e.deps[t],e);e.deps.length=e._depsLength}}function cleanupDepEffect(e,t){const n=e.get(t);void 0!==n&&t._trackId!==n&&(e.delete(t),0===e.size&&e.cleanup())}let shouldTrack=!0,pauseScheduleStack=0;const trackStack=[];function pauseTracking(){trackStack.push(shouldTrack),shouldTrack=!1}function resetTracking(){const e=trackStack.pop();shouldTrack=void 0===e||e}function pauseScheduling(){pauseScheduleStack++}function resetScheduling(){for(pauseScheduleStack--;!pauseScheduleStack&&queueEffectSchedulers.length;)queueEffectSchedulers.shift()()}function trackEffect(e,t,n){if(t.get(e)!==e._trackId){t.set(e,e._trackId);const n=e.deps[e._depsLength];n!==t?(n&&cleanupDepEffect(n,e),e.deps[e._depsLength++]=t):e._depsLength++}}const queueEffectSchedulers=[];function triggerEffects(e,t,n){pauseScheduling();for(const o of e.keys()){let n;o._dirtyLevel<t&&(null!=n?n:n=e.get(o)===o._trackId)&&(o._shouldSchedule||(o._shouldSchedule=0===o._dirtyLevel),o._dirtyLevel=t),o._shouldSchedule&&(null!=n?n:n=e.get(o)===o._trackId)&&(o.trigger(),o._runnings&&!o.allowRecurse||2===o._dirtyLevel||(o._shouldSchedule=!1,o.scheduler&&queueEffectSchedulers.push(o.scheduler)))}resetScheduling()}const createDep=(e,t)=>{const n=new Map;return n.cleanup=e,n.computed=t,n},targetMap=new WeakMap,ITERATE_KEY=Symbol(""),MAP_KEY_ITERATE_KEY=Symbol("");function track(e,t,n){if(shouldTrack&&activeEffect){let t=targetMap.get(e);t||targetMap.set(e,t=new Map);let o=t.get(n);o||t.set(n,o=createDep(()=>t.delete(n))),trackEffect(activeEffect,o)}}function trigger(e,t,n,o,r,i){const a=targetMap.get(e);if(!a)return;let s=[];if("clear"===t)s=[...a.values()];else if("length"===n&&isArray(e)){const e=Number(o);a.forEach((t,n)=>{("length"===n||!isSymbol(n)&&n>=e)&&s.push(t)})}else switch(void 0!==n&&s.push(a.get(n)),t){case"add":isArray(e)?isIntegerKey(n)&&s.push(a.get("length")):(s.push(a.get(ITERATE_KEY)),isMap(e)&&s.push(a.get(MAP_KEY_ITERATE_KEY)));break;case"delete":isArray(e)||(s.push(a.get(ITERATE_KEY)),isMap(e)&&s.push(a.get(MAP_KEY_ITERATE_KEY)));break;case"set":isMap(e)&&s.push(a.get(ITERATE_KEY))}pauseScheduling();for(const c of s)c&&triggerEffects(c,4);resetScheduling()}const isNonTrackableKeys=makeMap("__proto__,__v_isRef,__isVue"),builtInSymbols=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>"arguments"!==e&&"caller"!==e).map(e=>Symbol[e]).filter(isSymbol)),arrayInstrumentations=createArrayInstrumentations();function createArrayInstrumentations(){const e={};return["includes","indexOf","lastIndexOf"].forEach(t=>{e[t]=function(...e){const n=toRaw(this);for(let t=0,r=this.length;t<r;t++)track(n,"get",t+"");const o=n[t](...e);return-1===o||!1===o?n[t](...e.map(toRaw)):o}}),["push","pop","shift","unshift","splice"].forEach(t=>{e[t]=function(...e){pauseTracking(),pauseScheduling();const n=toRaw(this)[t].apply(this,e);return resetScheduling(),resetTracking(),n}}),e}function hasOwnProperty(e){const t=toRaw(this);return track(t,"has",e),t.hasOwnProperty(e)}class BaseReactiveHandler2{constructor(e=!1,t=!1){this._isReadonly=e,this._isShallow=t}get(e,t,n){const o=this._isReadonly,r=this._isShallow;if("__v_isReactive"===t)return!o;if("__v_isReadonly"===t)return o;if("__v_isShallow"===t)return r;if("__v_raw"===t)return n===(o?r?shallowReadonlyMap:readonlyMap:r?shallowReactiveMap:reactiveMap).get(e)||Object.getPrototypeOf(e)===Object.getPrototypeOf(n)?e:void 0;const i=isArray(e);if(!o){if(i&&hasOwn(arrayInstrumentations,t))return Reflect.get(arrayInstrumentations,t,n);if("hasOwnProperty"===t)return hasOwnProperty}const a=Reflect.get(e,t,n);return(isSymbol(t)?builtInSymbols.has(t):isNonTrackableKeys(t))?a:(o||track(e,"get",t),r?a:isRef(a)?i&&isIntegerKey(t)?a:a.value:isObject(a)?o?readonly(a):reactive(a):a)}}class MutableReactiveHandler2 extends BaseReactiveHandler2{constructor(e=!1){super(!1,e)}set(e,t,n,o){let r=e[t];if(!this._isShallow){const t=isReadonly(r);if(isShallow(n)||isReadonly(n)||(r=toRaw(r),n=toRaw(n)),!isArray(e)&&isRef(r)&&!isRef(n))return!t&&(r.value=n,!0)}const i=isArray(e)&&isIntegerKey(t)?Number(t)<e.length:hasOwn(e,t),a=Reflect.set(e,t,n,o);return e===toRaw(o)&&(i?hasChanged(n,r)&&trigger(e,"set",t,n):trigger(e,"add",t,n)),a}deleteProperty(e,t){const n=hasOwn(e,t);e[t];const o=Reflect.deleteProperty(e,t);return o&&n&&trigger(e,"delete",t,void 0),o}has(e,t){const n=Reflect.has(e,t);return isSymbol(t)&&builtInSymbols.has(t)||track(e,"has",t),n}ownKeys(e){return track(e,"iterate",isArray(e)?"length":ITERATE_KEY),Reflect.ownKeys(e)}}class ReadonlyReactiveHandler2 extends BaseReactiveHandler2{constructor(e=!1){super(!0,e)}set(e,t){return!0}deleteProperty(e,t){return!0}}const mutableHandlers=new MutableReactiveHandler2,readonlyHandlers=new ReadonlyReactiveHandler2,shallowReactiveHandlers=new MutableReactiveHandler2(!0),toShallow=e=>e,getProto=e=>Reflect.getPrototypeOf(e);function get(e,t,n=!1,o=!1){const r=toRaw(e=e.__v_raw),i=toRaw(t);n||(hasChanged(t,i)&&track(r,"get",t),track(r,"get",i));const{has:a}=getProto(r),s=o?toShallow:n?toReadonly:toReactive;return a.call(r,t)?s(e.get(t)):a.call(r,i)?s(e.get(i)):void(e!==r&&e.get(t))}function has(e,t=!1){const n=this.__v_raw,o=toRaw(n),r=toRaw(e);return t||(hasChanged(e,r)&&track(o,"has",e),track(o,"has",r)),e===r?n.has(e):n.has(e)||n.has(r)}function size(e,t=!1){return e=e.__v_raw,!t&&track(toRaw(e),"iterate",ITERATE_KEY),Reflect.get(e,"size",e)}function add(e){e=toRaw(e);const t=toRaw(this);return getProto(t).has.call(t,e)||(t.add(e),trigger(t,"add",e,e)),this}function set$1(e,t){t=toRaw(t);const n=toRaw(this),{has:o,get:r}=getProto(n);let i=o.call(n,e);i||(e=toRaw(e),i=o.call(n,e));const a=r.call(n,e);return n.set(e,t),i?hasChanged(t,a)&&trigger(n,"set",e,t):trigger(n,"add",e,t),this}function deleteEntry(e){const t=toRaw(this),{has:n,get:o}=getProto(t);let r=n.call(t,e);r||(e=toRaw(e),r=n.call(t,e)),o&&o.call(t,e);const i=t.delete(e);return r&&trigger(t,"delete",e,void 0),i}function clear(){const e=toRaw(this),t=0!==e.size,n=e.clear();return t&&trigger(e,"clear",void 0,void 0),n}function createForEach(e,t){return function(n,o){const r=this,i=r.__v_raw,a=toRaw(i),s=t?toShallow:e?toReadonly:toReactive;return!e&&track(a,"iterate",ITERATE_KEY),i.forEach((e,t)=>n.call(o,s(e),s(t),r))}}function createIterableMethod(e,t,n){return function(...o){const r=this.__v_raw,i=toRaw(r),a=isMap(i),s="entries"===e||e===Symbol.iterator&&a,c="keys"===e&&a,l=r[e](...o),u=n?toShallow:t?toReadonly:toReactive;return!t&&track(i,"iterate",c?MAP_KEY_ITERATE_KEY:ITERATE_KEY),{next(){const{value:e,done:t}=l.next();return t?{value:e,done:t}:{value:s?[u(e[0]),u(e[1])]:u(e),done:t}},[Symbol.iterator](){return this}}}}function createReadonlyMethod(e){return function(...t){return"delete"!==e&&("clear"===e?void 0:this)}}function createInstrumentations(){const e={get(e){return get(this,e)},get size(){return size(this)},has:has,add:add,set:set$1,delete:deleteEntry,clear:clear,forEach:createForEach(!1,!1)},t={get(e){return get(this,e,!1,!0)},get size(){return size(this)},has:has,add:add,set:set$1,delete:deleteEntry,clear:clear,forEach:createForEach(!1,!0)},n={get(e){return get(this,e,!0)},get size(){return size(this,!0)},has(e){return has.call(this,e,!0)},add:createReadonlyMethod("add"),set:createReadonlyMethod("set"),delete:createReadonlyMethod("delete"),clear:createReadonlyMethod("clear"),forEach:createForEach(!0,!1)},o={get(e){return get(this,e,!0,!0)},get size(){return size(this,!0)},has(e){return has.call(this,e,!0)},add:createReadonlyMethod("add"),set:createReadonlyMethod("set"),delete:createReadonlyMethod("delete"),clear:createReadonlyMethod("clear"),forEach:createForEach(!0,!0)};return["keys","values","entries",Symbol.iterator].forEach(r=>{e[r]=createIterableMethod(r,!1,!1),n[r]=createIterableMethod(r,!0,!1),t[r]=createIterableMethod(r,!1,!0),o[r]=createIterableMethod(r,!0,!0)}),[e,n,t,o]}const[mutableInstrumentations,readonlyInstrumentations,shallowInstrumentations,shallowReadonlyInstrumentations]=createInstrumentations();function createInstrumentationGetter(e,t){const n=t?e?shallowReadonlyInstrumentations:shallowInstrumentations:e?readonlyInstrumentations:mutableInstrumentations;return(t,o,r)=>"__v_isReactive"===o?!e:"__v_isReadonly"===o?e:"__v_raw"===o?t:Reflect.get(hasOwn(n,o)&&o in t?n:t,o,r)}const mutableCollectionHandlers={get:createInstrumentationGetter(!1,!1)},shallowCollectionHandlers={get:createInstrumentationGetter(!1,!0)},readonlyCollectionHandlers={get:createInstrumentationGetter(!0,!1)},reactiveMap=new WeakMap,shallowReactiveMap=new WeakMap,readonlyMap=new WeakMap,shallowReadonlyMap=new WeakMap;function targetTypeMap(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function getTargetType(e){return e.__v_skip||!Object.isExtensible(e)?0:targetTypeMap(toRawType(e))}function reactive(e){return isReadonly(e)?e:createReactiveObject(e,!1,mutableHandlers,mutableCollectionHandlers,reactiveMap)}function shallowReactive(e){return createReactiveObject(e,!1,shallowReactiveHandlers,shallowCollectionHandlers,shallowReactiveMap)}function readonly(e){return createReactiveObject(e,!0,readonlyHandlers,readonlyCollectionHandlers,readonlyMap)}function createReactiveObject(e,t,n,o,r){if(!isObject(e))return e;if(e.__v_raw&&(!t||!e.__v_isReactive))return e;const i=r.get(e);if(i)return i;const a=getTargetType(e);if(0===a)return e;const s=new Proxy(e,2===a?o:n);return r.set(e,s),s}function isReactive(e){return isReadonly(e)?isReactive(e.__v_raw):!(!e||!e.__v_isReactive)}function isReadonly(e){return!(!e||!e.__v_isReadonly)}function isShallow(e){return!(!e||!e.__v_isShallow)}function toRaw(e){const t=e&&e.__v_raw;return t?toRaw(t):e}function markRaw(e){return Object.isExtensible(e)&&def(e,"__v_skip",!0),e}const toReactive=e=>isObject(e)?reactive(e):e,toReadonly=e=>isObject(e)?readonly(e):e;class ComputedRefImpl{constructor(e,t,n,o){this.getter=e,this._setter=t,this.dep=void 0,this.__v_isRef=!0,this.__v_isReadonly=!1,this.effect=new ReactiveEffect2(()=>e(this._value),()=>triggerRefValue(this,2===this.effect._dirtyLevel?2:3)),this.effect.computed=this,this.effect.active=this._cacheable=!o,this.__v_isReadonly=n}get value(){const e=toRaw(this);return e._cacheable&&!e.effect.dirty||!hasChanged(e._value,e._value=e.effect.run())||triggerRefValue(e,4),trackRefValue(e),e.effect._dirtyLevel>=2&&triggerRefValue(e,2),e._value}set value(e){this._setter(e)}get _dirty(){return this.effect.dirty}set _dirty(e){this.effect.dirty=e}}function computed$1(e,t,n=!1){let o,r;const i=isFunction(e);i?(o=e,r=NOOP):(o=e.get,r=e.set);return new ComputedRefImpl(o,r,i||!r,n)}function trackRefValue(e){var t;shouldTrack&&activeEffect&&(e=toRaw(e),trackEffect(activeEffect,null!=(t=e.dep)?t:e.dep=createDep(()=>e.dep=void 0,e instanceof ComputedRefImpl?e:void 0)))}function triggerRefValue(e,t=4,n){const o=(e=toRaw(e)).dep;o&&triggerEffects(o,t)}function isRef(e){return!(!e||!0!==e.__v_isRef)}function ref(e){return createRef(e,!1)}function createRef(e,t){return isRef(e)?e:new RefImpl(e,t)}class RefImpl{constructor(e,t){this.__v_isShallow=t,this.dep=void 0,this.__v_isRef=!0,this._rawValue=t?e:toRaw(e),this._value=t?e:toReactive(e)}get value(){return trackRefValue(this),this._value}set value(e){const t=this.__v_isShallow||isShallow(e)||isReadonly(e);e=t?e:toRaw(e),hasChanged(e,this._rawValue)&&(this._rawValue=e,this._value=t?e:toReactive(e),triggerRefValue(this,4))}}function unref(e){return isRef(e)?e.value:e}const shallowUnwrapHandlers={get:(e,t,n)=>unref(Reflect.get(e,t,n)),set:(e,t,n,o)=>{const r=e[t];return isRef(r)&&!isRef(n)?(r.value=n,!0):Reflect.set(e,t,n,o)}};function proxyRefs(e){return isReactive(e)?e:new Proxy(e,shallowUnwrapHandlers)}function callWithErrorHandling(e,t,n,o){try{return o?e(...o):e()}catch(r){handleError(r,t,n)}}function callWithAsyncErrorHandling(e,t,n,o){if(isFunction(e)){const r=callWithErrorHandling(e,t,n,o);return r&&isPromise(r)&&r.catch(e=>{handleError(e,t,n)}),r}const r=[];for(let i=0;i<e.length;i++)r.push(callWithAsyncErrorHandling(e[i],t,n,o));return r}function handleError(e,t,n,o=!0){const r=t?t.vnode:null;if(t){let o=t.parent;const r=t.proxy,i=`https://vuejs.org/error-reference/#runtime-${n}`;for(;o;){const t=o.ec;if(t)for(let n=0;n<t.length;n++)if(!1===t[n](e,r,i))return;o=o.parent}const a=t.appContext.config.errorHandler;if(a)return void callWithErrorHandling(a,null,10,[e,r,i])}logError(e,n,r,o)}function logError(e,t,n,o=!0){console.error(e)}let isFlushing=!1,isFlushPending=!1;const queue=[];let flushIndex=0;const pendingPostFlushCbs=[];let activePostFlushCbs=null,postFlushIndex=0;const resolvedPromise=Promise.resolve();let currentFlushPromise=null;function nextTick$1(e){const t=currentFlushPromise||resolvedPromise;return e?t.then(this?e.bind(this):e):t}function findInsertionIndex(e){let t=flushIndex+1,n=queue.length;for(;t<n;){const o=t+n>>>1,r=queue[o],i=getId(r);i<e||i===e&&r.pre?t=o+1:n=o}return t}function queueJob(e){queue.length&&queue.includes(e,isFlushing&&e.allowRecurse?flushIndex+1:flushIndex)||(null==e.id?queue.push(e):queue.splice(findInsertionIndex(e.id),0,e),queueFlush())}function queueFlush(){isFlushing||isFlushPending||(isFlushPending=!0,currentFlushPromise=resolvedPromise.then(flushJobs))}function hasQueueJob(e){return queue.indexOf(e)>-1}function invalidateJob(e){const t=queue.indexOf(e);t>flushIndex&&queue.splice(t,1)}function queuePostFlushCb(e){isArray(e)?pendingPostFlushCbs.push(...e):activePostFlushCbs&&activePostFlushCbs.includes(e,e.allowRecurse?postFlushIndex+1:postFlushIndex)||pendingPostFlushCbs.push(e),queueFlush()}function flushPreFlushCbs(e,t,n=(isFlushing?flushIndex+1:0)){for(;n<queue.length;n++){const e=queue[n];e&&e.pre&&(queue.splice(n,1),n--,e())}}function flushPostFlushCbs(e){if(pendingPostFlushCbs.length){const e=[...new Set(pendingPostFlushCbs)].sort((e,t)=>getId(e)-getId(t));if(pendingPostFlushCbs.length=0,activePostFlushCbs)return void activePostFlushCbs.push(...e);for(activePostFlushCbs=e,postFlushIndex=0;postFlushIndex<activePostFlushCbs.length;postFlushIndex++)activePostFlushCbs[postFlushIndex]();activePostFlushCbs=null,postFlushIndex=0}}const getId=e=>null==e.id?1/0:e.id,comparator=(e,t)=>{const n=getId(e)-getId(t);if(0===n){if(e.pre&&!t.pre)return-1;if(t.pre&&!e.pre)return 1}return n};function flushJobs(e){isFlushPending=!1,isFlushing=!0,queue.sort(comparator);try{for(flushIndex=0;flushIndex<queue.length;flushIndex++){const e=queue[flushIndex];e&&!1!==e.active&&callWithErrorHandling(e,null,14)}}finally{flushIndex=0,queue.length=0,flushPostFlushCbs(),isFlushing=!1,currentFlushPromise=null,(queue.length||pendingPostFlushCbs.length)&&flushJobs()}}function emit(e,t,...n){if(e.isUnmounted)return;const o=e.vnode.props||EMPTY_OBJ;let r=n;const i=t.startsWith("update:"),a=i&&t.slice(7);if(a&&a in o){const e=`${"modelValue"===a?"model":a}Modifiers`,{number:t,trim:i}=o[e]||EMPTY_OBJ;i&&(r=n.map(e=>isString(e)?e.trim():e)),t&&(r=n.map(looseToNumber))}let s,c=o[s=toHandlerKey(t)]||o[s=toHandlerKey(camelize(t))];!c&&i&&(c=o[s=toHandlerKey(hyphenate(t))]),c&&callWithAsyncErrorHandling(c,e,6,r);const l=o[s+"Once"];if(l){if(e.emitted){if(e.emitted[s])return}else e.emitted={};e.emitted[s]=!0,callWithAsyncErrorHandling(l,e,6,r)}}function normalizeEmitsOptions(e,t,n=!1){const o=t.emitsCache,r=o.get(e);if(void 0!==r)return r;const i=e.emits;let a={},s=!1;if(!isFunction(e)){const o=e=>{const n=normalizeEmitsOptions(e,t,!0);n&&(s=!0,extend(a,n))};!n&&t.mixins.length&&t.mixins.forEach(o),e.extends&&o(e.extends),e.mixins&&e.mixins.forEach(o)}return i||s?(isArray(i)?i.forEach(e=>a[e]=null):extend(a,i),isObject(e)&&o.set(e,a),a):(isObject(e)&&o.set(e,null),null)}function isEmitListener(e,t){return!(!e||!isOn(t))&&(t=t.slice(2).replace(/Once$/,""),hasOwn(e,t[0].toLowerCase()+t.slice(1))||hasOwn(e,hyphenate(t))||hasOwn(e,t))}let currentRenderingInstance=null;function setCurrentRenderingInstance(e){const t=currentRenderingInstance;return currentRenderingInstance=e,e&&e.type.__scopeId,t}const INITIAL_WATCHER_VALUE={};function watch(e,t,n){return doWatch(e,t,n)}function doWatch(e,t,{immediate:n,deep:o,flush:r,once:i,onTrack:a,onTrigger:s}=EMPTY_OBJ){if(t&&i){const e=t;t=(...t)=>{e(...t),b()}}const c=currentInstance,l=e=>!0===o?e:traverse(e,!1===o?1:void 0);let u,p,d=!1,f=!1;if(isRef(e)?(u=()=>e.value,d=isShallow(e)):isReactive(e)?(u=()=>l(e),d=!0):isArray(e)?(f=!0,d=e.some(e=>isReactive(e)||isShallow(e)),u=()=>e.map(e=>isRef(e)?e.value:isReactive(e)?l(e):isFunction(e)?callWithErrorHandling(e,c,2):void 0)):u=isFunction(e)?t?()=>callWithErrorHandling(e,c,2):()=>(p&&p(),callWithAsyncErrorHandling(e,c,3,[h])):NOOP,t&&o){const e=u;u=()=>traverse(e())}let h=e=>{p=y.onStop=()=>{callWithErrorHandling(e,c,4),p=y.onStop=void 0}},m=f?new Array(e.length).fill(INITIAL_WATCHER_VALUE):INITIAL_WATCHER_VALUE;const g=()=>{if(y.active&&y.dirty)if(t){const e=y.run();(o||d||(f?e.some((e,t)=>hasChanged(e,m[t])):hasChanged(e,m)))&&(p&&p(),callWithAsyncErrorHandling(t,c,3,[e,m===INITIAL_WATCHER_VALUE?void 0:f&&m[0]===INITIAL_WATCHER_VALUE?[]:m,h]),m=e)}else y.run()};let v;g.allowRecurse=!!t,"sync"===r?v=g:"post"===r?v=()=>queuePostRenderEffect$1(g,c&&c.suspense):(g.pre=!0,c&&(g.id=c.uid),v=()=>queueJob(g));const y=new ReactiveEffect2(u,NOOP,v),x=getCurrentScope(),b=()=>{y.stop(),x&&remove(x.effects,y)};return t?n?g():m=y.run():"post"===r?queuePostRenderEffect$1(y.run.bind(y),c&&c.suspense):y.run(),b}function instanceWatch(e,t,n){const o=this.proxy,r=isString(e)?e.includes(".")?createPathGetter(o,e):()=>o[e]:e.bind(o,o);let i;isFunction(t)?i=t:(i=t.handler,n=t);const a=setCurrentInstance(this),s=doWatch(r,i.bind(o),n);return a(),s}function createPathGetter(e,t){const n=t.split(".");return()=>{let t=e;for(let e=0;e<n.length&&t;e++)t=t[n[e]];return t}}function traverse(e,t,n=0,o){if(!isObject(e)||e.__v_skip)return e;if(t&&t>0){if(n>=t)return e;n++}if((o=o||new Set).has(e))return e;if(o.add(e),isRef(e))traverse(e.value,t,n,o);else if(isArray(e))for(let r=0;r<e.length;r++)traverse(e[r],t,n,o);else if(isSet(e)||isMap(e))e.forEach(e=>{traverse(e,t,n,o)});else if(isPlainObject(e))for(const r in e)traverse(e[r],t,n,o);return e}function createAppContext(){return{app:null,config:{isNativeTag:NO,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let uid$1=0;function createAppAPI(e,t){return function(e,t=null){isFunction(e)||(e=extend({},e)),null==t||isObject(t)||(t=null);const n=createAppContext(),o=new WeakSet,r=n.app={_uid:uid$1++,_component:e,_props:t,_container:null,_context:n,_instance:null,version:version,get config(){return n.config},set config(e){},use:(e,...t)=>(o.has(e)||(e&&isFunction(e.install)?(o.add(e),e.install(r,...t)):isFunction(e)&&(o.add(e),e(r,...t))),r),mixin:e=>(n.mixins.includes(e)||n.mixins.push(e),r),component:(e,t)=>t?(n.components[e]=t,r):n.components[e],directive:(e,t)=>t?(n.directives[e]=t,r):n.directives[e],mount(){},unmount(){},provide:(e,t)=>(n.provides[e]=t,r),runWithContext(e){const t=currentApp;currentApp=r;try{return e()}finally{currentApp=t}}};return r}}let currentApp=null;function provide(e,t){if(currentInstance){let n=currentInstance.provides;const o=currentInstance.parent&&currentInstance.parent.provides;o===n&&(n=currentInstance.provides=Object.create(o)),n[e]=t,"app"===currentInstance.type.mpType&&currentInstance.appContext.app.provide(e,t)}else;}function inject(e,t,n=!1){const o=currentInstance||currentRenderingInstance;if(o||currentApp){const r=o?null==o.parent?o.vnode.appContext&&o.vnode.appContext.provides:o.parent.provides:currentApp._context.provides;if(r&&e in r)return r[e];if(arguments.length>1)return n&&isFunction(t)?t.call(o&&o.proxy):t}}
/*! #__NO_SIDE_EFFECTS__ */function defineComponent(e,t){return isFunction(e)?(()=>extend({name:e.name},t,{setup:e}))():e}const isKeepAlive=e=>e.type.__isKeepAlive;function onActivated(e,t){registerKeepAliveHook(e,"a",t)}function onDeactivated(e,t){registerKeepAliveHook(e,"da",t)}function registerKeepAliveHook(e,t,n=currentInstance){const o=e.__wdc||(e.__wdc=()=>{let t=n;for(;t;){if(t.isDeactivated)return;t=t.parent}return e()});if(injectHook(t,o,n),n){let e=n.parent;for(;e&&e.parent;)isKeepAlive(e.parent.vnode)&&injectToKeepAliveRoot(o,t,n,e),e=e.parent}}function injectToKeepAliveRoot(e,t,n,o){const r=injectHook(t,e,o,!0);onUnmounted(()=>{remove(o[t],r)},n)}function injectHook(e,t,n=currentInstance,o=!1){if(n){isRootHook(e)&&(n=n.root);const r=n[e]||(n[e]=[]),i=t.__weh||(t.__weh=(...o)=>{if(n.isUnmounted)return;pauseTracking();const r=setCurrentInstance(n),i=callWithAsyncErrorHandling(t,n,e,o);return r(),resetTracking(),i});return o?r.unshift(i):r.push(i),i}}const createHook$1=e=>(t,n=currentInstance)=>(!isInSSRComponentSetup||"sp"===e)&&injectHook(e,(...e)=>t(...e),n),onBeforeMount=createHook$1("bm"),onMounted=createHook$1("m"),onBeforeUpdate=createHook$1("bu"),onUpdated=createHook$1("u"),onBeforeUnmount=createHook$1("bum"),onUnmounted=createHook$1("um"),onServerPrefetch=createHook$1("sp"),onRenderTriggered=createHook$1("rtg"),onRenderTracked=createHook$1("rtc");function onErrorCaptured(e,t=currentInstance){injectHook("ec",e,t)}const getPublicInstance=e=>e?isStatefulComponent(e)?getExposeProxy(e)||e.proxy:getPublicInstance(e.parent):null,publicPropertiesMap=extend(Object.create(null),{$:e=>e,$el:e=>e.__$el||(e.__$el={}),$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>getPublicInstance(e.parent),$root:e=>getPublicInstance(e.root),$emit:e=>e.emit,$options:e=>resolveMergedOptions(e),$forceUpdate:e=>e.f||(e.f=()=>{e.effect.dirty=!0,queueJob(e.update)}),$watch:e=>instanceWatch.bind(e)}),hasSetupBinding=(e,t)=>e!==EMPTY_OBJ&&!e.__isScriptSetup&&hasOwn(e,t),PublicInstanceProxyHandlers={get({_:e},t){const{ctx:n,setupState:o,data:r,props:i,accessCache:a,type:s,appContext:c}=e;let l;if("$"!==t[0]){const s=a[t];if(void 0!==s)switch(s){case 1:return o[t];case 2:return r[t];case 4:return n[t];case 3:return i[t]}else{if(hasSetupBinding(o,t))return a[t]=1,o[t];if(r!==EMPTY_OBJ&&hasOwn(r,t))return a[t]=2,r[t];if((l=e.propsOptions[0])&&hasOwn(l,t))return a[t]=3,i[t];if(n!==EMPTY_OBJ&&hasOwn(n,t))return a[t]=4,n[t];shouldCacheAccess&&(a[t]=0)}}const u=publicPropertiesMap[t];let p,d;return u?("$attrs"===t&&track(e,"get",t),u(e)):(p=s.__cssModules)&&(p=p[t])?p:n!==EMPTY_OBJ&&hasOwn(n,t)?(a[t]=4,n[t]):(d=c.config.globalProperties,hasOwn(d,t)?d[t]:void 0)},set({_:e},t,n){const{data:o,setupState:r,ctx:i}=e;return hasSetupBinding(r,t)?(r[t]=n,!0):o!==EMPTY_OBJ&&hasOwn(o,t)?(o[t]=n,!0):!hasOwn(e.props,t)&&(("$"!==t[0]||!(t.slice(1)in e))&&(i[t]=n,!0))},has({_:{data:e,setupState:t,accessCache:n,ctx:o,appContext:r,propsOptions:i}},a){let s;return!!n[a]||e!==EMPTY_OBJ&&hasOwn(e,a)||hasSetupBinding(t,a)||(s=i[0])&&hasOwn(s,a)||hasOwn(o,a)||hasOwn(publicPropertiesMap,a)||hasOwn(r.config.globalProperties,a)},defineProperty(e,t,n){return null!=n.get?e._.accessCache[t]=0:hasOwn(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function normalizePropsOrEmits(e){return isArray(e)?e.reduce((e,t)=>(e[t]=null,e),{}):e}let shouldCacheAccess=!0;function applyOptions$1(e){const t=resolveMergedOptions(e),n=e.proxy,o=e.ctx;shouldCacheAccess=!1,t.beforeCreate&&callHook$1(t.beforeCreate,e,"bc");const{data:r,computed:i,methods:a,watch:s,provide:c,inject:l,created:u,beforeMount:p,mounted:d,beforeUpdate:f,updated:h,activated:m,deactivated:g,beforeDestroy:v,beforeUnmount:y,destroyed:x,unmounted:b,render:_,renderTracked:k,renderTriggered:C,errorCaptured:S,serverPrefetch:E,expose:A,inheritAttrs:O,components:w,directives:T,filters:R}=t;if(l&&resolveInjections(l,o,null),a)for(const I in a){const e=a[I];isFunction(e)&&(o[I]=e.bind(n))}if(r){const t=r.call(n,n);isObject(t)&&(e.data=reactive(t))}if(shouldCacheAccess=!0,i)for(const I in i){const e=i[I],t=isFunction(e)?e.bind(n,n):isFunction(e.get)?e.get.bind(n,n):NOOP,r=!isFunction(e)&&isFunction(e.set)?e.set.bind(n):NOOP,a=computed({get:t,set:r});Object.defineProperty(o,I,{enumerable:!0,configurable:!0,get:()=>a.value,set:e=>a.value=e})}if(s)for(const I in s)createWatcher(s[I],o,n,I);function P(e,t){isArray(t)?t.forEach(t=>e(t.bind(n))):t&&e(t.bind(n))}if(function(){if(c){const e=isFunction(c)?c.call(n):c;Reflect.ownKeys(e).forEach(t=>{provide(t,e[t])})}}(),u&&callHook$1(u,e,"c"),P(onBeforeMount,p),P(onMounted,d),P(onBeforeUpdate,f),P(onUpdated,h),P(onActivated,m),P(onDeactivated,g),P(onErrorCaptured,S),P(onRenderTracked,k),P(onRenderTriggered,C),P(onBeforeUnmount,y),P(onUnmounted,b),P(onServerPrefetch,E),isArray(A))if(A.length){const t=e.exposed||(e.exposed={});A.forEach(e=>{Object.defineProperty(t,e,{get:()=>n[e],set:t=>n[e]=t})})}else e.exposed||(e.exposed={});_&&e.render===NOOP&&(e.render=_),null!=O&&(e.inheritAttrs=O),w&&(e.components=w),T&&(e.directives=T),e.ctx.$onApplyOptions&&e.ctx.$onApplyOptions(t,e,n)}function resolveInjections(e,t,n=NOOP){isArray(e)&&(e=normalizeInject(e));for(const o in e){const n=e[o];let r;r=isObject(n)?"default"in n?inject(n.from||o,n.default,!0):inject(n.from||o):inject(n),isRef(r)?Object.defineProperty(t,o,{enumerable:!0,configurable:!0,get:()=>r.value,set:e=>r.value=e}):t[o]=r}}function callHook$1(e,t,n){callWithAsyncErrorHandling(isArray(e)?e.map(e=>e.bind(t.proxy)):e.bind(t.proxy),t,n)}function createWatcher(e,t,n,o){const r=o.includes(".")?createPathGetter(n,o):()=>n[o];if(isString(e)){const n=t[e];isFunction(n)&&watch(r,n)}else if(isFunction(e))watch(r,e.bind(n));else if(isObject(e))if(isArray(e))e.forEach(e=>createWatcher(e,t,n,o));else{const o=isFunction(e.handler)?e.handler.bind(n):t[e.handler];isFunction(o)&&watch(r,o,e)}}function resolveMergedOptions(e){const t=e.type,{mixins:n,extends:o}=t,{mixins:r,optionsCache:i,config:{optionMergeStrategies:a}}=e.appContext,s=i.get(t);let c;return s?c=s:r.length||n||o?(c={},r.length&&r.forEach(e=>mergeOptions(c,e,a,!0)),mergeOptions(c,t,a)):c=t,isObject(t)&&i.set(t,c),c}function mergeOptions(e,t,n,o=!1){const{mixins:r,extends:i}=t;i&&mergeOptions(e,i,n,!0),r&&r.forEach(t=>mergeOptions(e,t,n,!0));for(const a in t)if(o&&"expose"===a);else{const o=internalOptionMergeStrats[a]||n&&n[a];e[a]=o?o(e[a],t[a]):t[a]}return e}const internalOptionMergeStrats={data:mergeDataFn,props:mergeEmitsOrPropsOptions,emits:mergeEmitsOrPropsOptions,methods:mergeObjectOptions,computed:mergeObjectOptions,beforeCreate:mergeAsArray$1,created:mergeAsArray$1,beforeMount:mergeAsArray$1,mounted:mergeAsArray$1,beforeUpdate:mergeAsArray$1,updated:mergeAsArray$1,beforeDestroy:mergeAsArray$1,beforeUnmount:mergeAsArray$1,destroyed:mergeAsArray$1,unmounted:mergeAsArray$1,activated:mergeAsArray$1,deactivated:mergeAsArray$1,errorCaptured:mergeAsArray$1,serverPrefetch:mergeAsArray$1,components:mergeObjectOptions,directives:mergeObjectOptions,watch:mergeWatchOptions,provide:mergeDataFn,inject:mergeInject};function mergeDataFn(e,t){return t?e?function(){return extend(isFunction(e)?e.call(this,this):e,isFunction(t)?t.call(this,this):t)}:t:e}function mergeInject(e,t){return mergeObjectOptions(normalizeInject(e),normalizeInject(t))}function normalizeInject(e){if(isArray(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function mergeAsArray$1(e,t){return e?[...new Set([].concat(e,t))]:t}function mergeObjectOptions(e,t){return e?extend(Object.create(null),e,t):t}function mergeEmitsOrPropsOptions(e,t){return e?isArray(e)&&isArray(t)?[...new Set([...e,...t])]:extend(Object.create(null),normalizePropsOrEmits(e),normalizePropsOrEmits(null!=t?t:{})):t}function mergeWatchOptions(e,t){if(!e)return t;if(!t)return e;const n=extend(Object.create(null),e);for(const o in t)n[o]=mergeAsArray$1(e[o],t[o]);return n}function initProps$1(e,t,n,o=!1){const r={},i={};e.propsDefaults=Object.create(null),setFullProps(e,t,r,i);for(const a in e.propsOptions[0])a in r||(r[a]=void 0);n?e.props=o?r:shallowReactive(r):e.type.props?e.props=r:e.props=i,e.attrs=i}function updateProps(e,t,n,o){const{props:r,attrs:i,vnode:{patchFlag:a}}=e,s=toRaw(r),[c]=e.propsOptions;let l=!1;if(a>0&&!(16&a)){if(8&a){const n=e.vnode.dynamicProps;for(let o=0;o<n.length;o++){let a=n[o];if(isEmitListener(e.emitsOptions,a))continue;const u=t[a];if(c)if(hasOwn(i,a))u!==i[a]&&(i[a]=u,l=!0);else{const t=camelize(a);r[t]=resolvePropValue$1(c,s,t,u,e,!1)}else u!==i[a]&&(i[a]=u,l=!0)}}}else{let o;setFullProps(e,t,r,i)&&(l=!0);for(const i in s)t&&(hasOwn(t,i)||(o=hyphenate(i))!==i&&hasOwn(t,o))||(c?!n||void 0===n[i]&&void 0===n[o]||(r[i]=resolvePropValue$1(c,s,i,void 0,e,!0)):delete r[i]);if(i!==s)for(const e in i)t&&hasOwn(t,e)||(delete i[e],l=!0)}l&&trigger(e,"set","$attrs")}function setFullProps(e,t,n,o){const[r,i]=e.propsOptions;let a,s=!1;if(t)for(let c in t){if(isReservedProp(c))continue;const l=t[c];let u;r&&hasOwn(r,u=camelize(c))?i&&i.includes(u)?(a||(a={}))[u]=l:n[u]=l:isEmitListener(e.emitsOptions,c)||c in o&&l===o[c]||(o[c]=l,s=!0)}if(i){const t=toRaw(n),o=a||EMPTY_OBJ;for(let a=0;a<i.length;a++){const s=i[a];n[s]=resolvePropValue$1(r,t,s,o[s],e,!hasOwn(o,s))}}return s}function resolvePropValue$1(e,t,n,o,r,i){const a=e[n];if(null!=a){const e=hasOwn(a,"default");if(e&&void 0===o){const e=a.default;if(a.type!==Function&&!a.skipFactory&&isFunction(e)){const{propsDefaults:i}=r;if(n in i)o=i[n];else{const a=setCurrentInstance(r);o=i[n]=e.call(null,t),a()}}else o=e}a[0]&&(i&&!e?o=!1:!a[1]||""!==o&&o!==hyphenate(n)||(o=!0))}return o}function normalizePropsOptions(e,t,n=!1){const o=t.propsCache,r=o.get(e);if(r)return r;const i=e.props,a={},s=[];let c=!1;if(!isFunction(e)){const o=e=>{c=!0;const[n,o]=normalizePropsOptions(e,t,!0);extend(a,n),o&&s.push(...o)};!n&&t.mixins.length&&t.mixins.forEach(o),e.extends&&o(e.extends),e.mixins&&e.mixins.forEach(o)}if(!i&&!c)return isObject(e)&&o.set(e,EMPTY_ARR),EMPTY_ARR;if(isArray(i))for(let u=0;u<i.length;u++){const e=camelize(i[u]);validatePropName(e)&&(a[e]=EMPTY_OBJ)}else if(i)for(const u in i){const e=camelize(u);if(validatePropName(e)){const t=i[u],n=a[e]=isArray(t)||isFunction(t)?{type:t}:extend({},t);if(n){const t=getTypeIndex(Boolean,n.type),o=getTypeIndex(String,n.type);n[0]=t>-1,n[1]=o<0||t<o,(t>-1||hasOwn(n,"default"))&&s.push(e)}}}const l=[a,s];return isObject(e)&&o.set(e,l),l}function validatePropName(e){return"$"!==e[0]&&!isReservedProp(e)}function getType(e){if(null===e)return"null";if("function"==typeof e)return e.name||"";if("object"==typeof e){return e.constructor&&e.constructor.name||""}return""}function isSameType(e,t){return getType(e)===getType(t)}function getTypeIndex(e,t){return isArray(t)?t.findIndex(t=>isSameType(t,e)):isFunction(t)&&isSameType(t,e)?0:-1}const queuePostRenderEffect$1=queuePostFlushCb,emptyAppContext=createAppContext();let uid=0;function createComponentInstance(e,t,n){const o=e.type,r=(t?t.appContext:e.appContext)||emptyAppContext,i={uid:uid++,vnode:e,type:o,parent:t,appContext:r,root:null,next:null,subTree:null,effect:null,update:null,scope:new EffectScope(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(r.provides),accessCache:null,renderCache:[],components:null,directives:null,propsOptions:normalizePropsOptions(o,r),emitsOptions:normalizeEmitsOptions(o,r),emit:null,emitted:null,propsDefaults:EMPTY_OBJ,inheritAttrs:o.inheritAttrs,ctx:EMPTY_OBJ,data:EMPTY_OBJ,props:EMPTY_OBJ,attrs:EMPTY_OBJ,slots:EMPTY_OBJ,refs:EMPTY_OBJ,setupState:EMPTY_OBJ,setupContext:null,attrsProxy:null,slotsProxy:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null,$uniElements:new Map,$templateUniElementRefs:[],$templateUniElementStyles:{},$eS:{}};return i.ctx={_:i},i.root=t?t.root:i,i.emit=emit.bind(null,i),e.ce&&e.ce(i),i}let currentInstance=null;const getCurrentInstance=()=>currentInstance||currentRenderingInstance;let internalSetCurrentInstance,setInSSRSetupState;internalSetCurrentInstance=e=>{currentInstance=e},setInSSRSetupState=e=>{isInSSRComponentSetup=e};const setCurrentInstance=e=>{const t=currentInstance;return internalSetCurrentInstance(e),e.scope.on(),()=>{e.scope.off(),internalSetCurrentInstance(t)}},unsetCurrentInstance=()=>{currentInstance&&currentInstance.scope.off(),internalSetCurrentInstance(null)};function isStatefulComponent(e){return 4&e.vnode.shapeFlag}let isInSSRComponentSetup=!1;function setupComponent(e,t=!1){t&&setInSSRSetupState(t);const{props:n}=e.vnode,o=isStatefulComponent(e);initProps$1(e,n,o,t);const r=o?setupStatefulComponent(e):void 0;return t&&setInSSRSetupState(!1),r}function setupStatefulComponent(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=markRaw(new Proxy(e.ctx,PublicInstanceProxyHandlers));const{setup:o}=n;if(o){const t=e.setupContext=o.length>1?createSetupContext(e):null,n=setCurrentInstance(e);pauseTracking();const r=callWithErrorHandling(o,e,0,[e.props,t]);resetTracking(),n(),isPromise(r)?r.then(unsetCurrentInstance,unsetCurrentInstance):handleSetupResult(e,r)}else finishComponentSetup(e)}function handleSetupResult(e,t,n){isFunction(t)?e.render=t:isObject(t)&&(e.setupState=proxyRefs(t)),finishComponentSetup(e)}function finishComponentSetup(e,t,n){const o=e.type;e.render||(e.render=o.render||NOOP);{const t=setCurrentInstance(e);pauseTracking();try{applyOptions$1(e)}finally{resetTracking(),t()}}}function getAttrsProxy(e){return e.attrsProxy||(e.attrsProxy=new Proxy(e.attrs,{get:(t,n)=>(track(e,"get","$attrs"),t[n])}))}function createSetupContext(e){const t=t=>{e.exposed=t||{}};return{get attrs(){return getAttrsProxy(e)},slots:e.slots,emit:e.emit,expose:t}}function getExposeProxy(e){if(e.exposed)return e.exposeProxy||(e.exposeProxy=new Proxy(proxyRefs(markRaw(e.exposed)),{get:(t,n)=>n in t?t[n]:e.proxy[n],has:(e,t)=>t in e||t in publicPropertiesMap}))}const computed=(e,t)=>computed$1(e,t,isInSSRComponentSetup),version="3.4.21";function unwrapper(e){return unref(e)}const ARRAYTYPE="[object Array]",OBJECTTYPE="[object Object]";function diff(e,t){const n={};return syncKeys(e,t),_diff(e,t,"",n),n}function syncKeys(e,t){if((e=unwrapper(e))===t)return;const n=toTypeString(e),o=toTypeString(t);if(n==OBJECTTYPE&&o==OBJECTTYPE)for(let r in t){const n=e[r];void 0===n?e[r]=null:syncKeys(n,t[r])}else n==ARRAYTYPE&&o==ARRAYTYPE&&e.length>=t.length&&t.forEach((t,n)=>{syncKeys(e[n],t)})}function _diff(e,t,n,o){if((e=unwrapper(e))===t)return;const r=toTypeString(e),i=toTypeString(t);if(r==OBJECTTYPE)if(i!=OBJECTTYPE||Object.keys(e).length<Object.keys(t).length)setResult(o,n,e);else for(let a in e){const r=unwrapper(e[a]),i=t[a],s=toTypeString(r),c=toTypeString(i);if(s!=ARRAYTYPE&&s!=OBJECTTYPE)r!=i&&setResult(o,(""==n?"":n+".")+a,r);else if(s==ARRAYTYPE)c!=ARRAYTYPE||r.length<i.length?setResult(o,(""==n?"":n+".")+a,r):r.forEach((e,t)=>{_diff(e,i[t],(""==n?"":n+".")+a+"["+t+"]",o)});else if(s==OBJECTTYPE)if(c!=OBJECTTYPE||Object.keys(r).length<Object.keys(i).length)setResult(o,(""==n?"":n+".")+a,r);else for(let e in r)_diff(r[e],i[e],(""==n?"":n+".")+a+"."+e,o)}else r==ARRAYTYPE?i!=ARRAYTYPE||e.length<t.length?setResult(o,n,e):e.forEach((e,r)=>{_diff(e,t[r],n+"["+r+"]",o)}):setResult(o,n,e)}function setResult(e,t,n){e[t]=n}function hasComponentEffect(e){return queue.includes(e.update)}function flushCallbacks(e){const t=e.ctx.__next_tick_callbacks;if(t&&t.length){const e=t.slice(0);t.length=0;for(let t=0;t<e.length;t++)e[t]()}}function nextTick(e,t){const n=e.ctx;if(!n.__next_tick_pending&&!hasComponentEffect(e))return nextTick$1(t&&t.bind(e.proxy));let o;return n.__next_tick_callbacks||(n.__next_tick_callbacks=[]),n.__next_tick_callbacks.push(()=>{t?callWithErrorHandling(t.bind(e.proxy),e,14):o&&o(e.proxy)}),new Promise(e=>{o=e})}function clone(e,t){const n=typeof(e=unwrapper(e));if("object"===n&&null!==e){let n=t.get(e);if(void 0!==n)return n;if(isArray(e)){const o=e.length;n=new Array(o),t.set(e,n);for(let r=0;r<o;r++)n[r]=clone(e[r],t)}else{n={},t.set(e,n);for(const o in e)hasOwn(e,o)&&(n[o]=clone(e[o],t))}return n}if("symbol"!==n)return e}function deepCopy(e){return clone(e,"undefined"!=typeof WeakMap?new WeakMap:new Map)}function getMPInstanceData(e,t){const n=e.data,o=Object.create(null);return t.forEach(e=>{o[e]=n[e]}),o}function patch(e,t,n){if(!t)return;(t=deepCopy(t)).$eS=e.$eS||{};const o=e.ctx,r=o.mpType;if("page"===r||"component"===r){t.r0=1;const n=o.$scope,r=diff(t,getMPInstanceData(n,Object.keys(t)));Object.keys(r).length?(o.__next_tick_pending=!0,n.setData(r,()=>{o.__next_tick_pending=!1,flushCallbacks(e)}),flushPreFlushCbs()):flushCallbacks(e)}}function initAppConfig(e){e.globalProperties.$nextTick=function(e){return nextTick(this.$,e)}}function onApplyOptions(e,t,n){t.appContext.config.globalProperties.$applyOptions(e,t,n);const o=e.computed;if(o){const e=Object.keys(o);if(e.length){const n=t.ctx;n.$computedKeys||(n.$computedKeys=[]),n.$computedKeys.push(...e)}}delete t.ctx.$onApplyOptions}function setRef$1(e,t=!1){const{setupState:n,$templateRefs:o,$templateUniElementRefs:r,ctx:{$scope:i,$mpPlatform:a}}=e;if("mp-alipay"===a)return;if(!i||!o&&!r)return;if(t)return o&&o.forEach(e=>setTemplateRef(e,null,n)),void(r&&r.forEach(e=>setTemplateRef(e,null,n)));const s="mp-baidu"===a||"mp-toutiao"===a,c=e=>{if(0===e.length)return[];const t=(i.selectAllComponents(".r")||[]).concat(i.selectAllComponents(".r-i-f")||[]);return e.filter(e=>{const o=findComponentPublicInstance(t,e.i);return!(!s||null!==o)||(setTemplateRef(e,o,n),!1)})},l=()=>{if(o){const t=c(o);t.length&&e.proxy&&e.proxy.$scope&&e.proxy.$scope.setData({r1:1},()=>{c(t)})}};r&&r.length&&nextTick(e,()=>{r.forEach(e=>{isArray(e.v)?e.v.forEach(t=>{setTemplateRef(e,t,n)}):setTemplateRef(e,e.v,n)})}),i._$setRef?i._$setRef(l):nextTick(e,l)}function toSkip(e){return isObject(e)&&markRaw(e),e}function findComponentPublicInstance(e,t){const n=e.find(e=>e&&(e.properties||e.props).uI===t);if(n){const e=n.$vm;return e?getExposeProxy(e.$)||e:toSkip(n)}return null}function setTemplateRef({r:e,f:t},n,o){if(isFunction(e))e(n,{});else{const r=isString(e),i=isRef(e);if(r||i)if(t){if(!i)return;isArray(e.value)||(e.value=[]);const t=e.value;if(-1===t.indexOf(n)){if(t.push(n),!n)return;n.$&&onBeforeUnmount(()=>remove(t,n),n.$)}}else r?hasOwn(o,e)&&(o[e]=n):isRef(e)&&(e.value=n)}}const queuePostRenderEffect=queuePostFlushCb;function mountComponent(e,t){const n=e.component=createComponentInstance(e,t.parentComponent,null);return n.ctx.$onApplyOptions=onApplyOptions,n.ctx.$children=[],"app"===t.mpType&&(n.render=NOOP),t.onBeforeSetup&&t.onBeforeSetup(n,t),setupComponent(n),t.parentComponent&&n.proxy&&t.parentComponent.ctx.$children.push(getExposeProxy(n)||n.proxy),setupRenderEffect(n),n.proxy}const getFunctionalFallthrough=e=>{let t;for(const n in e)("class"===n||"style"===n||isOn(n))&&((t||(t={}))[n]=e[n]);return t};function renderComponentRoot(e){const{type:t,vnode:n,proxy:o,withProxy:r,props:i,propsOptions:[a],slots:s,attrs:c,emit:l,render:u,renderCache:p,data:d,setupState:f,ctx:h,uid:m,appContext:{app:{config:{globalProperties:{pruneComponentPropsCache:g}}}},inheritAttrs:v}=e;let y;e.$uniElementIds=new Map,e.$templateRefs=[],e.$templateUniElementRefs=[],e.$templateUniElementStyles={},e.$ei=0,g(m),e.__counter=0===e.__counter?1:0;const x=setCurrentRenderingInstance(e);try{if(4&n.shapeFlag){fallthroughAttrs(v,i,a,c);const e=r||o;y=u.call(e,e,p,i,f,d,h)}else{fallthroughAttrs(v,i,a,t.props?c:getFunctionalFallthrough(c));const e=t;y=e.length>1?e(i,{attrs:c,slots:s,emit:l}):e(i,null)}}catch(b){handleError(b,e,1),y=!1}return setRef$1(e),setCurrentRenderingInstance(x),y}function fallthroughAttrs(e,t,n,o){if(t&&o&&!1!==e){const e=Object.keys(o).filter(e=>"class"!==e&&"style"!==e);if(!e.length)return;n&&e.some(isModelListener)?e.forEach(e=>{isModelListener(e)&&e.slice(9)in n||(t[e]=o[e])}):e.forEach(e=>t[e]=o[e])}}const updateComponentPreRender=e=>{pauseTracking(),flushPreFlushCbs(),resetTracking()};function componentUpdateScopedSlotsFn(){const e=this.$scopedSlotsData;if(!e||0===e.length)return;const t=this.ctx.$scope,n=t.data,o=Object.create(null);e.forEach(({path:e,index:t,data:r})=>{const i=getValueByDataPath(n,e),a=isString(t)?`${e}.${t}`:`${e}[${t}]`;if(void 0===i||void 0===i[t])o[a]=r;else{const e=diff(r,i[t]);Object.keys(e).forEach(t=>{o[a+"."+t]=e[t]})}}),e.length=0,Object.keys(o).length&&t.setData(o)}function toggleRecurse({effect:e,update:t},n){e.allowRecurse=t.allowRecurse=n}function setupRenderEffect(e){const t=componentUpdateScopedSlotsFn.bind(e);e.$updateScopedSlots=()=>nextTick$1(()=>queueJob(t));const n=e.effect=new ReactiveEffect2(()=>{if(e.isMounted){const{next:t,bu:n,u:o}=e;toggleRecurse(e,!1),updateComponentPreRender(),n&&invokeArrayFns$1(n),toggleRecurse(e,!0),patch(e,renderComponentRoot(e)),o&&queuePostRenderEffect(o)}else onBeforeUnmount(()=>{setRef$1(e,!0)},e),patch(e,renderComponentRoot(e))},NOOP,()=>queueJob(o),e.scope),o=e.update=()=>{n.dirty&&n.run()};o.id=e.uid,toggleRecurse(e,!0),o()}function unmountComponent(e){const{bum:t,scope:n,update:o,um:r}=e;t&&invokeArrayFns$1(t);{const t=e.parent;if(t){const n=t.ctx.$children,o=getExposeProxy(e)||e.proxy,r=n.indexOf(o);r>-1&&n.splice(r,1)}}n.stop(),o&&(o.active=!1),r&&queuePostRenderEffect(r),queuePostRenderEffect(()=>{e.isUnmounted=!0})}const oldCreateApp=createAppAPI();function getTarget(){return"undefined"!=typeof window?window:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof global?global:"undefined"!=typeof my?my:void 0}function createVueApp(e,t=null){getTarget().__VUE__=!0;const n=oldCreateApp(e,t),o=n._context;initAppConfig(o.config);const r=e=>(e.appContext=o,e.shapeFlag=6,e),i=function(e,t){return mountComponent(r(e),t)},a=function(e){return e&&unmountComponent(e.$)};return n.mount=function(){e.render=NOOP;const t=mountComponent(r({type:e}),{mpType:"app",mpInstance:null,parentComponent:null,slots:[],props:null});return n._instance=t.$,t.$app=n,t.$createComponent=i,t.$destroyComponent=a,o.$appInstance=t,t},n.unmount=function(){},n}function injectLifecycleHook(e,t,n,o){isFunction(t)&&injectHook(e,t.bind(n),o)}function initHooks$1(e,t,n){const o=e.mpType||n.$mpType;o&&"component"!==o&&Object.keys(e).forEach(o=>{if(isUniLifecycleHook(o,e[o],!1)){const r=e[o];isArray(r)?r.forEach(e=>injectLifecycleHook(o,e,n,t)):injectLifecycleHook(o,r,n,t)}})}function applyOptions$2(e,t,n){initHooks$1(e,t,n)}function set(e,t,n){return e[t]=n}function $callMethod(e,...t){const n=this[e];return n?n(...t):(console.error(`method ${e} not found`),null)}function createErrorHandler(e){return function(t,n,o){if(!n)throw t;const r=e._instance;if(!r||!r.proxy)throw t;r.proxy.$callHook(ON_ERROR,t)}}function mergeAsArray(e,t){return e?[...new Set([].concat(e,t))]:t}function initOptionMergeStrategies(e){UniLifecycleHooks.forEach(t=>{e[t]=mergeAsArray})}let realAtob;const b64="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",b64re=/^(?:[A-Za-z\d+/]{4})*?(?:[A-Za-z\d+/]{2}(?:==)?|[A-Za-z\d+/]{3}=?)?$/;function b64DecodeUnicode(e){return decodeURIComponent(realAtob(e).split("").map(function(e){return"%"+("00"+e.charCodeAt(0).toString(16)).slice(-2)}).join(""))}function getCurrentUserInfo(){const e=index.getStorageSync("uni_id_token")||"",t=e.split(".");if(!e||3!==t.length)return{uid:null,role:[],permission:[],tokenExpired:0};let n;try{n=JSON.parse(b64DecodeUnicode(t[1]))}catch(o){throw new Error("获取当前用户信息出错，详细错误信息为："+o.message)}return n.tokenExpired=1e3*n.exp,delete n.exp,delete n.iat,n}function uniIdMixin(e){e.uniIDHasRole=function(e){const{role:t}=getCurrentUserInfo();return t.indexOf(e)>-1},e.uniIDHasPermission=function(e){const{permission:t}=getCurrentUserInfo();return this.uniIDHasRole("admin")||t.indexOf(e)>-1},e.uniIDTokenValid=function(){const{tokenExpired:e}=getCurrentUserInfo();return e>Date.now()}}function initApp(e){const t=e._context.config;t.errorHandler=invokeCreateErrorHandler(e,createErrorHandler),initOptionMergeStrategies(t.optionMergeStrategies);const n=t.globalProperties;uniIdMixin(n),n.$set=set,n.$applyOptions=applyOptions$2,n.$callMethod=$callMethod,index.invokeCreateVueAppHook(e)}realAtob="function"!=typeof atob?function(e){if(e=String(e).replace(/[\t\n\f\r ]+/g,""),!b64re.test(e))throw new Error("Failed to execute 'atob' on 'Window': The string to be decoded is not correctly encoded.");var t;e+="==".slice(2-(3&e.length));for(var n,o,r="",i=0;i<e.length;)t=b64.indexOf(e.charAt(i++))<<18|b64.indexOf(e.charAt(i++))<<12|(n=b64.indexOf(e.charAt(i++)))<<6|(o=b64.indexOf(e.charAt(i++))),r+=64===n?String.fromCharCode(t>>16&255):64===o?String.fromCharCode(t>>16&255,t>>8&255):String.fromCharCode(t>>16&255,t>>8&255,255&t);return r}:atob;const propsCaches=Object.create(null);function pruneComponentPropsCache(e){delete propsCaches[e]}function findComponentPropsData(e){if(!e)return;const[t,n]=e.split(",");return propsCaches[t]?propsCaches[t][parseInt(n)]:void 0}var plugin={install(e){initApp(e),e.config.globalProperties.pruneComponentPropsCache=pruneComponentPropsCache;const t=e.mount;e.mount=function(n){const o=t.call(e,n),r=getCreateApp();return r?r(o):"undefined"!=typeof createMiniProgramApp&&createMiniProgramApp(o),o}}};function getCreateApp(){const e="createApp";return"undefined"!=typeof global&&void 0!==global[e]?global[e]:"undefined"!=typeof my?my[e]:void 0}function vOn(e,t){const n=getCurrentInstance(),o=n.ctx,r=void 0===t||"mp-weixin"!==o.$mpPlatform&&"mp-qq"!==o.$mpPlatform&&"mp-xhs"!==o.$mpPlatform||!isString(t)&&"number"!=typeof t?"":"_"+t,i="e"+n.$ei+++r,a=o.$scope;if(!e)return delete a[i],i;const s=a[i];return s?s.value=e:a[i]=createInvoker(e,n),i}function createInvoker(e,t){const n=e=>{patchMPEvent(e);let o=[e];t&&t.ctx.$getTriggerEventDetail&&"number"==typeof e.detail&&(e.detail=t.ctx.$getTriggerEventDetail(e.detail)),e.detail&&e.detail.__args__&&(o=e.detail.__args__);const r=n.value,i=()=>callWithAsyncErrorHandling(patchStopImmediatePropagation(e,r),t,5,o),a=e.target,s=!!a&&(!!a.dataset&&"true"===String(a.dataset.eventsync));if(!bubbles.includes(e.type)||s){const t=i();if("input"===e.type&&(isArray(t)||isPromise(t)))return;return t}setTimeout(i)};return n.value=e,n}const bubbles=["tap","longpress","longtap","transitionend","animationstart","animationiteration","animationend","touchforcechange"];function patchMPEvent(e){e.type&&e.target&&(e.preventDefault=NOOP,e.stopPropagation=NOOP,e.stopImmediatePropagation=NOOP,hasOwn(e,"detail")||(e.detail={}),hasOwn(e,"markerId")&&(e.detail="object"==typeof e.detail?e.detail:{},e.detail.markerId=e.markerId),isPlainObject(e.detail)&&hasOwn(e.detail,"checked")&&!hasOwn(e.detail,"value")&&(e.detail.value=e.detail.checked),isPlainObject(e.detail)&&(e.target=extend({},e.target,e.detail)))}function patchStopImmediatePropagation(e,t){if(isArray(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n&&n.call(e),e._stopped=!0},t.map(e=>t=>!t._stopped&&e(t))}return t}function vFor(e,t){let n;if(isArray(e)||isString(e)){n=new Array(e.length);for(let o=0,r=e.length;o<r;o++)n[o]=t(e[o],o,o)}else if("number"==typeof e){n=new Array(e);for(let o=0;o<e;o++)n[o]=t(o+1,o,o)}else if(isObject(e))if(e[Symbol.iterator])n=Array.from(e,(e,n)=>t(e,n,n));else{const o=Object.keys(e);n=new Array(o.length);for(let r=0,i=o.length;r<i;r++){const i=o[r];n[r]=t(e[i],i,r)}}else n=[];return n}const o=(e,t)=>vOn(e,t),f=(e,t)=>vFor(e,t),e=(e,...t)=>extend(e,...t),n=e=>normalizeClass(e),t=e=>toDisplayString(e);function createApp$1(e,t=null){return e&&(e.mpType="app"),createVueApp(e,t).use(plugin)}const createSSRApp=createApp$1;function initVueIds(e,t){if(!e)return;const n=e.split(","),o=n.length;1===o?t._$vueId=n[0]:2===o&&(t._$vueId=n[0],t._$vuePid=n[1])}const EXTRAS=["externalClasses"];function initExtraOptions(e,t){EXTRAS.forEach(n=>{hasOwn(t,n)&&(e[n]=t[n])})}const WORKLET_RE=/_(.*)_worklet_factory_/;function initWorkletMethods(e,t){t&&Object.keys(t).forEach(n=>{const o=n.match(WORKLET_RE);if(o){const r=o[1];e[n]=t[n],e[r]=t[r]}})}function initWxsCallMethods(e,t){isArray(t)&&t.forEach(t=>{e[t]=function(e){return this.$vm[t](e)}})}function selectAllComponents(e,t,n){e.selectAllComponents(t).forEach(e=>{const t=e.properties.uR;n[t]=e.$vm||e})}function initRefs(e,t){Object.defineProperty(e,"refs",{get(){const e={};selectAllComponents(t,".r",e);return t.selectAllComponents(".r-i-f").forEach(t=>{const n=t.properties.uR;n&&(e[n]||(e[n]=[]),e[n].push(t.$vm||t))}),e}})}function findVmByVueId(e,t){const n=e.$children;for(let r=n.length-1;r>=0;r--){const e=n[r];if(e.$scope._$vueId===t)return e}let o;for(let r=n.length-1;r>=0;r--)if(o=findVmByVueId(n[r],t),o)return o}const MP_METHODS=["createSelectorQuery","createIntersectionObserver","selectAllComponents","selectComponent"];function createEmitFn(e,t){return function(n,...o){const r=t.$scope;if(r&&n){const e={__args__:o};r.triggerEvent(n,e)}return e.apply(this,[n,...o])}}function initBaseInstance(e,t){const n=e.ctx;n.mpType=t.mpType,n.$mpType=t.mpType,n.$mpPlatform="mp-weixin",n.$scope=t.mpInstance,n.$mp={},n._self={},e.slots={},isArray(t.slots)&&t.slots.length&&(t.slots.forEach(t=>{e.slots[t]=!0}),e.slots[SLOT_DEFAULT_NAME]&&(e.slots.default=!0)),n.getOpenerEventChannel=function(){return t.mpInstance.getOpenerEventChannel()},n.$hasHook=hasHook,n.$callHook=callHook,e.emit=createEmitFn(e.emit,n)}function initComponentInstance(e,t){initBaseInstance(e,t);const n=e.ctx;MP_METHODS.forEach(e=>{n[e]=function(...t){const o=n.$scope;if(o&&o[e])return o[e].apply(o,t)}})}function initMocks(e,t,n){const o=e.ctx;n.forEach(n=>{hasOwn(t,n)&&(e[n]=o[n]=t[n])})}function hasHook(e){const t=this.$[e];return!(!t||!t.length)}function callHook(e,t){"mounted"===e&&(callHook.call(this,"bm"),this.$.isMounted=!0,e="m");const n=this.$[e];return n&&invokeArrayFns(n,t)}const PAGE_INIT_HOOKS=[ON_LOAD,ON_SHOW,ON_HIDE,ON_UNLOAD,ON_RESIZE,ON_TAB_ITEM_TAP,ON_REACH_BOTTOM,ON_PULL_DOWN_REFRESH,ON_ADD_TO_FAVORITES];function findHooks(e,t=new Set){if(e){Object.keys(e).forEach(n=>{isUniLifecycleHook(n,e[n])&&t.add(n)});{const{extends:n,mixins:o}=e;o&&o.forEach(e=>findHooks(e,t)),n&&findHooks(n,t)}}return t}function initHook(e,t,n){-1!==n.indexOf(t)||hasOwn(e,t)||(e[t]=function(e){return this.$vm&&this.$vm.$callHook(t,e)})}const EXCLUDE_HOOKS=[ON_READY];function initHooks(e,t,n=EXCLUDE_HOOKS){t.forEach(t=>initHook(e,t,n))}function initUnknownHooks(e,t,n=EXCLUDE_HOOKS){findHooks(t).forEach(t=>initHook(e,t,n))}function initRuntimeHooks(e,t){if(!t)return;Object.keys(MINI_PROGRAM_PAGE_RUNTIME_HOOKS).forEach(n=>{t&MINI_PROGRAM_PAGE_RUNTIME_HOOKS[n]&&initHook(e,n,[])})}const findMixinRuntimeHooks=once(()=>{const e=[],t=isFunction(getApp)&&getApp({allowDefault:!0});if(t&&t.$vm&&t.$vm.$){const n=t.$vm.$.appContext.mixins;if(isArray(n)){const t=Object.keys(MINI_PROGRAM_PAGE_RUNTIME_HOOKS);n.forEach(n=>{t.forEach(t=>{hasOwn(n,t)&&!e.includes(t)&&e.push(t)})})}}return e});function initMixinRuntimeHooks(e){initHooks(e,findMixinRuntimeHooks())}const HOOKS=[ON_SHOW,ON_HIDE,ON_ERROR,ON_THEME_CHANGE,ON_PAGE_NOT_FOUND,ON_UNHANDLE_REJECTION];function parseApp(e,t){const n=e.$,o={globalData:e.$options&&e.$options.globalData||{},$vm:e,onLaunch(t){this.$vm=e;const o=n.ctx;this.$vm&&o.$scope&&o.$callHook||(initBaseInstance(n,{mpType:"app",mpInstance:this,slots:[]}),o.globalData=this.globalData,e.$callHook(ON_LAUNCH,t))}},{onError:r}=n;r&&(n.appContext.config.errorHandler=t=>{e.$callHook(ON_ERROR,t)}),initLocale(e);const i=e.$.type;initHooks(o,HOOKS),initUnknownHooks(o,i);{const e=i.methods;e&&extend(o,e)}return o}function initCreateApp(e){return function(e){return App(parseApp(e))}}function initCreateSubpackageApp(e){return function(e){const t=parseApp(e),n=isFunction(getApp)&&getApp({allowDefault:!0});if(!n)return;e.$.ctx.$scope=n;const o=n.globalData;o&&Object.keys(t.globalData).forEach(e=>{hasOwn(o,e)||(o[e]=t.globalData[e])}),Object.keys(t).forEach(e=>{hasOwn(n,e)||(n[e]=t[e])}),initAppLifecycle(t,e)}}function initAppLifecycle(e,t){if(isFunction(e.onLaunch)){const t=wx.getLaunchOptionsSync&&wx.getLaunchOptionsSync();e.onLaunch(t)}isFunction(e.onShow)&&wx.onAppShow&&wx.onAppShow(e=>{t.$callHook("onShow",e)}),isFunction(e.onHide)&&wx.onAppHide&&wx.onAppHide(e=>{t.$callHook("onHide",e)})}function initLocale(e){const t=ref(normalizeLocale(wx.getSystemInfoSync().language)||LOCALE_EN);Object.defineProperty(e,"$locale",{get:()=>t.value,set(e){t.value=e}})}const builtInProps=["eO","uR","uRIF","uI","uT","uP","uS"];function initDefaultProps(e,t=!1){const n={};if(!t){let e=function(e){const t=Object.create(null);e&&e.forEach(e=>{t[e]=!0}),this.setData({$slots:t})};builtInProps.forEach(e=>{n[e]={type:null,value:""}}),n.uS={type:null,value:[]},n.uS.observer=e}return e.behaviors&&e.behaviors.includes("wx://form-field")&&(e.properties&&e.properties.name||(n.name={type:null,value:""}),e.properties&&e.properties.value||(n.value={type:null,value:""})),n}function initVirtualHostProps(e){const t={};return e&&e.virtualHost&&(t.virtualHostStyle={type:null,value:""},t.virtualHostClass={type:null,value:""}),t}function initProps(e){e.properties||(e.properties={}),extend(e.properties,initDefaultProps(e),initVirtualHostProps(e.options))}const PROP_TYPES=[String,Number,Boolean,Object,Array,null];function parsePropType(e,t){return isArray(e)&&1===e.length?e[0]:e}function normalizePropType(e,t){const n=parsePropType(e);return-1!==PROP_TYPES.indexOf(n)?n:null}function initPageProps({properties:e},t){isArray(t)?t.forEach(t=>{e[t]={type:String,value:""}}):isPlainObject(t)&&Object.keys(t).forEach(n=>{const o=t[n];if(isPlainObject(o)){let t=o.default;isFunction(t)&&(t=t());const r=o.type;o.type=normalizePropType(r),e[n]={type:o.type,value:t}}else e[n]={type:normalizePropType(o)}})}function findPropsData(e,t){return(t?findPagePropsData(e):findComponentPropsData(resolvePropValue(e.uP)))||{}}function findPagePropsData(e){const t={};return isPlainObject(e)&&Object.keys(e).forEach(n=>{-1===builtInProps.indexOf(n)&&(t[n]=resolvePropValue(e[n]))}),t}function initFormField(e){const t=e.$options;isArray(t.behaviors)&&t.behaviors.includes("uni://form-field")&&e.$watch("modelValue",()=>{e.$scope&&e.$scope.setData({name:e.name,value:e.modelValue})},{immediate:!0})}function resolvePropValue(e){return e}function initData(e){return{}}function initPropsObserver(e){const t=function(){const e=this.properties.uP;e&&(this.$vm?updateComponentProps(resolvePropValue(e),this.$vm.$):"m"===resolvePropValue(this.properties.uT)&&updateMiniProgramComponentProperties(resolvePropValue(e),this))};e.observers||(e.observers={}),e.observers.uP=t}function updateMiniProgramComponentProperties(e,t){const n=t.properties,o=findComponentPropsData(e)||{};hasPropsChanged(n,o,!1)&&t.setData(o)}function updateComponentProps(e,t){const n=toRaw(t.props),o=findComponentPropsData(e)||{};hasPropsChanged(n,o)&&(updateProps(t,o,n),hasQueueJob(t.update)&&invalidateJob(t.update),t.update())}function hasPropsChanged(e,t,n=!0){const o=Object.keys(t);if(n&&o.length!==Object.keys(e).length)return!0;for(let r=0;r<o.length;r++){const n=o[r];if(t[n]!==e[n])return!0}return!1}function initBehaviors(e){const t=e.behaviors;let n=e.props;n||(e.props=n=[]);const o=[];return isArray(t)&&t.forEach(e=>{o.push(e.replace("uni://","wx://")),"uni://form-field"===e&&(isArray(n)?(n.push("name"),n.push("modelValue")):(n.name={type:String,default:""},n.modelValue={type:[String,Number,Boolean,Array,Object,Date],default:""}))}),o}function applyOptions(e,t){e.data=initData(),e.behaviors=initBehaviors(t)}function parseComponent(e,{parse:t,mocks:n,isPage:o,initRelation:r,handleLink:i,initLifetimes:a}){e=e.default||e;const s={multipleSlots:!0,addGlobalClass:!0,pureDataPattern:/^uP$/};isArray(e.mixins)&&e.mixins.forEach(e=>{isObject(e.options)&&extend(s,e.options)}),e.options&&extend(s,e.options);const c={options:s,lifetimes:a({mocks:n,isPage:o,initRelation:r,vueOptions:e}),pageLifetimes:{show(){this.$vm&&this.$vm.$callHook("onPageShow")},hide(){this.$vm&&this.$vm.$callHook("onPageHide")},resize(e){this.$vm&&this.$vm.$callHook("onPageResize",e)}},methods:{__l:i}};return applyOptions(c,e),initProps(c),initPropsObserver(c),initExtraOptions(c,e),initWxsCallMethods(c.methods,e.wxsCallMethods),initWorkletMethods(c.methods,e.methods),t&&t(c,{handleLink:i}),c}function initCreateComponent(e){return function(t){return Component(parseComponent(t,e))}}let $createComponentFn,$destroyComponentFn;function getAppVm(){return getApp().$vm}function $createComponent(e,t){$createComponentFn||($createComponentFn=getAppVm().$createComponent);const n=$createComponentFn(e,t);return getExposeProxy(n.$)||n}function $destroyComponent(e){return $destroyComponentFn||($destroyComponentFn=getAppVm().$destroyComponent),$destroyComponentFn(e)}function parsePage(e,t){const{parse:n,mocks:o,isPage:r,initRelation:i,handleLink:a,initLifetimes:s}=t,c=parseComponent(e,{mocks:o,isPage:r,initRelation:i,handleLink:a,initLifetimes:s});initPageProps(c,(e.default||e).props);const l=c.methods;return l.onLoad=function(e){return this.options=e,this.$page={fullPath:addLeadingSlash(this.route+stringifyQuery(e))},this.$vm&&this.$vm.$callHook(ON_LOAD,e)},initHooks(l,PAGE_INIT_HOOKS),initUnknownHooks(l,e),initRuntimeHooks(l,e.__runtimeHooks),initMixinRuntimeHooks(l),n&&n(c,{handleLink:a}),c}function initCreatePage(e){return function(t){return Component(parsePage(t,e))}}function initCreatePluginApp(e){return function(e){initAppLifecycle(parseApp(e),e)}}const MPPage=Page,MPComponent=Component;function initTriggerEvent(e){const t=e.triggerEvent,n=function(n,...o){return t.apply(e,[customizeEvent(n),...o])};try{e.triggerEvent=n}catch(o){e._triggerEvent=n}}function initMiniProgramHook(e,t,n){const o=t[e];t[e]=o?function(...e){return initTriggerEvent(this),o.apply(this,e)}:function(){initTriggerEvent(this)}}function initLifetimes({mocks:e,isPage:t,initRelation:n,vueOptions:o}){return{attached(){let r=this.properties;initVueIds(r.uI,this);const i={vuePid:this._$vuePid};n(this,i);const a=this,s=t(a);let c=r;this.$vm=$createComponent({type:o,props:findPropsData(c,s)},{mpType:s?"page":"component",mpInstance:a,slots:r.uS||{},parentComponent:i.parent&&i.parent.$,onBeforeSetup(t,n){initRefs(t,a),initMocks(t,a,e),initComponentInstance(t,n)}}),s||initFormField(this.$vm)},ready(){this.$vm&&(this.$vm.$callHook("mounted"),this.$vm.$callHook(ON_READY))},detached(){this.$vm&&(pruneComponentPropsCache(this.$vm.$.uid),$destroyComponent(this.$vm))}}}Page=function(e){return initMiniProgramHook(ON_LOAD,e),MPPage(e)},Component=function(e){initMiniProgramHook("created",e);return e.properties&&e.properties.uP||(initProps(e),initPropsObserver(e)),MPComponent(e)};const mocks=["__route__","__wxExparserNodeId__","__wxWebviewId__"];function isPage(e){return!!e.route}function initRelation(e,t){e.triggerEvent("__l",t)}function handleLink(e){const t=e.detail||e.value,n=t.vuePid;let o;n&&(o=findVmByVueId(this.$vm,n)),o||(o=this.$vm),t.parent=o}var parseOptions=Object.freeze({__proto__:null,handleLink:handleLink,initLifetimes:initLifetimes,initRelation:initRelation,isPage:isPage,mocks:mocks});const createApp=initCreateApp(),createPage=initCreatePage(parseOptions),createComponent=initCreateComponent(parseOptions),createPluginApp=initCreatePluginApp(),createSubpackageApp=initCreateSubpackageApp();wx.createApp=global.createApp=createApp,wx.createPage=createPage,wx.createComponent=createComponent,wx.createPluginApp=global.createPluginApp=createPluginApp,wx.createSubpackageApp=global.createSubpackageApp=createSubpackageApp;const createHook=e=>(t,n=getCurrentInstance())=>{!isInSSRComponentSetup&&injectHook(e,t,n)},onShow=createHook(ON_SHOW),onHide=createHook(ON_HIDE),onLaunch=createHook(ON_LAUNCH);function getDefaultExportFromCjs(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var vodWxSdkV2={};(function(exports){!function(e,t){for(var n in t)e[n]=t[n]}(exports,function(e){var t={};function n(o){if(t[o])return t[o].exports;var r=t[o]={i:o,l:!1,exports:{}};return e[o].call(r.exports,r,r.exports,n),r.l=!0,r.exports}return n.m=e,n.c=t,n.d=function(e,t,o){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:o})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var o=Object.create(null);if(n.r(o),Object.defineProperty(o,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var r in e)n.d(o,r,function(t){return e[t]}.bind(null,r));return o},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=0)}([function(e,t,n){function o(e){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function r(e,t,n){return t&&function(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}(e.prototype,t),e}function i(e,t){return(i=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function a(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],function(){})),!0}catch(e){return!1}}();return function(){var n,r=c(e);if(t){var i=c(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return function(e,t){if(t&&("object"===o(t)||"function"==typeof t))return t;return s(e)}(this,n)}}function s(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function c(e){return(c=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function l(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,o)}return n}function u(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?l(Object(n),!0).forEach(function(t){p(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):l(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function p(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var d=n(1),f=n(2);n(3).UploaderEvent;var h=n(4).EventEmitter,m="COS_REGION_KEY",g="vod2.qcloud.com",v=n(5),y=v.VodReporter,x=v.reportEvent;var b=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&i(e,t)}(n,e);var t=a(n);function n(e){var o;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,n),p(s(o=t.call(this)),"retryCommitNum",3),p(s(o),"retryApplyNum",3),p(s(o),"retryPrepareNum",3);var r,i,a,c=s(o);if("object"!==f.getType(e))throw new Error("opts must be a object");if(c.appId=e.appId||void 0,c.taskId=void 0,c.cos=void 0,c.cosStrategy=(i={FileParallelLimit:(r=e).fileParallelLimit,ChunkParallelLimit:r.chunkParallelLimit||6,ChunkRetryTimes:r.chunkRetryTimes,ChunkSize:r.chunkSize||8388608,SliceSize:r.sliceSize,CopyChunkParallelLimit:r.copyChunkParallelLimit,CopyChunkSize:r.copyChunkSize,CopySliceSize:r.copySliceSize,ProgressInterval:r.progressInterval},Object.keys(i).filter(function(e){return void 0!==i[e]}).reduce(function(e,t){return u(u({},e),{},p({},t,i[t]))},{})),!(a=e.mediaFile?e.mediaFile:e.videoFile))throw new Error("need `mediaFile` param");c.fileKey=a.tempFilePath.replace(/^.*?([^/]{32}\.\w+)$/,"$1"),e.mediaName?c.fileName=e.mediaName:c.fileName=e.fileName;var l=e.coverFile;if(c.videoFileMessage=f.getFileMessage(a,c.fileName),l&&(l.tempFilePath=l.tempFilePaths[0],l.size=l.tempFiles[0].size,c.coverFileMessage=f.getFileMessage(l,c.fileName),c.fileKey=l.tempFilePath.replace(/^.*?([^/]{32}\.\w+)$/,"$1")),c.reportId=e.reportId||"",c.getSignature=e.getSignature,c.error=e.error,c.progress=e.progress,c.finish=e.finish,!c.getSignature)throw new Error("need `getSignature` param");if(!(f.isFunction(c.getSignature)&&f.isFunction(c.error)&&f.isFunction(c.progress)&&f.isFunction(c.finish)))throw new Error("getSignature, error, progress, finish must be a Function.");return o}return r(n,[{key:"setStorage",value:function(e,t){wx$1.setStorageSync("wp_ugc_"+e,t)}},{key:"getStorage",value:function(e){try{return wx$1.getStorageSync("wp_ugc_"+e)}catch(t){return""}}},{key:"delStorage",value:function(e){wx$1.removeStorageSync("wp_ugc_"+e)}},{key:"regionRace",value:function(e,t){Promise.race(e.map(function(e){return t=e,new Promise(function(e,n){wx$1.request({method:"HEAD",url:"https://"+t.domain,success:function(n){e(t.region)},fail:function(e){f.isFunction(self.error)&&self.error(e)}})});var t})).then(function(e){wx$1.setStorageSync(m,e),t&&t(e)})}},{key:"requestRegion",value:function(e){var t=this;t.getSignature(function(n){t.signature=n;var o={signature:n},r=Date.now();wx$1.request({method:"POST",url:"https://vod2.qcloud.com/v3/index.php?Action=PrepareUploadUGC",data:o,dataType:"json",success:function(n){0===n.data.code?(t.appId=t.appId||n.data.data.appId,t.regionRace(n.data.data.cosRegionList,function(n){t.emit(x.report_prepare,{data:{region:n},requestStartTime:r}),e(n)})):t.retryPrepareNum>0?(t.emit(x.report_prepare,{err:n.data,requestStartTime:r}),t.retryPrepareNum-=1,t.requestRegion(e)):f.isFunction(t.error)&&t.error(n)},fail:function(e){f.isFunction(t.error)&&t.error(e)}})})}},{key:"getStoreRegion",value:function(e){try{var t=wx$1.getStorageSync(m);if(!t)throw new Error("no storage");return e(t)}catch(n){this.requestRegion(e)}}},{key:"start",value:function(){var e=this;e.applyUpload(function(t){e.uploadFile(t,function(){e.commitUpload()})})}},{key:"cancel",value:function(){this.cos.cancelTask(this.taskId)}},{key:"pause",value:function(){this.cos.pauseTask(this.taskId)}},{key:"restart",value:function(){this.cos.restartTask(this.taskId)}},{key:"applyUpload",value:function(e){var t=this;t.getSignature(function(n){t.signature=n;var o,r=t.getStorage(t.fileKey);o=r?{signature:n,vodSessionKey:r}:{signature:n,videoName:t.videoFileMessage.name,videoType:t.videoFileMessage.type},t.coverFileMessage&&(o.coverName=t.coverFileMessage.name,o.coverType=t.coverFileMessage.type);var i=Date.now();wx$1.request({method:"POST",url:"https://".concat(g,"/v3/index.php?Action=ApplyUploadUGC"),data:o,dataType:"json",success:function(n){0===n.data.code?(t.appId=t.appId||n.data.data.appId,t.emit(x.report_apply,{data:o,requestStartTime:i}),t.vodSessionKey=n.data.data.vodSessionKey,t.MiniProgramAccelerateHost=n.data.data.MiniProgramAccelerateHost,t.setStorage(t.fileKey,t.vodSessionKey),e(n)):t.retryApplyNum>0?(t.emit(x.report_apply,{err:n.data,requestStartTime:i}),t.retryApplyNum-=1,t.applyUpload(e)):f.isFunction(t.error)&&t.error(n)},fail:function(e){f.isFunction(t.error)&&t.error(e)}})})}},{key:"uploadFile",value:function(e,t){var n=this,o=e.data.data,r=new d(Object.assign({getAuthorization:function(e,t){t({TmpSecretId:o.tempCertificate.secretId,TmpSecretKey:o.tempCertificate.secretKey,XCosSecurityToken:o.tempCertificate.token,StartTime:o.timestamp,ExpiredTime:o.tempCertificate.expiredTime})}},n.cosStrategy));r.on("before-send",function(e){var t=e.url;console.log("url",t);var o=t.match(/^(https?:\/\/([^\/]+)\/)([^\/]*\/?)(.*)$/);e.url=t.replace(o[2],n.MiniProgramAccelerateHost||"vod2.qcloud.com"),e.headers["Vod-Forward-Cos"]=o[2]}),this.cos=r;var i={bucket:"".concat(o.storageBucket,"-").concat(o.storageAppId),region:o.storageRegionV5},a=[];if(this.videoFileMessage){var s=u(u({},i),{},{filePath:this.videoFileMessage.tempFilePath,fileSize:this.videoFileMessage.size,key:o.video.storagePath,onProgress:function(e){f.isFunction(n.progress)&&n.progress(e)},onTaskReady:function(e){n.taskId=e}});a.push(s)}if(this.coverFileMessage){var c=u(u({},i),{},{fileSize:this.coverFileMessage.size,filePath:this.coverFileMessage.tempFilePath,key:o.cover.storagePath,onTaskReady:f.noop,onProgress:f.noop});a.push(c)}var l=a.map(function(e){return new Promise(function(t,o){var i=Date.now();r.sliceUploadFile({Bucket:e.bucket,Region:e.region,Key:e.key,FilePath:e.filePath,FileSize:e.fileSize,onProgress:e.onProgress,onTaskReady:e.onTaskReady},function(r,a){if(r){if(e.filePath===n.videoFileMessage.tempFilePath&&n.emit(x.report_cos_upload,{err:r,requestStartTime:i}),f.isFunction(n.error)){var s=r.error,c=s&&s.Code?{code:s.Code,message:s.Message||s.message,reqid:s.RequestId||void 0}:{code:r.statusCode||-2,message:"cos error"};n.error(c)}o()}else t()})})});Promise.all(l).then(function(){t()})}},{key:"commitUpload",value:function(){var e=this,t={signature:this.signature,vodSessionKey:this.vodSessionKey},n=Date.now();wx$1.request({method:"POST",url:"https://".concat(g,"/v3/index.php?Action=CommitUploadUGC"),data:t,dataType:"json",success:function(t){if(0===t.data.code){e.emit(x.report_commit,{data:t.data.data,requestStartTime:n});var o=t.data.data;f.isFunction(e.finish)&&e.finish({fileId:o.fileId,videoName:e.videoFileMessage.name,videoUrl:o.video&&o.video.url,coverUrl:o.cover&&o.cover.url,verify_content:o.video&&o.video.verify_content}),e.delStorage(e.fileKey)}else e.emit(x.report_commit,{err:t.data,requestStartTime:n}),e.retryCommitNum>0?(e.retryCommitNum-=1,e.commitUpload()):f.isFunction(e.error)&&e.error(t)},fail:function(t){f.isFunction(e.error)&&e.error(t)}})}}]),n}(h);e.exports={start:function(e){try{var t=new b(e);return new y(t),t.start(),t}catch(n){if(!f.isFunction(e.error))throw n;e.error({code:-1,message:n.message})}}}},function(module,exports,__webpack_require__){var t2;window,t2=function(){return function(e){var t={};function n(o){if(t[o])return t[o].exports;var r=t[o]={i:o,l:!1,exports:{}};return e[o].call(r.exports,r,r.exports,n),r.l=!0,r.exports}return n.m=e,n.c=t,n.d=function(e,t,o){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:o})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var o=Object.create(null);if(n.r(o),Object.defineProperty(o,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var r in e)n.d(o,r,function(t){return e[t]}.bind(null,r));return o},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="/Users/<USER>/Documents/项目/sdk/cos-wx-sdk-v5/demo/lib",n(n.s=10)}([function(e,t,n){(function(t){function o(){
/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */
o=function(){return e};var e={},t=Object.prototype,n=t.hasOwnProperty,r="function"==typeof Symbol?Symbol:{},a=r.iterator||"@@iterator",s=r.asyncIterator||"@@asyncIterator",c=r.toStringTag||"@@toStringTag";function l(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{l({},"")}catch(w){l=function(e,t,n){return e[t]=n}}function u(e,t,n,o){var r=t&&t.prototype instanceof f?t:f,i=Object.create(r.prototype),a=new E(o||[]);return i._invoke=function(e,t,n){var o="suspendedStart";return function(r,i){if("executing"===o)throw new Error("Generator is already running");if("completed"===o){if("throw"===r)throw i;return{value:void 0,done:!0}}for(n.method=r,n.arg=i;;){var a=n.delegate;if(a){var s=k(a,n);if(s){if(s===d)continue;return s}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===o)throw o="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o="executing";var c=p(e,t,n);if("normal"===c.type){if(o=n.done?"completed":"suspendedYield",c.arg===d)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(o="completed",n.method="throw",n.arg=c.arg)}}}(e,n,a),i}function p(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(o){return{type:"throw",arg:o}}}e.wrap=u;var d={};function f(){}function h(){}function m(){}var g={};l(g,a,function(){return this});var v=Object.getPrototypeOf,y=v&&v(v(A([])));y&&y!==t&&n.call(y,a)&&(g=y);var x=m.prototype=f.prototype=Object.create(g);function b(e){["next","throw","return"].forEach(function(t){l(e,t,function(e){return this._invoke(t,e)})})}function _(e,t){function o(r,a,s,c){var l=p(e[r],e,a);if("throw"!==l.type){var u=l.arg,d=u.value;return d&&"object"==i(d)&&n.call(d,"__await")?t.resolve(d.__await).then(function(e){o("next",e,s,c)},function(e){o("throw",e,s,c)}):t.resolve(d).then(function(e){u.value=e,s(u)},function(e){return o("throw",e,s,c)})}c(l.arg)}var r;this._invoke=function(e,n){function i(){return new t(function(t,r){o(e,n,t,r)})}return r=r?r.then(i,i):i()}}function k(e,t){var n=e.iterator[t.method];if(void 0===n){if(t.delegate=null,"throw"===t.method){if(e.iterator.return&&(t.method="return",t.arg=void 0,k(e,t),"throw"===t.method))return d;t.method="throw",t.arg=new TypeError("The iterator does not provide a 'throw' method")}return d}var o=p(n,e.iterator,t.arg);if("throw"===o.type)return t.method="throw",t.arg=o.arg,t.delegate=null,d;var r=o.arg;return r?r.done?(t[e.resultName]=r.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,d):r:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,d)}function C(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function S(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function E(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(C,this),this.reset(!0)}function A(e){if(e){var t=e[a];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,r=function t(){for(;++o<e.length;)if(n.call(e,o))return t.value=e[o],t.done=!1,t;return t.value=void 0,t.done=!0,t};return r.next=r}}return{next:O}}function O(){return{value:void 0,done:!0}}return h.prototype=m,l(x,"constructor",m),l(m,"constructor",h),h.displayName=l(m,c,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===h||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,m):(e.__proto__=m,l(e,c,"GeneratorFunction")),e.prototype=Object.create(x),e},e.awrap=function(e){return{__await:e}},b(_.prototype),l(_.prototype,s,function(){return this}),e.AsyncIterator=_,e.async=function(t,n,o,r,i){void 0===i&&(i=Promise);var a=new _(u(t,n,o,r),i);return e.isGeneratorFunction(n)?a:a.next().then(function(e){return e.done?e.value:a.next()})},b(x),l(x,c,"Generator"),l(x,a,function(){return this}),l(x,"toString",function(){return"[object Generator]"}),e.keys=function(e){var t=[];for(var n in e)t.push(n);return t.reverse(),function n(){for(;t.length;){var o=t.pop();if(o in e)return n.value=o,n.done=!1,n}return n.done=!0,n}},e.values=A,E.prototype={constructor:E,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(S),!e)for(var t in this)"t"===t.charAt(0)&&n.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function o(n,o){return a.type="throw",a.arg=e,t.next=n,o&&(t.method="next",t.arg=void 0),!!o}for(var r=this.tryEntries.length-1;r>=0;--r){var i=this.tryEntries[r],a=i.completion;if("root"===i.tryLoc)return o("end");if(i.tryLoc<=this.prev){var s=n.call(i,"catchLoc"),c=n.call(i,"finallyLoc");if(s&&c){if(this.prev<i.catchLoc)return o(i.catchLoc,!0);if(this.prev<i.finallyLoc)return o(i.finallyLoc)}else if(s){if(this.prev<i.catchLoc)return o(i.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return o(i.finallyLoc)}}}},abrupt:function(e,t){for(var o=this.tryEntries.length-1;o>=0;--o){var r=this.tryEntries[o];if(r.tryLoc<=this.prev&&n.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var i=r;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=e,a.arg=t,i?(this.method="next",this.next=i.finallyLoc,d):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),d},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),S(n),d}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var o=n.completion;if("throw"===o.type){var r=o.arg;S(n)}return r}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,n){return this.delegate={iterator:A(e),resultName:t,nextLoc:n},"next"===this.method&&(this.arg=void 0),d}},e}function r(e,t,n,o,r,i,a){try{var s=e[i](a),c=s.value}catch(l){return void n(l)}s.done?t(c):Promise.resolve(c).then(o,r)}function i(e){return(i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var a=n(12),s=n(15),c=n(16),l=n(21),u=n(5).btoa,p=wx$1.getFileSystemManager(),d=n(6);function f(e){return encodeURIComponent(e).replace(/!/g,"%21").replace(/'/g,"%27").replace(/\(/g,"%28").replace(/\)/g,"%29").replace(/\*/g,"%2A")}function h(e,t){var n=[];for(var o in e)e.hasOwnProperty(o)&&n.push(t?f(o).toLowerCase():o);return n.sort(function(e,t){return(e=e.toLowerCase())===(t=t.toLowerCase())?0:e>t?1:-1})}var m=["content-disposition","content-encoding","content-length","content-md5","expect","expires","host","if-match","if-modified-since","if-none-match","if-unmodified-since","origin","range","response-cache-control","response-content-disposition","response-content-encoding","response-content-language","response-content-type","response-expires","transfer-encoding","versionid"],g=function(){},v=function(e){var t={};for(var n in e)e.hasOwnProperty(n)&&void 0!==e[n]&&null!==e[n]&&(t[n]=e[n]);return t};function y(e){return k(e,function(e){return"object"===i(e)&&null!==e?y(e):e})}function x(e,t){return _(t,function(n,o){e[o]=t[o]}),e}function b(e){return e instanceof Array}function _(e,t){for(var n in e)e.hasOwnProperty(n)&&t(e[n],n)}function k(e,t){var n=b(e)?[]:{};for(var o in e)e.hasOwnProperty(o)&&(n[o]=t(e[o],o));return n}var C,S,E=function(e,t){if(t=x({},t),"getAuth"!==e&&"getV4Auth"!==e&&"getObjectUrl"!==e){var n=t.Headers||{};t&&"object"===i(t)&&(function(){for(var e in t)t.hasOwnProperty(e)&&e.indexOf("x-cos-")>-1&&(n[e]=t[e])}(),P.each({"x-cos-mfa":"MFA","Content-MD5":"ContentMD5","Content-Length":"ContentLength","Content-Type":"ContentType",Expect:"Expect",Expires:"Expires","Cache-Control":"CacheControl","Content-Disposition":"ContentDisposition","Content-Encoding":"ContentEncoding",Range:"Range","If-Modified-Since":"IfModifiedSince","If-Unmodified-Since":"IfUnmodifiedSince","If-Match":"IfMatch","If-None-Match":"IfNoneMatch","x-cos-copy-source":"CopySource","x-cos-copy-source-Range":"CopySourceRange","x-cos-metadata-directive":"MetadataDirective","x-cos-copy-source-If-Modified-Since":"CopySourceIfModifiedSince","x-cos-copy-source-If-Unmodified-Since":"CopySourceIfUnmodifiedSince","x-cos-copy-source-If-Match":"CopySourceIfMatch","x-cos-copy-source-If-None-Match":"CopySourceIfNoneMatch","x-cos-acl":"ACL","x-cos-grant-read":"GrantRead","x-cos-grant-write":"GrantWrite","x-cos-grant-full-control":"GrantFullControl","x-cos-grant-read-acp":"GrantReadAcp","x-cos-grant-write-acp":"GrantWriteAcp","x-cos-storage-class":"StorageClass","x-cos-traffic-limit":"TrafficLimit","x-cos-mime-limit":"MimeLimit","x-cos-server-side-encryption-customer-algorithm":"SSECustomerAlgorithm","x-cos-server-side-encryption-customer-key":"SSECustomerKey","x-cos-server-side-encryption-customer-key-MD5":"SSECustomerKeyMD5","x-cos-server-side-encryption":"ServerSideEncryption","x-cos-server-side-encryption-cos-kms-key-id":"SSEKMSKeyId","x-cos-server-side-encryption-context":"SSEContext"},function(e,o){void 0!==t[e]&&(n[o]=t[e])}),t.Headers=v(n))}return t},A=function(e){return new Promise(function(t,n){p.readFile({filePath:e,success:function(e){t(e.data)},fail:function(e){n((null==e?void 0:e.errMsg)||"")}})})},O=(C=o().mark(function e(t,n,r){return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if("postObject"!==t){e.next=4;break}r(),e.next=21;break;case 4:if("putObject"!==t){e.next=20;break}if(void 0!==n.Body||!n.FilePath){e.next=17;break}return e.prev=6,e.next=9,A(n.FilePath);case 9:n.Body=e.sent,e.next=17;break;case 12:return e.prev=12,e.t0=e.catch(6),n.Body=void 0,r({error:"readFile error, ".concat(e.t0)}),e.abrupt("return");case 17:void 0!==n.Body?(n.ContentLength=n.Body.byteLength,r(null,n.ContentLength)):r({error:"missing param Body"}),e.next=21;break;case 20:n.FilePath?p.stat({path:n.FilePath,success:function(e){var t=e.stats;n.FileStat=t,n.FileStat.FilePath=n.FilePath;var o=t.isDirectory()?0:t.size;n.ContentLength=o=o||0,r(null,o)},fail:function(e){r(e)}}):r({error:"missing param FilePath"});case 21:case"end":return e.stop()}},e,null,[[6,12]])}),S=function(){var e=this,t=arguments;return new Promise(function(n,o){var i=C.apply(e,t);function a(e){r(i,n,o,a,s,"next",e)}function s(e){r(i,n,o,a,s,"throw",e)}a(void 0)})},function(e,t,n){return S.apply(this,arguments)}),w=function(e){return Date.now()+(e||0)},T=function(e,t){e=e.split("."),t=t.split(".");for(var n=Math.max(e.length,t.length);e.length<n;)e.push("0");for(;t.length<n;)t.push("0");for(var o=0;o<n;o++){var r=parseInt(e[o]),i=parseInt(t[o]);if(r>i)return 1;if(r<i)return-1}return 0},R=function(){var e=wx$1.getSystemInfoSync(),t=T(e.SDKVersion,"2.10.0")>=0;return!t&&e.platform,function(){return t}}(),P={noop:g,formatParams:E,apiWrapper:function(e,n){return function(o,r){var i,a=this;if("function"==typeof o&&(r=o,o={}),o=E(e,o),a.options.EnableTracker)if("sliceUploadFile"===o.calledBySdk)i=o.tracker&&o.tracker.generateSubTracker({apiName:e});else if(["uploadFile","uploadFiles"].includes(e))i=null;else{var s=-1;o.Body&&(s="string"==typeof o.Body?o.Body.length:o.Body.size||o.Body.byteLength||-1),i=new d({bucket:o.Bucket,region:o.Region,apiName:e,fileKey:o.Key,fileSize:s,deepTracker:a.options.DeepTracker,customId:a.options.CustomId,delay:a.options.TrackerDelay})}o.tracker=i;var c=function(e){return e&&e.headers&&(e.headers["x-cos-request-id"]&&(e.RequestId=e.headers["x-cos-request-id"]),e.headers["x-ci-request-id"]&&(e.RequestId=e.headers["x-ci-request-id"]),e.headers["x-cos-version-id"]&&(e.VersionId=e.headers["x-cos-version-id"]),e.headers["x-cos-delete-marker"]&&(e.DeleteMarker=e.headers["x-cos-delete-marker"])),e},l=function(e,t){i&&i.formatResult(e,t),r&&r(c(e),c(t))},u=function(){if("getService"!==e&&"abortUploadTask"!==e){var t=function(e,t){var n=t.Bucket,o=t.Region,r=t.Key;if(e.indexOf("Bucket")>-1||"deleteMultipleObject"===e||"multipartList"===e||"listObjectVersions"===e){if(!n)return"Bucket";if(!o)return"Region"}else if(e.indexOf("Object")>-1||e.indexOf("multipart")>-1||"sliceUploadFile"===e||"abortUploadTask"===e){if(!n)return"Bucket";if(!o)return"Region";if(!r)return"Key"}return!1}(e,o);if(t)return"missing param "+t;if(o.Region){if(o.Region.indexOf("cos.")>-1)return'param Region should not be start with "cos."';if(!/^([a-z\d-]+)$/.test(o.Region))return"Region format error.";!a.options.CompatibilityMode&&-1===o.Region.indexOf("-")&&"yfb"!==o.Region&&"default"!==o.Region&&o.Region}if(o.Bucket){if(!/^([a-z\d-]+)-(\d+)$/.test(o.Bucket))if(o.AppId)o.Bucket=o.Bucket+"-"+o.AppId;else{if(!a.options.AppId)return'Bucket should format as "test-1250000000".';o.Bucket=o.Bucket+"-"+a.options.AppId}o.AppId&&delete o.AppId}o.Key&&"/"===o.Key.substr(0,1)&&(o.Key=o.Key.substr(1))}}(),p=["getAuth","getObjectUrl"].includes(e),f=t.Promise;if(!p&&f&&!r)return new f(function(e,t){if(r=function(n,o){n?t(n):e(o)},u)return l({error:u});n.call(a,o,l)});if(u)return l({error:u});var h=n.call(a,o,l);return p?h:void 0}},xml2json:c,json2xml:l,md5:a,clearKey:v,fileSlice:function(e,t,n,o){e?p.readFile({filePath:e,position:t,length:n-t,success:function(e){o(e.data)},fail:function(){o(null)}}):o(null)},getBodyMd5:function(e,t,n){n=n||g,e&&t&&t instanceof ArrayBuffer?P.getFileMd5(t,function(e,t){n(t)}):n()},getFileMd5:function(e,t){var n=a(e);return t&&t(n),n},binaryBase64:function(e){var t,n,o,r="";for(t=0,n=e.length/2;t<n;t++)o=parseInt(e[2*t]+e[2*t+1],16),r+=String.fromCharCode(o);return u(r)},extend:x,isArray:b,isInArray:function(e,t){for(var n=!1,o=0;o<e.length;o++)if(t===e[o]){n=!0;break}return n},makeArray:function(e){return b(e)?e:[e]},each:_,map:k,filter:function(e,t){var n=b(e),o=n?[]:{};for(var r in e)e.hasOwnProperty(r)&&t(e[r],r)&&(n?o.push(e[r]):o[r]=e[r]);return o},clone:y,attr:function(e,t,n){return e&&t in e?e[t]:n},uuid:function(){var e=function(){return(65536*(1+Math.random())|0).toString(16).substring(1)};return e()+e()+"-"+e()+"-"+e()+"-"+e()+"-"+e()+e()+e()},camSafeUrlEncode:f,throttleOnProgress:function(e,t){var n,o,r=this,i=0,a=0,s=Date.now();function c(){if(o=0,t&&"function"==typeof t){n=Date.now();var r,c=Math.max(0,Math.round((a-i)/((n-s)/1e3)*100)/100)||0;r=0===a&&0===e?1:Math.floor(a/e*100)/100||0,s=n,i=a;try{t({loaded:a,total:e,speed:c,percent:r})}catch(l){}}}return function(t,n){if(t&&(a=t.loaded,e=t.total),n)clearTimeout(o),c();else{if(o)return;o=setTimeout(c,r.options.ProgressInterval)}}},getFileSize:O,getFileSizeByPath:function(e){return new Promise(function(t,n){p.stat({path:e,success:function(e){var n=e.stats,o=n.isDirectory()?0:n.size;t(o)},fail:function(e){n((null==e?void 0:e.errMsg)||"")}})})},getSkewTime:w,obj2str:function(e,t){var n,o,r,i=[],a=h(e);for(n=0;n<a.length;n++)r=void 0===e[o=a[n]]||null===e[o]?"":""+e[o],o=t?f(o).toLowerCase():f(o),r=f(r)||"",i.push(o+"="+r);return i.join("&")},getAuth:function(e){var t,n=(e=e||{}).SecretId,o=e.SecretKey,r=e.KeyTime,i=(e.method||e.Method||"get").toLowerCase(),a=y(e.Query||e.params||{}),c=function(e){var t={};for(var n in e){var o=n.toLowerCase();(o.indexOf("x-cos-")>-1||m.indexOf(o)>-1)&&(t[n]=e[n])}return t}(y(e.Headers||e.headers||{})),l=e.Key||"";e.UseRawKey?t=e.Pathname||e.pathname||"/"+l:0!==(t=e.Pathname||e.pathname||l).indexOf("/")&&(t="/"+t);var u=!1!==e.ForceSignHost;if(!c.Host&&!c.host&&e.Bucket&&e.Region&&u&&(c.Host=e.Bucket+".cos."+e.Region+".myqcloud.com"),n&&o){var p=Math.round(w(e.SystemClockOffset)/1e3)-1,d=p,f=e.Expires||e.expires;d+=void 0===f?900:1*f||0;var g=n,v=r||p+";"+d,x=r||p+";"+d,b=h(c,!0).join(";").toLowerCase(),_=h(a,!0).join(";").toLowerCase(),k=s.HmacSHA1(x,o).toString(),C=[i,t,P.obj2str(a,!0),P.obj2str(c,!0),""].join("\n"),S=["sha1",v,s.SHA1(C).toString(),""].join("\n");return["q-sign-algorithm=sha1","q-ak="+g,"q-sign-time="+v,"q-key-time="+x,"q-header-list="+b,"q-url-param-list="+_,"q-signature="+s.HmacSHA1(S,k).toString()].join("&")}},compareVersion:T,canFileSlice:R,isCIHost:function(e){return/^https?:\/\/([^/]+\.)?ci\.[^/]+/.test(e)},error:function(e,t){var n=e;return e.message=e.message||null,"string"==typeof t?(e.error=t,e.message=t):"object"===i(t)&&null!==t&&(x(e,t),(t.code||t.name)&&(e.code=t.code||t.name),t.message&&(e.message=t.message),t.stack&&(e.stack=t.stack)),"function"==typeof Object.defineProperty&&(Object.defineProperty(e,"name",{writable:!0,enumerable:!1}),Object.defineProperty(e,"message",{enumerable:!0})),e.name=t&&t.name||e.name||e.code||"Error",e.code||(e.code=e.name),e.error||(e.error=y(n)),e},getSourceParams:function(e){var t=this.options.CopySourceParser;if(t)return t(e);var n=e.match(/^([^.]+-\d+)\.cos(v6|-cdc|-internal)?\.([^.]+)\.((myqcloud\.com)|(tencentcos\.cn))\/(.+)$/);return n?{Bucket:n[1],Region:n[3],Key:n[7]}:null}};e.exports=P}).call(this,n(2))},function(e,t,n){function o(e,t){return void 0===t&&(t=Object),t&&"function"==typeof t.freeze?t.freeze(e):e}var r=o({HTML:"text/html",isHTML:function(e){return e===r.HTML},XML_APPLICATION:"application/xml",XML_TEXT:"text/xml",XML_XHTML_APPLICATION:"application/xhtml+xml",XML_SVG_IMAGE:"image/svg+xml"}),i=o({HTML:"http://www.w3.org/1999/xhtml",isHTML:function(e){return e===i.HTML},SVG:"http://www.w3.org/2000/svg",XML:"http://www.w3.org/XML/1998/namespace",XMLNS:"http://www.w3.org/2000/xmlns/"});t.assign=function(e,t){if(null===e||"object"!=typeof e)throw new TypeError("target is not an object");for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e},t.freeze=o,t.MIME_TYPE=r,t.NAMESPACE=i},function(e,t){var n;n=function(){return this}();try{n=n||new Function("return this")()}catch(o){"object"==typeof window&&(n=window)}e.exports=n},function(e,t){e.exports=function(e){return e.webpackPolyfill||(e.deprecate=function(){},e.paths=[],e.children||(e.children=[]),Object.defineProperty(e,"loaded",{enumerable:!0,get:function(){return e.l}}),Object.defineProperty(e,"id",{enumerable:!0,get:function(){return e.i}}),e.webpackPolyfill=1),e}},function(e,t,n){var o=n(1).NAMESPACE;function r(e){return""!==e}function i(e,t){return e.hasOwnProperty(t)||(e[t]=!0),e}function a(e){if(!e)return[];var t,n=(t=e)?t.split(/[\t\n\f\r ]+/).filter(r):[];return Object.keys(n.reduce(i,{}))}function s(e,t){for(var n in e)t[n]=e[n]}function c(e,t){var n=e.prototype;if(!(n instanceof t)){let o=function(){};o.prototype=t.prototype,s(n,o=new o),e.prototype=n=o}n.constructor!=e&&(n.constructor=e)}var l={},u=l.ELEMENT_NODE=1,p=l.ATTRIBUTE_NODE=2,d=l.TEXT_NODE=3,f=l.CDATA_SECTION_NODE=4,h=l.ENTITY_REFERENCE_NODE=5,m=l.ENTITY_NODE=6,g=l.PROCESSING_INSTRUCTION_NODE=7,v=l.COMMENT_NODE=8,y=l.DOCUMENT_NODE=9,x=l.DOCUMENT_TYPE_NODE=10,b=l.DOCUMENT_FRAGMENT_NODE=11,_=l.NOTATION_NODE=12,k={},C={},S=(k.INDEX_SIZE_ERR=(C[1]="Index size error",1),k.DOMSTRING_SIZE_ERR=(C[2]="DOMString size error",2),k.HIERARCHY_REQUEST_ERR=(C[3]="Hierarchy request error",3)),E=(k.WRONG_DOCUMENT_ERR=(C[4]="Wrong document",4),k.INVALID_CHARACTER_ERR=(C[5]="Invalid character",5),k.NO_DATA_ALLOWED_ERR=(C[6]="No data allowed",6),k.NO_MODIFICATION_ALLOWED_ERR=(C[7]="No modification allowed",7),k.NOT_FOUND_ERR=(C[8]="Not found",8)),A=(k.NOT_SUPPORTED_ERR=(C[9]="Not supported",9),k.INUSE_ATTRIBUTE_ERR=(C[10]="Attribute in use",10));function O(e,t){if(t instanceof Error)var n=t;else n=this,Error.call(this,C[e]),this.message=C[e],Error.captureStackTrace&&Error.captureStackTrace(this,O);return n.code=e,t&&(this.message=this.message+": "+t),n}function w(){}function T(e,t){this._node=e,this._refresh=t,R(this)}function R(e){var t=e._node._inc||e._node.ownerDocument._inc;if(e._inc!=t){var n=e._refresh(e._node);le(e,"length",n.length),s(n,e),e._inc=t}}function P(){}function I(e,t){for(var n=e.length;n--;)if(e[n]===t)return n}function N(e,t,n,r){if(r?t[I(t,r)]=n:t[t.length++]=n,e){n.ownerElement=e;var i=e.ownerDocument;i&&(r&&H(i,e,r),s=e,c=n,(a=i)&&a._inc++,c.namespaceURI===o.XMLNS&&(s._nsMap[c.prefix?c.localName:""]=c.value))}var a,s,c}function B(e,t,n){var o=I(t,n);if(!(o>=0))throw O(E,new Error(e.tagName+"@"+n));for(var r=t.length-1;o<r;)t[o]=t[++o];if(t.length=r,e){var i=e.ownerDocument;i&&(H(i,e,n),n.ownerElement=null)}}function M(){}function L(){}function D(e){return("<"==e?"&lt;":">"==e&&"&gt;")||"&"==e&&"&amp;"||'"'==e&&"&quot;"||"&#"+e.charCodeAt()+";"}function j(e,t){if(t(e))return!0;if(e=e.firstChild)do{if(j(e,t))return!0}while(e=e.nextSibling)}function F(){}function H(e,t,n,r){e&&e._inc++,n.namespaceURI===o.XMLNS&&delete t._nsMap[n.prefix?n.localName:""]}function U(e,t,n){if(e&&e._inc){e._inc++;var o=t.childNodes;if(n)o[o.length++]=n;else{for(var r=t.firstChild,i=0;r;)o[i++]=r,r=r.nextSibling;o.length=i,delete o[o.length]}}}function $(e,t){var n=t.previousSibling,o=t.nextSibling;return n?n.nextSibling=o:e.firstChild=o,o?o.previousSibling=n:e.lastChild=n,t.parentNode=null,t.previousSibling=null,t.nextSibling=null,U(e.ownerDocument,e),t}function z(e,t,n){var o=t.parentNode;if(o&&o.removeChild(t),t.nodeType===b){var r=t.firstChild;if(null==r)return t;var i=t.lastChild}else r=i=t;var a=n?n.previousSibling:e.lastChild;r.previousSibling=a,i.nextSibling=n,a?a.nextSibling=r:e.firstChild=r,null==n?e.lastChild=i:n.previousSibling=i;do{r.parentNode=e}while(r!==i&&(r=r.nextSibling));return U(e.ownerDocument||e,e),t.nodeType==b&&(t.firstChild=t.lastChild=null),t}function K(){this._nsMap={}}function q(){}function V(){}function G(){}function W(){}function Y(){}function X(){}function J(){}function Q(){}function Z(){}function ee(){}function te(){}function ne(){}function oe(e,t){var n=[],o=9==this.nodeType&&this.documentElement||this,r=o.prefix,i=o.namespaceURI;if(i&&null==r&&null==(r=o.lookupPrefix(i)))var a=[{namespace:i,prefix:null}];return ae(this,n,e,t,a),n.join("")}function re(e,t,n){var r=e.prefix||"",i=e.namespaceURI;if(!i)return!1;if("xml"===r&&i===o.XML||i===o.XMLNS)return!1;for(var a=n.length;a--;){var s=n[a];if(s.prefix===r)return s.namespace!==i}return!0}function ie(e,t,n){e.push(" ",t,'="',n.replace(/[<>&"\t\n\r]/g,D),'"')}function ae(e,t,n,r,i){if(i||(i=[]),r){if(!(e=r(e)))return;if("string"==typeof e)return void t.push(e)}switch(e.nodeType){case u:var a=e.attributes,s=a.length,c=e.firstChild,l=e.tagName,m=l;if(!(n=o.isHTML(e.namespaceURI)||n)&&!e.prefix&&e.namespaceURI){for(var _,k=0;k<a.length;k++)if("xmlns"===a.item(k).name){_=a.item(k).value;break}if(!_)for(var C=i.length-1;C>=0;C--)if(""===(S=i[C]).prefix&&S.namespace===e.namespaceURI){_=S.namespace;break}if(_!==e.namespaceURI)for(C=i.length-1;C>=0;C--){var S;if((S=i[C]).namespace===e.namespaceURI){S.prefix&&(m=S.prefix+":"+l);break}}}t.push("<",m);for(var E=0;E<s;E++)"xmlns"==(A=a.item(E)).prefix?i.push({prefix:A.localName,namespace:A.value}):"xmlns"==A.nodeName&&i.push({prefix:"",namespace:A.value});for(E=0;E<s;E++){var A,O,w;re(A=a.item(E),0,i)&&(ie(t,(O=A.prefix||"")?"xmlns:"+O:"xmlns",w=A.namespaceURI),i.push({prefix:O,namespace:w})),ae(A,t,n,r,i)}if(l===m&&re(e,0,i)&&(ie(t,(O=e.prefix||"")?"xmlns:"+O:"xmlns",w=e.namespaceURI),i.push({prefix:O,namespace:w})),c||n&&!/^(?:meta|link|img|br|hr|input)$/i.test(l)){if(t.push(">"),n&&/^script$/i.test(l))for(;c;)c.data?t.push(c.data):ae(c,t,n,r,i.slice()),c=c.nextSibling;else for(;c;)ae(c,t,n,r,i.slice()),c=c.nextSibling;t.push("</",m,">")}else t.push("/>");return;case y:case b:for(c=e.firstChild;c;)ae(c,t,n,r,i.slice()),c=c.nextSibling;return;case p:return ie(t,e.name,e.value);case d:return t.push(e.data.replace(/[<&>]/g,D));case f:return t.push("<![CDATA[",e.data,"]]>");case v:return t.push("\x3c!--",e.data,"--\x3e");case x:var T=e.publicId,R=e.systemId;if(t.push("<!DOCTYPE ",e.name),T)t.push(" PUBLIC ",T),R&&"."!=R&&t.push(" ",R),t.push(">");else if(R&&"."!=R)t.push(" SYSTEM ",R,">");else{var P=e.internalSubset;P&&t.push(" [",P,"]"),t.push(">")}return;case g:return t.push("<?",e.target," ",e.data,"?>");case h:return t.push("&",e.nodeName,";");default:t.push("??",e.nodeName)}}function se(e,t,n){var o;switch(t.nodeType){case u:(o=t.cloneNode(!1)).ownerDocument=e;case b:break;case p:n=!0}if(o||(o=t.cloneNode(!1)),o.ownerDocument=e,o.parentNode=null,n)for(var r=t.firstChild;r;)o.appendChild(se(e,r,n)),r=r.nextSibling;return o}function ce(e,t,n){var o=new t.constructor;for(var r in t){var i=t[r];"object"!=typeof i&&i!=o[r]&&(o[r]=i)}switch(t.childNodes&&(o.childNodes=new w),o.ownerDocument=e,o.nodeType){case u:var a=t.attributes,s=o.attributes=new P,c=a.length;s._ownerElement=o;for(var l=0;l<c;l++)o.setAttributeNode(ce(e,a.item(l),!0));break;case p:n=!0}if(n)for(var d=t.firstChild;d;)o.appendChild(ce(e,d,n)),d=d.nextSibling;return o}function le(e,t,n){e[t]=n}k.INVALID_STATE_ERR=(C[11]="Invalid state",11),k.SYNTAX_ERR=(C[12]="Syntax error",12),k.INVALID_MODIFICATION_ERR=(C[13]="Invalid modification",13),k.NAMESPACE_ERR=(C[14]="Invalid namespace",14),k.INVALID_ACCESS_ERR=(C[15]="Invalid access",15),O.prototype=Error.prototype,s(k,O),w.prototype={length:0,item:function(e){return this[e]||null},toString:function(e,t){for(var n=[],o=0;o<this.length;o++)ae(this[o],n,e,t);return n.join("")}},T.prototype.item=function(e){return R(this),this[e]},c(T,w),P.prototype={length:0,item:w.prototype.item,getNamedItem:function(e){for(var t=this.length;t--;){var n=this[t];if(n.nodeName==e)return n}},setNamedItem:function(e){var t=e.ownerElement;if(t&&t!=this._ownerElement)throw new O(A);var n=this.getNamedItem(e.nodeName);return N(this._ownerElement,this,e,n),n},setNamedItemNS:function(e){var t,n=e.ownerElement;if(n&&n!=this._ownerElement)throw new O(A);return t=this.getNamedItemNS(e.namespaceURI,e.localName),N(this._ownerElement,this,e,t),t},removeNamedItem:function(e){var t=this.getNamedItem(e);return B(this._ownerElement,this,t),t},removeNamedItemNS:function(e,t){var n=this.getNamedItemNS(e,t);return B(this._ownerElement,this,n),n},getNamedItemNS:function(e,t){for(var n=this.length;n--;){var o=this[n];if(o.localName==t&&o.namespaceURI==e)return o}return null}},M.prototype={hasFeature:function(e,t){return!0},createDocument:function(e,t,n){var o=new F;if(o.implementation=this,o.childNodes=new w,o.doctype=n||null,n&&o.appendChild(n),t){var r=o.createElementNS(e,t);o.appendChild(r)}return o},createDocumentType:function(e,t,n){var o=new X;return o.name=e,o.nodeName=e,o.publicId=t||"",o.systemId=n||"",o}},L.prototype={firstChild:null,lastChild:null,previousSibling:null,nextSibling:null,attributes:null,parentNode:null,childNodes:null,ownerDocument:null,nodeValue:null,namespaceURI:null,prefix:null,localName:null,insertBefore:function(e,t){return z(this,e,t)},replaceChild:function(e,t){this.insertBefore(e,t),t&&this.removeChild(t)},removeChild:function(e){return $(this,e)},appendChild:function(e){return this.insertBefore(e,null)},hasChildNodes:function(){return null!=this.firstChild},cloneNode:function(e){return ce(this.ownerDocument||this,this,e)},normalize:function(){for(var e=this.firstChild;e;){var t=e.nextSibling;t&&t.nodeType==d&&e.nodeType==d?(this.removeChild(t),e.appendData(t.data)):(e.normalize(),e=t)}},isSupported:function(e,t){return this.ownerDocument.implementation.hasFeature(e,t)},hasAttributes:function(){return this.attributes.length>0},lookupPrefix:function(e){for(var t=this;t;){var n=t._nsMap;if(n)for(var o in n)if(n[o]==e)return o;t=t.nodeType==p?t.ownerDocument:t.parentNode}return null},lookupNamespaceURI:function(e){for(var t=this;t;){var n=t._nsMap;if(n&&e in n)return n[e];t=t.nodeType==p?t.ownerDocument:t.parentNode}return null},isDefaultNamespace:function(e){return null==this.lookupPrefix(e)}},s(l,L),s(l,L.prototype),F.prototype={nodeName:"#document",nodeType:y,doctype:null,documentElement:null,_inc:1,insertBefore:function(e,t){if(e.nodeType==b){for(var n=e.firstChild;n;){var o=n.nextSibling;this.insertBefore(n,t),n=o}return e}return null==this.documentElement&&e.nodeType==u&&(this.documentElement=e),z(this,e,t),e.ownerDocument=this,e},removeChild:function(e){return this.documentElement==e&&(this.documentElement=null),$(this,e)},importNode:function(e,t){return se(this,e,t)},getElementById:function(e){var t=null;return j(this.documentElement,function(n){if(n.nodeType==u&&n.getAttribute("id")==e)return t=n,!0}),t},getElementsByClassName:function(e){var t=a(e);return new T(this,function(n){var o=[];return t.length>0&&j(n.documentElement,function(r){if(r!==n&&r.nodeType===u){var i=r.getAttribute("class");if(i){var s=e===i;if(!s){var c=a(i);s=t.every((l=c,function(e){return l&&-1!==l.indexOf(e)}))}s&&o.push(r)}}var l}),o})},createElement:function(e){var t=new K;return t.ownerDocument=this,t.nodeName=e,t.tagName=e,t.localName=e,t.childNodes=new w,(t.attributes=new P)._ownerElement=t,t},createDocumentFragment:function(){var e=new ee;return e.ownerDocument=this,e.childNodes=new w,e},createTextNode:function(e){var t=new G;return t.ownerDocument=this,t.appendData(e),t},createComment:function(e){var t=new W;return t.ownerDocument=this,t.appendData(e),t},createCDATASection:function(e){var t=new Y;return t.ownerDocument=this,t.appendData(e),t},createProcessingInstruction:function(e,t){var n=new te;return n.ownerDocument=this,n.tagName=n.target=e,n.nodeValue=n.data=t,n},createAttribute:function(e){var t=new q;return t.ownerDocument=this,t.name=e,t.nodeName=e,t.localName=e,t.specified=!0,t},createEntityReference:function(e){var t=new Z;return t.ownerDocument=this,t.nodeName=e,t},createElementNS:function(e,t){var n=new K,o=t.split(":"),r=n.attributes=new P;return n.childNodes=new w,n.ownerDocument=this,n.nodeName=t,n.tagName=t,n.namespaceURI=e,2==o.length?(n.prefix=o[0],n.localName=o[1]):n.localName=t,r._ownerElement=n,n},createAttributeNS:function(e,t){var n=new q,o=t.split(":");return n.ownerDocument=this,n.nodeName=t,n.name=t,n.namespaceURI=e,n.specified=!0,2==o.length?(n.prefix=o[0],n.localName=o[1]):n.localName=t,n}},c(F,L),K.prototype={nodeType:u,hasAttribute:function(e){return null!=this.getAttributeNode(e)},getAttribute:function(e){var t=this.getAttributeNode(e);return t&&t.value||""},getAttributeNode:function(e){return this.attributes.getNamedItem(e)},setAttribute:function(e,t){var n=this.ownerDocument.createAttribute(e);n.value=n.nodeValue=""+t,this.setAttributeNode(n)},removeAttribute:function(e){var t=this.getAttributeNode(e);t&&this.removeAttributeNode(t)},appendChild:function(e){return e.nodeType===b?this.insertBefore(e,null):(t=this,(n=e).parentNode&&n.parentNode.removeChild(n),n.parentNode=t,n.previousSibling=t.lastChild,n.nextSibling=null,n.previousSibling?n.previousSibling.nextSibling=n:t.firstChild=n,t.lastChild=n,U(t.ownerDocument,t,n),n);var t,n},setAttributeNode:function(e){return this.attributes.setNamedItem(e)},setAttributeNodeNS:function(e){return this.attributes.setNamedItemNS(e)},removeAttributeNode:function(e){return this.attributes.removeNamedItem(e.nodeName)},removeAttributeNS:function(e,t){var n=this.getAttributeNodeNS(e,t);n&&this.removeAttributeNode(n)},hasAttributeNS:function(e,t){return null!=this.getAttributeNodeNS(e,t)},getAttributeNS:function(e,t){var n=this.getAttributeNodeNS(e,t);return n&&n.value||""},setAttributeNS:function(e,t,n){var o=this.ownerDocument.createAttributeNS(e,t);o.value=o.nodeValue=""+n,this.setAttributeNode(o)},getAttributeNodeNS:function(e,t){return this.attributes.getNamedItemNS(e,t)},getElementsByTagName:function(e){return new T(this,function(t){var n=[];return j(t,function(o){o===t||o.nodeType!=u||"*"!==e&&o.tagName!=e||n.push(o)}),n})},getElementsByTagNameNS:function(e,t){return new T(this,function(n){var o=[];return j(n,function(r){r===n||r.nodeType!==u||"*"!==e&&r.namespaceURI!==e||"*"!==t&&r.localName!=t||o.push(r)}),o})}},F.prototype.getElementsByTagName=K.prototype.getElementsByTagName,F.prototype.getElementsByTagNameNS=K.prototype.getElementsByTagNameNS,c(K,L),q.prototype.nodeType=p,c(q,L),V.prototype={data:"",substringData:function(e,t){return this.data.substring(e,e+t)},appendData:function(e){e=this.data+e,this.nodeValue=this.data=e,this.length=e.length},insertData:function(e,t){this.replaceData(e,0,t)},appendChild:function(e){throw new Error(C[S])},deleteData:function(e,t){this.replaceData(e,t,"")},replaceData:function(e,t,n){n=this.data.substring(0,e)+n+this.data.substring(e+t),this.nodeValue=this.data=n,this.length=n.length}},c(V,L),G.prototype={nodeName:"#text",nodeType:d,splitText:function(e){var t=this.data,n=t.substring(e);t=t.substring(0,e),this.data=this.nodeValue=t,this.length=t.length;var o=this.ownerDocument.createTextNode(n);return this.parentNode&&this.parentNode.insertBefore(o,this.nextSibling),o}},c(G,V),W.prototype={nodeName:"#comment",nodeType:v},c(W,V),Y.prototype={nodeName:"#cdata-section",nodeType:f},c(Y,V),X.prototype.nodeType=x,c(X,L),J.prototype.nodeType=_,c(J,L),Q.prototype.nodeType=m,c(Q,L),Z.prototype.nodeType=h,c(Z,L),ee.prototype.nodeName="#document-fragment",ee.prototype.nodeType=b,c(ee,L),te.prototype.nodeType=g,c(te,L),ne.prototype.serializeToString=function(e,t,n){return oe.call(e,t,n)},L.prototype.toString=oe;try{if(Object.defineProperty){let e=function(t){switch(t.nodeType){case u:case b:var n=[];for(t=t.firstChild;t;)7!==t.nodeType&&8!==t.nodeType&&n.push(e(t)),t=t.nextSibling;return n.join("");default:return t.nodeValue}};Object.defineProperty(T.prototype,"length",{get:function(){return R(this),this.$$length}}),Object.defineProperty(L.prototype,"textContent",{get:function(){return e(this)},set:function(e){switch(this.nodeType){case u:case b:for(;this.firstChild;)this.removeChild(this.firstChild);(e||String(e))&&this.appendChild(this.ownerDocument.createTextNode(e));break;default:this.data=e,this.value=e,this.nodeValue=e}}}),le=function(e,t,n){e["$$"+t]=n}}}catch(ue){}t.DocumentType=X,t.DOMException=O,t.DOMImplementation=M,t.Element=K,t.Node=L,t.NodeList=w,t.XMLSerializer=ne},function(e,t){var n,o,r,i,a,s,c,l,u,p,d,f,h,m,g,v,y,x,b=(o=(n=n||{}).Base64,i=function(e){for(var t={},n=0,o=e.length;n<o;n++)t[e.charAt(n)]=n;return t}(r="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/"),a=String.fromCharCode,s=function(e){if(e.length<2)return(t=e.charCodeAt(0))<128?e:t<2048?a(192|t>>>6)+a(128|63&t):a(224|t>>>12&15)+a(128|t>>>6&63)+a(128|63&t);var t=65536+1024*(e.charCodeAt(0)-55296)+(e.charCodeAt(1)-56320);return a(240|t>>>18&7)+a(128|t>>>12&63)+a(128|t>>>6&63)+a(128|63&t)},c=/[\uD800-\uDBFF][\uDC00-\uDFFFF]|[^\x00-\x7F]/g,l=function(e){return e.replace(c,s)},u=function(e){var t=[0,2,1][e.length%3],n=e.charCodeAt(0)<<16|(e.length>1?e.charCodeAt(1):0)<<8|(e.length>2?e.charCodeAt(2):0);return[r.charAt(n>>>18),r.charAt(n>>>12&63),t>=2?"=":r.charAt(n>>>6&63),t>=1?"=":r.charAt(63&n)].join("")},p=n.btoa?function(e){return n.btoa(e)}:function(e){return e.replace(/[\s\S]{1,3}/g,u)},d=function(e){return p(l(e))},f=function(e,t){return t?d(String(e)).replace(/[+\/]/g,function(e){return"+"==e?"-":"_"}).replace(/=/g,""):d(String(e))},h=new RegExp(["[À-ß][-¿]","[à-ï][-¿]{2}","[ð-÷][-¿]{3}"].join("|"),"g"),m=function(e){switch(e.length){case 4:var t=((7&e.charCodeAt(0))<<18|(63&e.charCodeAt(1))<<12|(63&e.charCodeAt(2))<<6|63&e.charCodeAt(3))-65536;return a(55296+(t>>>10))+a(56320+(1023&t));case 3:return a((15&e.charCodeAt(0))<<12|(63&e.charCodeAt(1))<<6|63&e.charCodeAt(2));default:return a((31&e.charCodeAt(0))<<6|63&e.charCodeAt(1))}},g=function(e){return e.replace(h,m)},v=function(e){var t=e.length,n=t%4,o=(t>0?i[e.charAt(0)]<<18:0)|(t>1?i[e.charAt(1)]<<12:0)|(t>2?i[e.charAt(2)]<<6:0)|(t>3?i[e.charAt(3)]:0),r=[a(o>>>16),a(o>>>8&255),a(255&o)];return r.length-=[0,0,2,1][n],r.join("")},y=n.atob?function(e){return n.atob(e)}:function(e){return e.replace(/[\s\S]{1,4}/g,v)},x=function(e){return function(e){return g(y(e))}(String(e).replace(/[-_]/g,function(e){return"-"==e?"+":"/"}).replace(/[^A-Za-z0-9\+\/]/g,""))},{VERSION:"2.1.9",atob:y,btoa:p,fromBase64:x,toBase64:f,utob:l,encode:f,encodeURI:function(e){return f(e,!0)},btou:g,decode:x,noConflict:function(){var e=n.Base64;return n.Base64=o,e}});e.exports=b},function(e,t,n){function o(e){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function r(){
/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */
r=function(){return e};var e={},t=Object.prototype,n=t.hasOwnProperty,i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",s=i.asyncIterator||"@@asyncIterator",c=i.toStringTag||"@@toStringTag";function l(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{l({},"")}catch(w){l=function(e,t,n){return e[t]=n}}function u(e,t,n,o){var r=t&&t.prototype instanceof f?t:f,i=Object.create(r.prototype),a=new E(o||[]);return i._invoke=function(e,t,n){var o="suspendedStart";return function(r,i){if("executing"===o)throw new Error("Generator is already running");if("completed"===o){if("throw"===r)throw i;return{value:void 0,done:!0}}for(n.method=r,n.arg=i;;){var a=n.delegate;if(a){var s=k(a,n);if(s){if(s===d)continue;return s}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===o)throw o="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o="executing";var c=p(e,t,n);if("normal"===c.type){if(o=n.done?"completed":"suspendedYield",c.arg===d)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(o="completed",n.method="throw",n.arg=c.arg)}}}(e,n,a),i}function p(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(o){return{type:"throw",arg:o}}}e.wrap=u;var d={};function f(){}function h(){}function m(){}var g={};l(g,a,function(){return this});var v=Object.getPrototypeOf,y=v&&v(v(A([])));y&&y!==t&&n.call(y,a)&&(g=y);var x=m.prototype=f.prototype=Object.create(g);function b(e){["next","throw","return"].forEach(function(t){l(e,t,function(e){return this._invoke(t,e)})})}function _(e,t){function r(i,a,s,c){var l=p(e[i],e,a);if("throw"!==l.type){var u=l.arg,d=u.value;return d&&"object"==o(d)&&n.call(d,"__await")?t.resolve(d.__await).then(function(e){r("next",e,s,c)},function(e){r("throw",e,s,c)}):t.resolve(d).then(function(e){u.value=e,s(u)},function(e){return r("throw",e,s,c)})}c(l.arg)}var i;this._invoke=function(e,n){function o(){return new t(function(t,o){r(e,n,t,o)})}return i=i?i.then(o,o):o()}}function k(e,t){var n=e.iterator[t.method];if(void 0===n){if(t.delegate=null,"throw"===t.method){if(e.iterator.return&&(t.method="return",t.arg=void 0,k(e,t),"throw"===t.method))return d;t.method="throw",t.arg=new TypeError("The iterator does not provide a 'throw' method")}return d}var o=p(n,e.iterator,t.arg);if("throw"===o.type)return t.method="throw",t.arg=o.arg,t.delegate=null,d;var r=o.arg;return r?r.done?(t[e.resultName]=r.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,d):r:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,d)}function C(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function S(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function E(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(C,this),this.reset(!0)}function A(e){if(e){var t=e[a];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,r=function t(){for(;++o<e.length;)if(n.call(e,o))return t.value=e[o],t.done=!1,t;return t.value=void 0,t.done=!0,t};return r.next=r}}return{next:O}}function O(){return{value:void 0,done:!0}}return h.prototype=m,l(x,"constructor",m),l(m,"constructor",h),h.displayName=l(m,c,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===h||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,m):(e.__proto__=m,l(e,c,"GeneratorFunction")),e.prototype=Object.create(x),e},e.awrap=function(e){return{__await:e}},b(_.prototype),l(_.prototype,s,function(){return this}),e.AsyncIterator=_,e.async=function(t,n,o,r,i){void 0===i&&(i=Promise);var a=new _(u(t,n,o,r),i);return e.isGeneratorFunction(n)?a:a.next().then(function(e){return e.done?e.value:a.next()})},b(x),l(x,c,"Generator"),l(x,a,function(){return this}),l(x,"toString",function(){return"[object Generator]"}),e.keys=function(e){var t=[];for(var n in e)t.push(n);return t.reverse(),function n(){for(;t.length;){var o=t.pop();if(o in e)return n.value=o,n.done=!1,n}return n.done=!0,n}},e.values=A,E.prototype={constructor:E,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(S),!e)for(var t in this)"t"===t.charAt(0)&&n.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function o(n,o){return a.type="throw",a.arg=e,t.next=n,o&&(t.method="next",t.arg=void 0),!!o}for(var r=this.tryEntries.length-1;r>=0;--r){var i=this.tryEntries[r],a=i.completion;if("root"===i.tryLoc)return o("end");if(i.tryLoc<=this.prev){var s=n.call(i,"catchLoc"),c=n.call(i,"finallyLoc");if(s&&c){if(this.prev<i.catchLoc)return o(i.catchLoc,!0);if(this.prev<i.finallyLoc)return o(i.finallyLoc)}else if(s){if(this.prev<i.catchLoc)return o(i.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return o(i.finallyLoc)}}}},abrupt:function(e,t){for(var o=this.tryEntries.length-1;o>=0;--o){var r=this.tryEntries[o];if(r.tryLoc<=this.prev&&n.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var i=r;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=e,a.arg=t,i?(this.method="next",this.next=i.finallyLoc,d):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),d},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),S(n),d}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var o=n.completion;if("throw"===o.type){var r=o.arg;S(n)}return r}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,n){return this.delegate={iterator:A(e),resultName:t,nextLoc:n},"next"===this.method&&(this.arg=void 0),d}},e}function i(e,t,n,o,r,i,a){try{var s=e[i](a),c=s.value}catch(l){return void n(l)}s.done?t(c):Promise.resolve(c).then(o,r)}var a,s=n(7),c=n(22),l=null,u=function(e){return l||(l=new c({appkey:"0AND0VEVB24UBGDU",versionCode:s.version,channelID:"mp_sdk",openid:"openid",unionid:"unid",strictMode:!1,delay:e,sessionDuration:6e4})),l},p=function(){var e=function(){return(65536*(1+Math.random())|0).toString(16).substring(1)};return e()+e()+"-"+e()+"-"+e()+"-"+e()+"-"+e()+e()+e()},d=function(){return new Promise(function(e){if(wx$1.canIUse("getNetworkType"))try{wx$1.getNetworkType({success:function(t){e(t.networkType)}})}catch(t){e("can_not_get_network_type")}else e("can_not_get_network_type")})},f={devicePlatform:"",wxVersion:"",wxSystem:"",wxSdkVersion:""};(a={devicePlatform:"",wxVersion:"",wxSystem:"",wxSdkVersion:""},new Promise(function(e){if(wx$1.canIUse("getSystemInfo"))try{wx$1.getSystemInfo({success:function(t){var n=t.platform,o=t.version,r=t.system,i=t.SDKVersion;Object.assign(a,{devicePlatform:n,wxVersion:o,wxSystem:r,wxSdkVersion:i}),e(a)}})}catch(t){e({devicePlatform:"can_not_get_system_info",wxVersion:"can_not_get_system_info",wxSystem:"can_not_get_system_info",wxSdkVersion:"can_not_get_system_info"})}else e({devicePlatform:"can_not_get_system_info",wxVersion:"can_not_get_system_info",wxSystem:"can_not_get_system_info",wxSdkVersion:"can_not_get_system_info"})})).then(function(e){Object.assign(f,e)});var h=["multipartInit","multipartUpload","multipartComplete","multipartList","multipartListPart","multipartAbort"],m=["putObject","postObject","appendObject","sliceUploadFile","uploadFile","uploadFiles"].concat(h),g=["getObject"];function v(e){return e.replace(/([A-Z])/g,"_$1").toLowerCase()}var y=function(){function e(t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e);var n=t.parent,o=t.traceId,r=t.bucket,i=t.region,a=t.apiName,c=t.fileKey,l=t.fileSize,d=t.accelerate,h=t.customId,m=t.delay,g=t.deepTracker,v=r&&r.substr(r.lastIndexOf("-")+1)||"";this.parent=n,this.deepTracker=g,this.delay=m,this.params={cossdkVersion:s.version,region:i,networkType:"",host:"",accelerate:d?"Y":"N",requestPath:c||"",size:l||-1,httpMd5:0,httpSign:0,httpFull:0,name:a||"",result:"",tookTime:0,errorNode:"",errorCode:"",errorMessage:"",errorRequestId:"",errorStatusCode:0,errorServiceName:"",tracePlatform:"cos-wx-sdk-v5",traceId:o||p(),bucket:r,appid:v,partNumber:0,retryTimes:0,reqUrl:"",customId:h||"",devicePlatform:f.devicePlatform,wxVersion:f.wxVersion,wxSystem:f.wxSystem,wxSdkVersion:f.wxSdkVersion,md5StartTime:0,md5EndTime:0,signStartTime:0,signEndTime:0,httpStartTime:0,httpEndTime:0,startTime:(new Date).getTime(),endTime:0},this.beacon=u(m)}var t,n,o,a;return t=e,n=[{key:"formatResult",value:(o=r().mark(function e(t,n){var o,i,a,s,c,l,u,p,f,h,m,g,v,y,x,b,_,k,C;return r().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return h=(new Date).getTime(),m=h-this.params.startTime,e.next=4,d();case 4:if(g=e.sent,v=t?(null==t||null===(o=t.error)||void 0===o||null===(i=o.error)||void 0===i?void 0:i.Code)||"Error":"",y=t&&((null==t||null===(a=t.error)||void 0===a||null===(s=a.error)||void 0===s?void 0:s.Message)||(null==t||null===(c=t.error)||void 0===c?void 0:c.error)||(null==t?void 0:t.error))||"",x=t?null==t||null===(l=t.error)||void 0===l?void 0:l.statusCode:n.statusCode,b=t?null==t||null===(u=t.error)||void 0===u||null===(p=u.error)||void 0===p?void 0:p.Resource:"",_=t?(null==t||null===(f=t.error)||void 0===f?void 0:f.RequestId)||"":(null==n?void 0:n.RequestId)||"",k=t?_?"Server":"Client":"",Object.assign(this.params,{tookTime:m,networkType:g,httpMd5:this.params.md5EndTime-this.params.md5StartTime,httpSign:this.params.signEndTime-this.params.signStartTime,httpFull:this.params.httpEndTime-this.params.httpStartTime,result:t?"Fail":"Success",errorType:k,errorCode:v,errorStatusCode:x,errorMessage:y,errorServiceName:b,errorRequestId:_}),!t||v&&y||(this.params.fullError=t?JSON.stringify(t):""),"getObject"===this.params.name&&(this.params.size=n?n.headers&&n.headers["content-length"]:-1),this.params.reqUrl)try{C=/^http(s)?:\/\/(.*?)\//.exec(this.params.reqUrl),this.params.host=C[2]}catch(r){this.params.host=this.params.reqUrl}this.sendEvents();case 16:case"end":return e.stop()}},e,this)}),a=function(){var e=this,t=arguments;return new Promise(function(n,r){var a=o.apply(e,t);function s(e){i(a,n,r,s,c,"next",e)}function c(e){i(a,n,r,s,c,"throw",e)}s(void 0)})},function(e,t){return a.apply(this,arguments)})},{key:"setParams",value:function(e){Object.assign(this.params,e)}},{key:"sendEvents",value:function(){if(!h.includes(this.params.name)||this.deepTracker){var e,t=(e=this.params.name,m.includes(e)?"cos_upload":g.includes(e)?"cos_download":"base_service"),n=function(e){var t={},n="Success"===e.result?["tracePlatform","cossdkVersion","region","bucket","appid","networkType","host","accelerate","requestPath","partNumber","size","name","result","tookTime","errorRequestId","retryTimes","reqUrl","customId","devicePlatform","wxVersion","wxSystem","wxSdkVersion"]:["tracePlatform","cossdkVersion","region","networkType","host","accelerate","requestPath","size","httpMd5","httpSign","httpFull","name","result","tookTime","errorNode","errorCode","errorMessage","errorRequestId","errorStatusCode","errorServiceName","errorType","traceId","bucket","appid","partNumber","retryTimes","reqUrl","customId","fullError","devicePlatform","wxVersion","wxSystem","wxSdkVersion"];for(var o in e)n.includes(o)&&(t[v(o)]=e[o]);return t}(this.params);this.beacon||(this.beacon=u(this.delay||5e3)),0===this.params.delay?this.beacon&&this.beacon.onDirectUserAction(t,n):this.beacon&&this.beacon.onUserAction(t,n)}}},{key:"generateSubTracker",value:function(t){return Object.assign(t,{parent:this,deepTracker:this.deepTracker,traceId:this.params.traceId,bucket:this.params.bucket,region:this.params.region,fileKey:this.params.requestPath,customId:this.params.customId,delay:this.params.delay}),new e(t)}}],n&&function(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}(t.prototype,n),Object.defineProperty(t,"prototype",{writable:!1}),e}();e.exports=y},function(e){e.exports=JSON.parse('{"name":"cos-wx-sdk-v5","version":"1.4.6","description":"小程序 SDK for [腾讯云对象存储服务](https://cloud.tencent.com/product/cos)","main":"demo/lib/cos-wx-sdk-v5.min.js","scripts":{"dev":"cross-env NODE_ENV=development node build.js --mode=development","build":"cross-env NODE_ENV=production node build.js --mode=production","sts.js":"node server/sts.js"},"repository":{"type":"git","url":"http://github.com/tencentyun/cos-wx-sdk-v5.git"},"author":"carsonxu","license":"ISC","dependencies":{"mime":"^2.4.6","@xmldom/xmldom":"^0.8.2"},"devDependencies":{"babel-core":"6.26.3","babel-loader":"8.2.5","@babel/preset-env":"7.16.11","body-parser":"^1.18.3","cross-env":"^7.0.3","express":"^4.17.1","qcloud-cos-sts":"^3.0.2","terser-webpack-plugin":"4.2.3","webpack":"4.46.0","webpack-cli":"4.10.0"}}')},function(e,t){var n=function(e){var t={},n=function(e){return!t[e]&&(t[e]=[]),t[e]};e.on=function(e,t){n(e).push(t)},e.off=function(e,t){for(var o=n(e),r=o.length-1;r>=0;r--)t===o[r]&&o.splice(r,1)},e.emit=function(e,t){for(var o=n(e).map(function(e){return e}),r=0;r<o.length;r++)o[r](t)}};e.exports.init=n,e.exports.EventProxy=function(){n(this)}},function(e,t,n){var o,r,i=n(0),a="cos_sdk_upload_cache",s=function(){try{o.length?wx$1.setStorageSync(a,JSON.stringify(o)):wx$1.removeStorageSync(a)}catch(e){}},c=function(){if(!o){o=function(){try{var e=JSON.parse(wx$1.getStorageSync(a))}catch(t){}return e||(e=[]),e}();for(var e=!1,t=Math.round(Date.now()/1e3),n=o.length-1;n>=0;n--){var r=o[n][2];(!r||r+2592e3<t)&&(o.splice(n,1),e=!0)}e&&s()}},l=function(){r||(r=setTimeout(function(){s(),r=null},400))},u={using:{},setUsing:function(e){u.using[e]=!0},removeUsing:function(e){delete u.using[e]},getFileId:function(e,t,n,o){return e.FilePath&&e.size&&e.lastModifiedTime&&t?i.md5([e.FilePath].join("::"))+"-"+i.md5([e.size,e.mode,e.lastAccessedTime,e.lastModifiedTime,t,n,o].join("::")):null},getCopyFileId:function(e,t,n,o,r){var a=t["content-length"],s=t.etag||"",c=t["last-modified"];return e&&n?i.md5([e,a,s,c,n,o,r].join("::")):null},getUploadIdList:function(e){if(!e)return null;c();for(var t=[],n=0;n<o.length;n++)o[n][0]===e&&t.push(o[n][1]);return t.length?t:null},saveUploadId:function(e,t,n){if(c(),e){for(var r=e.substr(0,e.indexOf("-")+1),i=o.length-1;i>=0;i--){var a=o[i];(a[0]===e&&a[1]===t||e!==a[0]&&0===a[0].indexOf(r))&&o.splice(i,1)}o.unshift([e,t,Math.round(Date.now()/1e3)]),o.length>n&&o.splice(n),l()}},removeUploadId:function(e){c(),delete u.using[e];for(var t=o.length-1;t>=0;t--)o[t][1]===e&&o.splice(t,1);l()}};e.exports=u},function(e,t,n){var o=n(11);e.exports=o},function(e,t,n){var o=n(0),r=n(8),i=n(23),a=n(24),s=n(30),c=n(7),l={SecretId:"",SecretKey:"",SecurityToken:"",ChunkRetryTimes:2,FileParallelLimit:3,ChunkParallelLimit:3,ChunkSize:1048576,SliceSize:1048576,CopyChunkParallelLimit:20,CopyChunkSize:10485760,CopySliceSize:10485760,MaxPartNumber:1e4,ProgressInterval:1e3,UploadQueueSize:1e4,Domain:"",ServiceDomain:"",Protocol:"",CompatibilityMode:!1,ForcePathStyle:!1,Timeout:0,CorrectClockSkew:!0,SystemClockOffset:0,UploadCheckContentMd5:!1,UploadAddMetaMd5:!1,UploadIdCacheLimit:50,UseAccelerate:!1,ForceSignHost:!0,HttpDNSServiceId:"",SimpleUploadMethod:"postObject",EnableTracker:!1,DeepTracker:!1,TrackerDelay:5e3,CustomId:""},u=function(e){this.options=o.extend(o.clone(l),e||{}),this.options.FileParallelLimit=Math.max(1,this.options.FileParallelLimit),this.options.ChunkParallelLimit=Math.max(1,this.options.ChunkParallelLimit),this.options.ChunkRetryTimes=Math.max(0,this.options.ChunkRetryTimes),this.options.ChunkSize=Math.max(1048576,this.options.ChunkSize),this.options.CopyChunkParallelLimit=Math.max(1,this.options.CopyChunkParallelLimit),this.options.CopyChunkSize=Math.max(1048576,this.options.CopyChunkSize),this.options.CopySliceSize=Math.max(0,this.options.CopySliceSize),this.options.MaxPartNumber=Math.max(1024,Math.min(1e4,this.options.MaxPartNumber)),this.options.Timeout=Math.max(0,this.options.Timeout),this.options.AppId,this.options.SecretId&&this.options.SecretId.indexOf(" "),this.options.SecretKey&&this.options.SecretKey.indexOf(" "),r.init(this),i.init(this)};a.init(u,i),s.init(u,i),u.util={md5:o.md5,xml2json:o.xml2json,json2xml:o.json2xml},u.getAuthorization=o.getAuth,u.version=c.version,e.exports=u},function(module,exports,__webpack_require__){(function(process,global,module){var __WEBPACK_AMD_DEFINE_RESULT__;function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}(function(){var ERROR="input is invalid type",WINDOW="object"===("undefined"==typeof window?"undefined":_typeof(window)),root=WINDOW?window:{};root.JS_MD5_NO_WINDOW&&(WINDOW=!1);var WEB_WORKER=!WINDOW&&"object"===("undefined"==typeof self?"undefined":_typeof(self)),NODE_JS=!root.JS_MD5_NO_NODE_JS&&"object"===(void 0===process?"undefined":_typeof(process))&&process.versions&&process.versions.node;NODE_JS?root=global:WEB_WORKER&&(root=self);var COMMON_JS=!root.JS_MD5_NO_COMMON_JS&&"object"===_typeof(module)&&module.exports,AMD=__webpack_require__(14),ARRAY_BUFFER=!root.JS_MD5_NO_ARRAY_BUFFER&&"undefined"!=typeof ArrayBuffer,HEX_CHARS="0123456789abcdef".split(""),EXTRA=[128,32768,8388608,-2147483648],SHIFT=[0,8,16,24],OUTPUT_TYPES=["hex","array","digest","buffer","arrayBuffer","base64"],BASE64_ENCODE_CHAR="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".split(""),blocks=[],buffer8;if(ARRAY_BUFFER){var buffer=new ArrayBuffer(68);buffer8=new Uint8Array(buffer),blocks=new Uint32Array(buffer)}!root.JS_MD5_NO_NODE_JS&&Array.isArray||(Array.isArray=function(e){return"[object Array]"===Object.prototype.toString.call(e)}),!ARRAY_BUFFER||!root.JS_MD5_NO_ARRAY_BUFFER_IS_VIEW&&ArrayBuffer.isView||(ArrayBuffer.isView=function(e){return"object"===_typeof(e)&&e.buffer&&e.buffer.constructor===ArrayBuffer});var createOutputMethod=function(e){return function(t){return new Md5(!0).update(t)[e]()}},createMethod=function(){var e=createOutputMethod("hex");NODE_JS&&(e=nodeWrap(e)),e.getCtx=e.create=function(){return new Md5},e.update=function(t){return e.create().update(t)};for(var t=0;t<OUTPUT_TYPES.length;++t){var n=OUTPUT_TYPES[t];e[n]=createOutputMethod(n)}return e},nodeWrap=function nodeWrap(method){var crypto=eval("require('crypto')"),Buffer=eval("require('buffer').Buffer"),nodeMethod=function(e){if("string"==typeof e)return crypto.createHash("md5").update(e,"utf8").digest("hex");if(null==e)throw ERROR;return e.constructor===ArrayBuffer&&(e=new Uint8Array(e)),Array.isArray(e)||ArrayBuffer.isView(e)||e.constructor===Buffer?crypto.createHash("md5").update(new Buffer(e)).digest("hex"):method(e)};return nodeMethod};function Md5(e){if(e)blocks[0]=blocks[16]=blocks[1]=blocks[2]=blocks[3]=blocks[4]=blocks[5]=blocks[6]=blocks[7]=blocks[8]=blocks[9]=blocks[10]=blocks[11]=blocks[12]=blocks[13]=blocks[14]=blocks[15]=0,this.blocks=blocks,this.buffer8=buffer8;else if(ARRAY_BUFFER){var t=new ArrayBuffer(68);this.buffer8=new Uint8Array(t),this.blocks=new Uint32Array(t)}else this.blocks=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];this.h0=this.h1=this.h2=this.h3=this.start=this.bytes=this.hBytes=0,this.finalized=this.hashed=!1,this.first=!0}Md5.prototype.update=function(e){if(!this.finalized){var t,n=_typeof(e);if("string"!==n){if("object"!==n)throw ERROR;if(null===e)throw ERROR;if(!ARRAY_BUFFER||e.constructor!==ArrayBuffer&&"ArrayBuffer"!==e.constructor.name){if(!(Array.isArray(e)||ARRAY_BUFFER&&ArrayBuffer.isView(e)))throw ERROR}else e=new Uint8Array(e);t=!0}for(var o,r,i=0,a=e.length,s=this.blocks,c=this.buffer8;i<a;){if(this.hashed&&(this.hashed=!1,s[0]=s[16],s[16]=s[1]=s[2]=s[3]=s[4]=s[5]=s[6]=s[7]=s[8]=s[9]=s[10]=s[11]=s[12]=s[13]=s[14]=s[15]=0),t)if(ARRAY_BUFFER)for(r=this.start;i<a&&r<64;++i)c[r++]=e[i];else for(r=this.start;i<a&&r<64;++i)s[r>>2]|=e[i]<<SHIFT[3&r++];else if(ARRAY_BUFFER)for(r=this.start;i<a&&r<64;++i)(o=e.charCodeAt(i))<128?c[r++]=o:o<2048?(c[r++]=192|o>>6,c[r++]=128|63&o):o<55296||o>=57344?(c[r++]=224|o>>12,c[r++]=128|o>>6&63,c[r++]=128|63&o):(o=65536+((1023&o)<<10|1023&e.charCodeAt(++i)),c[r++]=240|o>>18,c[r++]=128|o>>12&63,c[r++]=128|o>>6&63,c[r++]=128|63&o);else for(r=this.start;i<a&&r<64;++i)(o=e.charCodeAt(i))<128?s[r>>2]|=o<<SHIFT[3&r++]:o<2048?(s[r>>2]|=(192|o>>6)<<SHIFT[3&r++],s[r>>2]|=(128|63&o)<<SHIFT[3&r++]):o<55296||o>=57344?(s[r>>2]|=(224|o>>12)<<SHIFT[3&r++],s[r>>2]|=(128|o>>6&63)<<SHIFT[3&r++],s[r>>2]|=(128|63&o)<<SHIFT[3&r++]):(o=65536+((1023&o)<<10|1023&e.charCodeAt(++i)),s[r>>2]|=(240|o>>18)<<SHIFT[3&r++],s[r>>2]|=(128|o>>12&63)<<SHIFT[3&r++],s[r>>2]|=(128|o>>6&63)<<SHIFT[3&r++],s[r>>2]|=(128|63&o)<<SHIFT[3&r++]);this.lastByteIndex=r,this.bytes+=r-this.start,r>=64?(this.start=r-64,this.hash(),this.hashed=!0):this.start=r}return this.bytes>4294967295&&(this.hBytes+=this.bytes/4294967296|0,this.bytes=this.bytes%4294967296),this}},Md5.prototype.finalize=function(){if(!this.finalized){this.finalized=!0;var e=this.blocks,t=this.lastByteIndex;e[t>>2]|=EXTRA[3&t],t>=56&&(this.hashed||this.hash(),e[0]=e[16],e[16]=e[1]=e[2]=e[3]=e[4]=e[5]=e[6]=e[7]=e[8]=e[9]=e[10]=e[11]=e[12]=e[13]=e[14]=e[15]=0),e[14]=this.bytes<<3,e[15]=this.hBytes<<3|this.bytes>>>29,this.hash()}},Md5.prototype.hash=function(){var e,t,n,o,r,i,a=this.blocks;this.first?t=((t=((e=((e=a[0]-680876937)<<7|e>>>25)-271733879|0)^(n=((n=(-271733879^(o=((o=(-1732584194^2004318071&e)+a[1]-117830708)<<12|o>>>20)+e|0)&(-271733879^e))+a[2]-1126478375)<<17|n>>>15)+o|0)&(o^e))+a[3]-1316259209)<<22|t>>>10)+n|0:(e=this.h0,t=this.h1,n=this.h2,t=((t+=((e=((e+=((o=this.h3)^t&(n^o))+a[0]-680876936)<<7|e>>>25)+t|0)^(n=((n+=(t^(o=((o+=(n^e&(t^n))+a[1]-389564586)<<12|o>>>20)+e|0)&(e^t))+a[2]+606105819)<<17|n>>>15)+o|0)&(o^e))+a[3]-1044525330)<<22|t>>>10)+n|0),t=((t+=((e=((e+=(o^t&(n^o))+a[4]-176418897)<<7|e>>>25)+t|0)^(n=((n+=(t^(o=((o+=(n^e&(t^n))+a[5]+1200080426)<<12|o>>>20)+e|0)&(e^t))+a[6]-1473231341)<<17|n>>>15)+o|0)&(o^e))+a[7]-45705983)<<22|t>>>10)+n|0,t=((t+=((e=((e+=(o^t&(n^o))+a[8]+1770035416)<<7|e>>>25)+t|0)^(n=((n+=(t^(o=((o+=(n^e&(t^n))+a[9]-1958414417)<<12|o>>>20)+e|0)&(e^t))+a[10]-42063)<<17|n>>>15)+o|0)&(o^e))+a[11]-1990404162)<<22|t>>>10)+n|0,t=((t+=((e=((e+=(o^t&(n^o))+a[12]+1804603682)<<7|e>>>25)+t|0)^(n=((n+=(t^(o=((o+=(n^e&(t^n))+a[13]-40341101)<<12|o>>>20)+e|0)&(e^t))+a[14]-1502002290)<<17|n>>>15)+o|0)&(o^e))+a[15]+1236535329)<<22|t>>>10)+n|0,t=((t+=((o=((o+=(t^n&((e=((e+=(n^o&(t^n))+a[1]-165796510)<<5|e>>>27)+t|0)^t))+a[6]-1069501632)<<9|o>>>23)+e|0)^e&((n=((n+=(e^t&(o^e))+a[11]+643717713)<<14|n>>>18)+o|0)^o))+a[0]-373897302)<<20|t>>>12)+n|0,t=((t+=((o=((o+=(t^n&((e=((e+=(n^o&(t^n))+a[5]-701558691)<<5|e>>>27)+t|0)^t))+a[10]+38016083)<<9|o>>>23)+e|0)^e&((n=((n+=(e^t&(o^e))+a[15]-660478335)<<14|n>>>18)+o|0)^o))+a[4]-405537848)<<20|t>>>12)+n|0,t=((t+=((o=((o+=(t^n&((e=((e+=(n^o&(t^n))+a[9]+568446438)<<5|e>>>27)+t|0)^t))+a[14]-1019803690)<<9|o>>>23)+e|0)^e&((n=((n+=(e^t&(o^e))+a[3]-187363961)<<14|n>>>18)+o|0)^o))+a[8]+1163531501)<<20|t>>>12)+n|0,t=((t+=((o=((o+=(t^n&((e=((e+=(n^o&(t^n))+a[13]-1444681467)<<5|e>>>27)+t|0)^t))+a[2]-51403784)<<9|o>>>23)+e|0)^e&((n=((n+=(e^t&(o^e))+a[7]+1735328473)<<14|n>>>18)+o|0)^o))+a[12]-1926607734)<<20|t>>>12)+n|0,t=((t+=((i=(o=((o+=((r=t^n)^(e=((e+=(r^o)+a[5]-378558)<<4|e>>>28)+t|0))+a[8]-2022574463)<<11|o>>>21)+e|0)^e)^(n=((n+=(i^t)+a[11]+1839030562)<<16|n>>>16)+o|0))+a[14]-35309556)<<23|t>>>9)+n|0,t=((t+=((i=(o=((o+=((r=t^n)^(e=((e+=(r^o)+a[1]-1530992060)<<4|e>>>28)+t|0))+a[4]+1272893353)<<11|o>>>21)+e|0)^e)^(n=((n+=(i^t)+a[7]-155497632)<<16|n>>>16)+o|0))+a[10]-1094730640)<<23|t>>>9)+n|0,t=((t+=((i=(o=((o+=((r=t^n)^(e=((e+=(r^o)+a[13]+681279174)<<4|e>>>28)+t|0))+a[0]-358537222)<<11|o>>>21)+e|0)^e)^(n=((n+=(i^t)+a[3]-722521979)<<16|n>>>16)+o|0))+a[6]+76029189)<<23|t>>>9)+n|0,t=((t+=((i=(o=((o+=((r=t^n)^(e=((e+=(r^o)+a[9]-640364487)<<4|e>>>28)+t|0))+a[12]-421815835)<<11|o>>>21)+e|0)^e)^(n=((n+=(i^t)+a[15]+530742520)<<16|n>>>16)+o|0))+a[2]-995338651)<<23|t>>>9)+n|0,t=((t+=((o=((o+=(t^((e=((e+=(n^(t|~o))+a[0]-198630844)<<6|e>>>26)+t|0)|~n))+a[7]+1126891415)<<10|o>>>22)+e|0)^((n=((n+=(e^(o|~t))+a[14]-1416354905)<<15|n>>>17)+o|0)|~e))+a[5]-57434055)<<21|t>>>11)+n|0,t=((t+=((o=((o+=(t^((e=((e+=(n^(t|~o))+a[12]+1700485571)<<6|e>>>26)+t|0)|~n))+a[3]-1894986606)<<10|o>>>22)+e|0)^((n=((n+=(e^(o|~t))+a[10]-1051523)<<15|n>>>17)+o|0)|~e))+a[1]-2054922799)<<21|t>>>11)+n|0,t=((t+=((o=((o+=(t^((e=((e+=(n^(t|~o))+a[8]+1873313359)<<6|e>>>26)+t|0)|~n))+a[15]-30611744)<<10|o>>>22)+e|0)^((n=((n+=(e^(o|~t))+a[6]-1560198380)<<15|n>>>17)+o|0)|~e))+a[13]+1309151649)<<21|t>>>11)+n|0,t=((t+=((o=((o+=(t^((e=((e+=(n^(t|~o))+a[4]-145523070)<<6|e>>>26)+t|0)|~n))+a[11]-1120210379)<<10|o>>>22)+e|0)^((n=((n+=(e^(o|~t))+a[2]+718787259)<<15|n>>>17)+o|0)|~e))+a[9]-343485551)<<21|t>>>11)+n|0,this.first?(this.h0=e+1732584193|0,this.h1=t-271733879|0,this.h2=n-1732584194|0,this.h3=o+271733878|0,this.first=!1):(this.h0=this.h0+e|0,this.h1=this.h1+t|0,this.h2=this.h2+n|0,this.h3=this.h3+o|0)},Md5.prototype.hex=function(){this.finalize();var e=this.h0,t=this.h1,n=this.h2,o=this.h3;return HEX_CHARS[e>>4&15]+HEX_CHARS[15&e]+HEX_CHARS[e>>12&15]+HEX_CHARS[e>>8&15]+HEX_CHARS[e>>20&15]+HEX_CHARS[e>>16&15]+HEX_CHARS[e>>28&15]+HEX_CHARS[e>>24&15]+HEX_CHARS[t>>4&15]+HEX_CHARS[15&t]+HEX_CHARS[t>>12&15]+HEX_CHARS[t>>8&15]+HEX_CHARS[t>>20&15]+HEX_CHARS[t>>16&15]+HEX_CHARS[t>>28&15]+HEX_CHARS[t>>24&15]+HEX_CHARS[n>>4&15]+HEX_CHARS[15&n]+HEX_CHARS[n>>12&15]+HEX_CHARS[n>>8&15]+HEX_CHARS[n>>20&15]+HEX_CHARS[n>>16&15]+HEX_CHARS[n>>28&15]+HEX_CHARS[n>>24&15]+HEX_CHARS[o>>4&15]+HEX_CHARS[15&o]+HEX_CHARS[o>>12&15]+HEX_CHARS[o>>8&15]+HEX_CHARS[o>>20&15]+HEX_CHARS[o>>16&15]+HEX_CHARS[o>>28&15]+HEX_CHARS[o>>24&15]},Md5.prototype.toString=Md5.prototype.hex,Md5.prototype.digest=function(){this.finalize();var e=this.h0,t=this.h1,n=this.h2,o=this.h3;return[255&e,e>>8&255,e>>16&255,e>>24&255,255&t,t>>8&255,t>>16&255,t>>24&255,255&n,n>>8&255,n>>16&255,n>>24&255,255&o,o>>8&255,o>>16&255,o>>24&255]},Md5.prototype.array=Md5.prototype.digest,Md5.prototype.arrayBuffer=function(){this.finalize();var e=new ArrayBuffer(16),t=new Uint32Array(e);return t[0]=this.h0,t[1]=this.h1,t[2]=this.h2,t[3]=this.h3,e},Md5.prototype.buffer=Md5.prototype.arrayBuffer,Md5.prototype.base64=function(){for(var e,t,n,o="",r=this.array(),i=0;i<15;)e=r[i++],t=r[i++],n=r[i++],o+=BASE64_ENCODE_CHAR[e>>>2]+BASE64_ENCODE_CHAR[63&(e<<4|t>>>4)]+BASE64_ENCODE_CHAR[63&(t<<2|n>>>6)]+BASE64_ENCODE_CHAR[63&n];return e=r[i],o+(BASE64_ENCODE_CHAR[e>>>2]+BASE64_ENCODE_CHAR[e<<4&63]+"==")};var exports=createMethod();COMMON_JS?module.exports=exports:(root.md5=exports,AMD&&(__WEBPACK_AMD_DEFINE_RESULT__=function(){return exports}.call(exports,__webpack_require__,exports,module),void 0===__WEBPACK_AMD_DEFINE_RESULT__||(module.exports=__WEBPACK_AMD_DEFINE_RESULT__)))})()}).call(this,__webpack_require__(13),__webpack_require__(2),__webpack_require__(3)(module))},function(e,t){var n,o,r=e.exports={};function i(){throw new Error("setTimeout has not been defined")}function a(){throw new Error("clearTimeout has not been defined")}function s(e){if(n===setTimeout)return setTimeout(e,0);if((n===i||!n)&&setTimeout)return n=setTimeout,setTimeout(e,0);try{return n(e,0)}catch(t){try{return n.call(null,e,0)}catch(o){return n.call(this,e,0)}}}!function(){try{n="function"==typeof setTimeout?setTimeout:i}catch(e){n=i}try{o="function"==typeof clearTimeout?clearTimeout:a}catch(e){o=a}}();var c,l=[],u=!1,p=-1;function d(){u&&c&&(u=!1,c.length?l=c.concat(l):p=-1,l.length&&f())}function f(){if(!u){var e=s(d);u=!0;for(var t=l.length;t;){for(c=l,l=[];++p<t;)c&&c[p].run();p=-1,t=l.length}c=null,u=!1,function(e){if(o===clearTimeout)return clearTimeout(e);if((o===a||!o)&&clearTimeout)return o=clearTimeout,clearTimeout(e);try{o(e)}catch(t){try{return o.call(null,e)}catch(n){return o.call(this,e)}}}(e)}}function h(e,t){this.fun=e,this.array=t}function m(){}r.nextTick=function(e){var t=new Array(arguments.length-1);if(arguments.length>1)for(var n=1;n<arguments.length;n++)t[n-1]=arguments[n];l.push(new h(e,t)),1!==l.length||u||s(f)},h.prototype.run=function(){this.fun.apply(null,this.array)},r.title="browser",r.browser=!0,r.env={},r.argv=[],r.version="",r.versions={},r.on=m,r.addListener=m,r.once=m,r.off=m,r.removeListener=m,r.removeAllListeners=m,r.emit=m,r.prependListener=m,r.prependOnceListener=m,r.listeners=function(e){return[]},r.binding=function(e){throw new Error("process.binding is not supported")},r.cwd=function(){return"/"},r.chdir=function(e){throw new Error("process.chdir is not supported")},r.umask=function(){return 0}},function(e,t){(function(t){e.exports=t}).call(this,{})},function(e,t){var n,o,r,i,a,s,c,l,u,p=p||function(e){var t={},n=t.lib={},o=function(){},r=n.Base={extend:function(e){o.prototype=this;var t=new o;return e&&t.mixIn(e),t.hasOwnProperty("init")||(t.init=function(){t.$super.init.apply(this,arguments)}),t.init.prototype=t,t.$super=this,t},create:function(){var e=this.extend();return e.init.apply(e,arguments),e},init:function(){},mixIn:function(e){for(var t in e)e.hasOwnProperty(t)&&(this[t]=e[t]);e.hasOwnProperty("toString")&&(this.toString=e.toString)},clone:function(){return this.init.prototype.extend(this)}},i=n.WordArray=r.extend({init:function(e,t){e=this.words=e||[],this.sigBytes=null!=t?t:4*e.length},toString:function(e){return(e||s).stringify(this)},concat:function(e){var t=this.words,n=e.words,o=this.sigBytes;if(e=e.sigBytes,this.clamp(),o%4)for(var r=0;r<e;r++)t[o+r>>>2]|=(n[r>>>2]>>>24-r%4*8&255)<<24-(o+r)%4*8;else if(65535<n.length)for(r=0;r<e;r+=4)t[o+r>>>2]=n[r>>>2];else t.push.apply(t,n);return this.sigBytes+=e,this},clamp:function(){var t=this.words,n=this.sigBytes;t[n>>>2]&=4294967295<<32-n%4*8,t.length=e.ceil(n/4)},clone:function(){var e=r.clone.call(this);return e.words=this.words.slice(0),e},random:function(t){for(var n=[],o=0;o<t;o+=4)n.push(4294967296*e.random()|0);return new i.init(n,t)}}),a=t.enc={},s=a.Hex={stringify:function(e){var t=e.words;e=e.sigBytes;for(var n=[],o=0;o<e;o++){var r=t[o>>>2]>>>24-o%4*8&255;n.push((r>>>4).toString(16)),n.push((15&r).toString(16))}return n.join("")},parse:function(e){for(var t=e.length,n=[],o=0;o<t;o+=2)n[o>>>3]|=parseInt(e.substr(o,2),16)<<24-o%8*4;return new i.init(n,t/2)}},c=a.Latin1={stringify:function(e){var t=e.words;e=e.sigBytes;for(var n=[],o=0;o<e;o++)n.push(String.fromCharCode(t[o>>>2]>>>24-o%4*8&255));return n.join("")},parse:function(e){for(var t=e.length,n=[],o=0;o<t;o++)n[o>>>2]|=(255&e.charCodeAt(o))<<24-o%4*8;return new i.init(n,t)}},l=a.Utf8={stringify:function(e){try{return decodeURIComponent(escape(c.stringify(e)))}catch(t){throw Error("Malformed UTF-8 data")}},parse:function(e){return c.parse(unescape(encodeURIComponent(e)))}},u=n.BufferedBlockAlgorithm=r.extend({reset:function(){this._data=new i.init,this._nDataBytes=0},_append:function(e){"string"==typeof e&&(e=l.parse(e)),this._data.concat(e),this._nDataBytes+=e.sigBytes},_process:function(t){var n=this._data,o=n.words,r=n.sigBytes,a=this.blockSize,s=r/(4*a);if(t=(s=t?e.ceil(s):e.max((0|s)-this._minBufferSize,0))*a,r=e.min(4*t,r),t){for(var c=0;c<t;c+=a)this._doProcessBlock(o,c);c=o.splice(0,t),n.sigBytes-=r}return new i.init(c,r)},clone:function(){var e=r.clone.call(this);return e._data=this._data.clone(),e},_minBufferSize:0});n.Hasher=u.extend({cfg:r.extend(),init:function(e){this.cfg=this.cfg.extend(e),this.reset()},reset:function(){u.reset.call(this),this._doReset()},update:function(e){return this._append(e),this._process(),this},finalize:function(e){return e&&this._append(e),this._doFinalize()},blockSize:16,_createHelper:function(e){return function(t,n){return new e.init(n).finalize(t)}},_createHmacHelper:function(e){return function(t,n){return new p.HMAC.init(e,n).finalize(t)}}});var p=t.algo={};return t}(Math);o=(a=(n=p).lib).WordArray,r=a.Hasher,i=[],a=n.algo.SHA1=r.extend({_doReset:function(){this._hash=new o.init([1732584193,4023233417,2562383102,271733878,3285377520])},_doProcessBlock:function(e,t){for(var n=this._hash.words,o=n[0],r=n[1],a=n[2],s=n[3],c=n[4],l=0;80>l;l++){if(16>l)i[l]=0|e[t+l];else{var u=i[l-3]^i[l-8]^i[l-14]^i[l-16];i[l]=u<<1|u>>>31}u=(o<<5|o>>>27)+c+i[l],u=20>l?u+(1518500249+(r&a|~r&s)):40>l?u+(1859775393+(r^a^s)):60>l?u+((r&a|r&s|a&s)-1894007588):u+((r^a^s)-899497514),c=s,s=a,a=r<<30|r>>>2,r=o,o=u}n[0]=n[0]+o|0,n[1]=n[1]+r|0,n[2]=n[2]+a|0,n[3]=n[3]+s|0,n[4]=n[4]+c|0},_doFinalize:function(){var e=this._data,t=e.words,n=8*this._nDataBytes,o=8*e.sigBytes;return t[o>>>5]|=128<<24-o%32,t[14+(o+64>>>9<<4)]=Math.floor(n/4294967296),t[15+(o+64>>>9<<4)]=n,e.sigBytes=4*t.length,this._process(),this._hash},clone:function(){var e=r.clone.call(this);return e._hash=this._hash.clone(),e}}),n.SHA1=r._createHelper(a),n.HmacSHA1=r._createHmacHelper(a),u=(l=p).enc.Utf8,l.algo.HMAC=l.lib.Base.extend({init:function(e,t){e=this._hasher=new e.init,"string"==typeof t&&(t=u.parse(t));var n=e.blockSize,o=4*n;t.sigBytes>o&&(t=e.finalize(t)),t.clamp();for(var r=this._oKey=t.clone(),i=this._iKey=t.clone(),a=r.words,s=i.words,c=0;c<n;c++)a[c]^=1549556828,s[c]^=909522486;r.sigBytes=i.sigBytes=o,this.reset()},reset:function(){var e=this._hasher;e.reset(),e.update(this._iKey)},update:function(e){return this._hasher.update(e),this},finalize:function(e){var t=this._hasher;return e=t.finalize(e),t.reset(),t.finalize(this._oKey.clone().concat(e))}}),c=(s=p).lib.WordArray,s.enc.Base64={stringify:function(e){var t=e.words,n=e.sigBytes,o=this._map;e.clamp();for(var r=[],i=0;i<n;i+=3)for(var a=(t[i>>>2]>>>24-i%4*8&255)<<16|(t[i+1>>>2]>>>24-(i+1)%4*8&255)<<8|t[i+2>>>2]>>>24-(i+2)%4*8&255,s=0;s<4&&i+.75*s<n;s++)r.push(o.charAt(a>>>6*(3-s)&63));var c=o.charAt(64);if(c)for(;r.length%4;)r.push(c);return r.join("")},parse:function(e){var t=e.length,n=this._map,o=n.charAt(64);if(o){var r=e.indexOf(o);-1!=r&&(t=r)}for(var i=[],a=0,s=0;s<t;s++)if(s%4){var l=n.indexOf(e.charAt(s-1))<<s%4*2,u=n.indexOf(e.charAt(s))>>>6-s%4*2;i[a>>>2]|=(l|u)<<24-a%4*8,a++}return c.create(i,a)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="},e.exports=p},function(e,t,n){var o=n(17).DOMParser,r=function(e){function t(e){var t=e.localName;return null==t&&(t=e.baseName),null!=t&&""!=t||(t=e.nodeName),t}function n(e){return"string"==typeof e?e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;"):e}function r(e,t,n,o){for(var r=0;r<e.length;r++){var i=e[r];if("string"==typeof i){if(i==o)break}else if(i instanceof RegExp){if(i.test(o))break}else if("function"==typeof i&&i(t,n,o))break}return r!=e.length}function i(t,n,o){"property"===e.arrayAccessForm&&(t[n]instanceof Array?t[n+"_asArray"]=t[n]:t[n+"_asArray"]=[t[n]]),!(t[n]instanceof Array)&&e.arrayAccessFormPaths.length>0&&r(e.arrayAccessFormPaths,t,n,o)&&(t[n]=[t[n]])}function a(e){var t=e.split(/[-T:+Z]/g),n=new Date(t[0],t[1]-1,t[2]),o=t[5].split(".");if(n.setHours(t[3],t[4],o[0]),o.length>1&&n.setMilliseconds(o[1]),t[6]&&t[7]){var r=60*t[6]+Number(t[7]);r=0+("-"==(/\d\d-\d\d:\d\d$/.test(e)?"-":"+")?-1*r:r),n.setMinutes(n.getMinutes()-r-n.getTimezoneOffset())}else-1!==e.indexOf("Z",e.length-1)&&(n=new Date(Date.UTC(n.getFullYear(),n.getMonth(),n.getDate(),n.getHours(),n.getMinutes(),n.getSeconds(),n.getMilliseconds())));return n}function s(t,n,o,i){return!(1==n&&e.xmlElementsFilter.length>0)||r(e.xmlElementsFilter,t,o,i)}function c(n,o){if(9==n.nodeType){for(var l=new Object,u=n.childNodes,p=0;p<u.length;p++)1==(d=u.item(p)).nodeType&&(l[f=t(d)]=c(d,f));return l}if(1==n.nodeType){for((l=new Object).__cnt=0,u=n.childNodes,p=0;p<u.length;p++){var d,f=t(d=u.item(p));if(8!=d.nodeType){var h=o+"."+f;s(l,d.nodeType,f,h)&&(l.__cnt++,null==l[f]?(l[f]=c(d,h),i(l,f,h)):(null!=l[f]&&(l[f]instanceof Array||(l[f]=[l[f]],i(l,f,h))),l[f][l[f].length]=c(d,h)))}}for(var m=0;m<n.attributes.length;m++){var g=n.attributes.item(m);l.__cnt++,l[e.attributePrefix+g.name]=g.value}var v=n.prefix;return null!=v&&""!=v&&(l.__cnt++,l.__prefix=v),null!=l["#text"]&&(l.__text=l["#text"],l.__text instanceof Array&&(l.__text=l.__text.join("\n")),e.stripWhitespaces&&(l.__text=l.__text.trim()),delete l["#text"],"property"==e.arrayAccessForm&&delete l["#text_asArray"],l.__text=function(t,n,o){if(e.datetimeAccessFormPaths.length>0){var i=o.split(".#")[0];return r(e.datetimeAccessFormPaths,t,n,i)?a(t):t}return t}(l.__text,f,o+"."+f)),null!=l["#cdata-section"]&&(l.__cdata=l["#cdata-section"],delete l["#cdata-section"],"property"==e.arrayAccessForm&&delete l["#cdata-section_asArray"]),0==l.__cnt&&"text"==e.emptyNodeForm?l="":1==l.__cnt&&null!=l.__text?l=l.__text:1!=l.__cnt||null==l.__cdata||e.keepCData?l.__cnt>1&&null!=l.__text&&e.skipEmptyTextNodesForObj&&(e.stripWhitespaces&&""==l.__text||""==l.__text.trim())&&delete l.__text:l=l.__cdata,delete l.__cnt,!e.enableToStringFunc||null==l.__text&&null==l.__cdata||(l.toString=function(){return(null!=this.__text?this.__text:"")+(null!=this.__cdata?this.__cdata:"")}),l}if(3==n.nodeType||4==n.nodeType)return n.nodeValue}function l(t,o,r,i){var a="<"+(null!=t&&null!=t.__prefix?t.__prefix+":":"")+o;if(null!=r)for(var s=0;s<r.length;s++){var c=r[s],l=t[c];e.escapeMode&&(l=n(l)),a+=" "+c.substr(e.attributePrefix.length)+"=",e.useDoubleQuotes?a+='"'+l+'"':a+="'"+l+"'"}return a+(i?"/>":">")}function u(e,t){return"</"+(null!=e.__prefix?e.__prefix+":":"")+t+">"}function p(t,n){return"property"==e.arrayAccessForm&&(r="_asArray",-1!==(o=n.toString()).indexOf(r,o.length-8))||0==n.toString().indexOf(e.attributePrefix)||0==n.toString().indexOf("__")||t[n]instanceof Function;var o,r}function d(e){var t=0;if(e instanceof Object)for(var n in e)p(e,n)||t++;return t}function f(t,n,o){return 0==e.jsonPropertiesFilter.length||""==o||r(e.jsonPropertiesFilter,t,n,o)}function h(t){var n=[];if(t instanceof Object)for(var o in t)-1==o.toString().indexOf("__")&&0==o.toString().indexOf(e.attributePrefix)&&n.push(o);return n}function m(t){var o,r,i="";return t instanceof Object?i+=(r="",null!=(o=t).__cdata&&(r+="<![CDATA["+o.__cdata+"]]>"),null!=o.__text&&(e.escapeMode?r+=n(o.__text):r+=o.__text),r):null!=t&&(e.escapeMode?i+=n(t):i+=t),i}function g(e,t){return""===e?t:e+"."+t}function v(e,t,n,o){var r="";if(0==e.length)r+=l(e,t,n,!0);else for(var i=0;i<e.length;i++)r+=l(e[i],t,h(e[i]),!1),r+=y(e[i],g(o,t)),r+=u(e[i],t);return r}function y(e,t){var n="";if(d(e)>0)for(var o in e)if(!p(e,o)&&(""==t||f(e,o,g(t,o)))){var r=e[o],i=h(r);null==r||null==r?n+=l(r,o,i,!0):r instanceof Object?r instanceof Array?n+=v(r,o,i,t):r instanceof Date?(n+=l(r,o,i,!1),n+=r.toISOString(),n+=u(r,o)):d(r)>0||null!=r.__text||null!=r.__cdata?(n+=l(r,o,i,!1),n+=y(r,g(t,o)),n+=u(r,o)):n+=l(r,o,i,!0):(n+=l(r,o,i,!1),n+=m(r),n+=u(r,o))}return n+m(e)}void 0===(e=e||{}).escapeMode&&(e.escapeMode=!0),e.attributePrefix=e.attributePrefix||"_",e.arrayAccessForm=e.arrayAccessForm||"none",e.emptyNodeForm=e.emptyNodeForm||"text",void 0===e.enableToStringFunc&&(e.enableToStringFunc=!0),e.arrayAccessFormPaths=e.arrayAccessFormPaths||[],void 0===e.skipEmptyTextNodesForObj&&(e.skipEmptyTextNodesForObj=!0),void 0===e.stripWhitespaces&&(e.stripWhitespaces=!0),e.datetimeAccessFormPaths=e.datetimeAccessFormPaths||[],void 0===e.useDoubleQuotes&&(e.useDoubleQuotes=!1),e.xmlElementsFilter=e.xmlElementsFilter||[],e.jsonPropertiesFilter=e.jsonPropertiesFilter||[],void 0===e.keepCData&&(e.keepCData=!1),this.parseXmlString=function(e){var t;if(void 0===e)return null;if(o){var n=new o,r=null;try{r=n.parseFromString("INVALID","text/xml").getElementsByTagName("parsererror")[0].namespaceURI}catch(i){r=null}try{t=n.parseFromString(e,"text/xml"),null!=r&&t.getElementsByTagNameNS(r,"parsererror").length>0&&(t=null)}catch(i){t=null}}else 0==e.indexOf("<?")&&(e=e.substr(e.indexOf("?>")+2)),(t=new ActiveXObject("Microsoft.XMLDOM")).async="false",t.loadXML(e);return t},this.asArray=function(e){return void 0===e||null==e?[]:e instanceof Array?e:[e]},this.toXmlDateTime=function(e){return e instanceof Date?e.toISOString():"number"==typeof e?new Date(e).toISOString():null},this.asDateTime=function(e){return"string"==typeof e?a(e):e},this.xml2json=function(e){return c(e)},this.xml_str2json=function(e){var t=this.parseXmlString(e);return null!=t?this.xml2json(t):null},this.json2xml_str=function(e){return y(e,"")},this.json2xml=function(e){var t=this.json2xml_str(e);return this.parseXmlString(t)},this.getVersion=function(){return"1.2.0"}};e.exports=function(e){if(!e)return null;var t=(new o).parseFromString(e,"text/xml"),n=(new r).xml2json(t);return n.html&&n.getElementsByTagName("parsererror").length?null:n}},function(e,t,n){var o=n(4);t.DOMImplementation=o.DOMImplementation,t.XMLSerializer=o.XMLSerializer,t.DOMParser=n(18).DOMParser},function(e,t,n){var o=n(1),r=n(4),i=n(19),a=n(20),s=r.DOMImplementation,c=o.NAMESPACE,l=a.ParseError,u=a.XMLReader;function p(e){return e.replace(/\r[\n\u0085]/g,"\n").replace(/[\r\u0085\u2028]/g,"\n")}function d(e){this.options=e||{locator:{}}}function f(){this.cdata=!1}function h(e,t){t.lineNumber=e.lineNumber,t.columnNumber=e.columnNumber}function m(e,t,n){return"string"==typeof e?e.substr(t,n):e.length>=t+n||t?new java.lang.String(e,t,n)+"":e}function g(e,t){e.currentElement?e.currentElement.appendChild(t):e.doc.appendChild(t)}d.prototype.parseFromString=function(e,t){var n=this.options,o=new u,r=n.domBuilder||new f,a=n.errorHandler,s=n.locator,l=n.xmlns||{},d=/\/x?html?$/.test(t),h=d?i.HTML_ENTITIES:i.XML_ENTITIES;s&&r.setDocumentLocator(s),o.errorHandler=function(e,t,n){if(!e){if(t instanceof f)return t;e=t}var o={},r=e instanceof Function;function i(t){var i=e[t];!i&&r&&(i=2==e.length?function(n){e(t,n)}:e),o[t]=i&&function(e){i("[xmldom "+t+"]\t"+e+function(e){if(e)return"\n@"+(e.systemId||"")+"#[line:"+e.lineNumber+",col:"+e.columnNumber+"]"}(n))}||function(){}}return n=n||{},i("warning"),i("error"),i("fatalError"),o}(a,r,s),o.domBuilder=n.domBuilder||r,d&&(l[""]=c.HTML),l.xml=l.xml||c.XML;var m=n.normalizeLineEndings||p;return e&&"string"==typeof e?o.parse(m(e),l,h):o.errorHandler.error("invalid doc source"),r.doc},f.prototype={startDocument:function(){this.doc=(new s).createDocument(null,null,null),this.locator&&(this.doc.documentURI=this.locator.systemId)},startElement:function(e,t,n,o){var r=this.doc,i=r.createElementNS(e,n||t),a=o.length;g(this,i),this.currentElement=i,this.locator&&h(this.locator,i);for(var s=0;s<a;s++){e=o.getURI(s);var c=o.getValue(s),l=(n=o.getQName(s),r.createAttributeNS(e,n));this.locator&&h(o.getLocator(s),l),l.value=l.nodeValue=c,i.setAttributeNode(l)}},endElement:function(e,t,n){var o=this.currentElement;o.tagName,this.currentElement=o.parentNode},startPrefixMapping:function(e,t){},endPrefixMapping:function(e){},processingInstruction:function(e,t){var n=this.doc.createProcessingInstruction(e,t);this.locator&&h(this.locator,n),g(this,n)},ignorableWhitespace:function(e,t,n){},characters:function(e,t,n){if(e=m.apply(this,arguments)){if(this.cdata)var o=this.doc.createCDATASection(e);else o=this.doc.createTextNode(e);this.currentElement?this.currentElement.appendChild(o):/^\s*$/.test(e)&&this.doc.appendChild(o),this.locator&&h(this.locator,o)}},skippedEntity:function(e){},endDocument:function(){this.doc.normalize()},setDocumentLocator:function(e){(this.locator=e)&&(e.lineNumber=0)},comment:function(e,t,n){e=m.apply(this,arguments);var o=this.doc.createComment(e);this.locator&&h(this.locator,o),g(this,o)},startCDATA:function(){this.cdata=!0},endCDATA:function(){this.cdata=!1},startDTD:function(e,t,n){var o=this.doc.implementation;if(o&&o.createDocumentType){var r=o.createDocumentType(e,t,n);this.locator&&h(this.locator,r),g(this,r),this.doc.doctype=r}},warning:function(e){},error:function(e){},fatalError:function(e){throw new l(e,this.locator)}},"endDTD,startEntity,endEntity,attributeDecl,elementDecl,externalEntityDecl,internalEntityDecl,resolveEntity,getExternalSubset,notationDecl,unparsedEntityDecl".replace(/\w+/g,function(e){f.prototype[e]=function(){return null}}),t.__DOMHandler=f,t.normalizeLineEndings=p,t.DOMParser=d},function(e,t,n){var o=n(1).freeze;t.XML_ENTITIES=o({amp:"&",apos:"'",gt:">",lt:"<",quot:'"'}),t.HTML_ENTITIES=o({lt:"<",gt:">",amp:"&",quot:'"',apos:"'",Agrave:"À",Aacute:"Á",Acirc:"Â",Atilde:"Ã",Auml:"Ä",Aring:"Å",AElig:"Æ",Ccedil:"Ç",Egrave:"È",Eacute:"É",Ecirc:"Ê",Euml:"Ë",Igrave:"Ì",Iacute:"Í",Icirc:"Î",Iuml:"Ï",ETH:"Ð",Ntilde:"Ñ",Ograve:"Ò",Oacute:"Ó",Ocirc:"Ô",Otilde:"Õ",Ouml:"Ö",Oslash:"Ø",Ugrave:"Ù",Uacute:"Ú",Ucirc:"Û",Uuml:"Ü",Yacute:"Ý",THORN:"Þ",szlig:"ß",agrave:"à",aacute:"á",acirc:"â",atilde:"ã",auml:"ä",aring:"å",aelig:"æ",ccedil:"ç",egrave:"è",eacute:"é",ecirc:"ê",euml:"ë",igrave:"ì",iacute:"í",icirc:"î",iuml:"ï",eth:"ð",ntilde:"ñ",ograve:"ò",oacute:"ó",ocirc:"ô",otilde:"õ",ouml:"ö",oslash:"ø",ugrave:"ù",uacute:"ú",ucirc:"û",uuml:"ü",yacute:"ý",thorn:"þ",yuml:"ÿ",nbsp:" ",iexcl:"¡",cent:"¢",pound:"£",curren:"¤",yen:"¥",brvbar:"¦",sect:"§",uml:"¨",copy:"©",ordf:"ª",laquo:"«",not:"¬",shy:"­­",reg:"®",macr:"¯",deg:"°",plusmn:"±",sup2:"²",sup3:"³",acute:"´",micro:"µ",para:"¶",middot:"·",cedil:"¸",sup1:"¹",ordm:"º",raquo:"»",frac14:"¼",frac12:"½",frac34:"¾",iquest:"¿",times:"×",divide:"÷",forall:"∀",part:"∂",exist:"∃",empty:"∅",nabla:"∇",isin:"∈",notin:"∉",ni:"∋",prod:"∏",sum:"∑",minus:"−",lowast:"∗",radic:"√",prop:"∝",infin:"∞",ang:"∠",and:"∧",or:"∨",cap:"∩",cup:"∪",int:"∫",there4:"∴",sim:"∼",cong:"≅",asymp:"≈",ne:"≠",equiv:"≡",le:"≤",ge:"≥",sub:"⊂",sup:"⊃",nsub:"⊄",sube:"⊆",supe:"⊇",oplus:"⊕",otimes:"⊗",perp:"⊥",sdot:"⋅",Alpha:"Α",Beta:"Β",Gamma:"Γ",Delta:"Δ",Epsilon:"Ε",Zeta:"Ζ",Eta:"Η",Theta:"Θ",Iota:"Ι",Kappa:"Κ",Lambda:"Λ",Mu:"Μ",Nu:"Ν",Xi:"Ξ",Omicron:"Ο",Pi:"Π",Rho:"Ρ",Sigma:"Σ",Tau:"Τ",Upsilon:"Υ",Phi:"Φ",Chi:"Χ",Psi:"Ψ",Omega:"Ω",alpha:"α",beta:"β",gamma:"γ",delta:"δ",epsilon:"ε",zeta:"ζ",eta:"η",theta:"θ",iota:"ι",kappa:"κ",lambda:"λ",mu:"μ",nu:"ν",xi:"ξ",omicron:"ο",pi:"π",rho:"ρ",sigmaf:"ς",sigma:"σ",tau:"τ",upsilon:"υ",phi:"φ",chi:"χ",psi:"ψ",omega:"ω",thetasym:"ϑ",upsih:"ϒ",piv:"ϖ",OElig:"Œ",oelig:"œ",Scaron:"Š",scaron:"š",Yuml:"Ÿ",fnof:"ƒ",circ:"ˆ",tilde:"˜",ensp:" ",emsp:" ",thinsp:" ",zwnj:"‌",zwj:"‍",lrm:"‎",rlm:"‏",ndash:"–",mdash:"—",lsquo:"‘",rsquo:"’",sbquo:"‚",ldquo:"“",rdquo:"”",bdquo:"„",dagger:"†",Dagger:"‡",bull:"•",hellip:"…",permil:"‰",prime:"′",Prime:"″",lsaquo:"‹",rsaquo:"›",oline:"‾",euro:"€",trade:"™",larr:"←",uarr:"↑",rarr:"→",darr:"↓",harr:"↔",crarr:"↵",lceil:"⌈",rceil:"⌉",lfloor:"⌊",rfloor:"⌋",loz:"◊",spades:"♠",clubs:"♣",hearts:"♥",diams:"♦"}),t.entityMap=t.HTML_ENTITIES},function(e,t,n){var o=n(1).NAMESPACE,r=/[A-Z_a-z\xC0-\xD6\xD8-\xF6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD]/,i=new RegExp("[\\-\\.0-9"+r.source.slice(1,-1)+"\\u00B7\\u0300-\\u036F\\u203F-\\u2040]"),a=new RegExp("^"+r.source+i.source+"*(?::"+r.source+i.source+"*)?$");function s(e,t){this.message=e,this.locator=t,Error.captureStackTrace&&Error.captureStackTrace(this,s)}function c(){}function l(e,t){return t.lineNumber=e.lineNumber,t.columnNumber=e.columnNumber,t}function u(e,t,n,r,i,a){function s(e,t,o){n.attributeNames.hasOwnProperty(e)&&a.fatalError("Attribute "+e+" redefined"),n.addValue(e,t.replace(/[\t\n\r]/g," ").replace(/&#?\w+;/g,i),o)}for(var c,l=++t,u=0;;){var p=e.charAt(l);switch(p){case"=":if(1===u)c=e.slice(t,l),u=3;else{if(2!==u)throw new Error("attribute equal must after attrName");u=3}break;case"'":case'"':if(3===u||1===u){if(1===u&&(a.warning('attribute value must after "="'),c=e.slice(t,l)),t=l+1,!((l=e.indexOf(p,t))>0))throw new Error("attribute value no end '"+p+"' match");s(c,d=e.slice(t,l),t-1),u=5}else{if(4!=u)throw new Error('attribute value must after "="');s(c,d=e.slice(t,l),t),a.warning('attribute "'+c+'" missed start quot('+p+")!!"),t=l+1,u=5}break;case"/":switch(u){case 0:n.setTagName(e.slice(t,l));case 5:case 6:case 7:u=7,n.closed=!0;case 4:case 1:case 2:break;default:throw new Error("attribute invalid close char('/')")}break;case"":return a.error("unexpected end of input"),0==u&&n.setTagName(e.slice(t,l)),l;case">":switch(u){case 0:n.setTagName(e.slice(t,l));case 5:case 6:case 7:break;case 4:case 1:"/"===(d=e.slice(t,l)).slice(-1)&&(n.closed=!0,d=d.slice(0,-1));case 2:2===u&&(d=c),4==u?(a.warning('attribute "'+d+'" missed quot(")!'),s(c,d,t)):(o.isHTML(r[""])&&d.match(/^(?:disabled|checked|selected)$/i)||a.warning('attribute "'+d+'" missed value!! "'+d+'" instead!!'),s(d,d,t));break;case 3:throw new Error("attribute value missed!!")}return l;case"":p=" ";default:if(p<=" ")switch(u){case 0:n.setTagName(e.slice(t,l)),u=6;break;case 1:c=e.slice(t,l),u=2;break;case 4:var d=e.slice(t,l);a.warning('attribute "'+d+'" missed quot(")!!'),s(c,d,t);case 5:u=6}else switch(u){case 2:n.tagName,o.isHTML(r[""])&&c.match(/^(?:disabled|checked|selected)$/i)||a.warning('attribute "'+c+'" missed value!! "'+c+'" instead2!!'),s(c,c,t),t=l,u=1;break;case 5:a.warning('attribute space is required"'+c+'"!!');case 6:u=1,t=l;break;case 3:u=4,t=l;break;case 7:throw new Error("elements closed character '/' and '>' must be connected to")}}l++}}function p(e,t,n){for(var r=e.tagName,i=null,a=e.length;a--;){var s=e[a],c=s.qName,l=s.value;if((f=c.indexOf(":"))>0)var u=s.prefix=c.slice(0,f),p=c.slice(f+1),d="xmlns"===u&&p;else p=c,u=null,d="xmlns"===c&&"";s.localName=p,!1!==d&&(null==i&&(i={},h(n,n={})),n[d]=i[d]=l,s.uri=o.XMLNS,t.startPrefixMapping(d,l))}for(a=e.length;a--;)(u=(s=e[a]).prefix)&&("xml"===u&&(s.uri=o.XML),"xmlns"!==u&&(s.uri=n[u||""]));var f;(f=r.indexOf(":"))>0?(u=e.prefix=r.slice(0,f),p=e.localName=r.slice(f+1)):(u=null,p=e.localName=r);var m=e.uri=n[u||""];if(t.startElement(m,p,r,e),!e.closed)return e.currentNSMap=n,e.localNSMap=i,!0;if(t.endElement(m,p,r),i)for(u in i)t.endPrefixMapping(u)}function d(e,t,n,o,r){if(/^(?:script|textarea)$/i.test(n)){var i=e.indexOf("</"+n+">",t),a=e.substring(t+1,i);if(/[&<]/.test(a))return/^script$/i.test(n)?(r.characters(a,0,a.length),i):(a=a.replace(/&#?\w+;/g,o),r.characters(a,0,a.length),i)}return t+1}function f(e,t,n,o){var r=o[n];return null==r&&((r=e.lastIndexOf("</"+n+">"))<t&&(r=e.lastIndexOf("</"+n)),o[n]=r),r<t}function h(e,t){for(var n in e)t[n]=e[n]}function m(e,t,n,o){if("-"===e.charAt(t+2))return"-"===e.charAt(t+3)?(r=e.indexOf("--\x3e",t+4))>t?(n.comment(e,t+4,r-t-4),r+3):(o.error("Unclosed comment"),-1):-1;if("CDATA["==e.substr(t+3,6)){var r=e.indexOf("]]>",t+9);return n.startCDATA(),n.characters(e,t+9,r-t-9),n.endCDATA(),r+3}var i=function(e,t){var n,o=[],r=/'[^']+'|"[^"]+"|[^\s<>\/=]+=?|(\/?\s*>|<)/g;for(r.lastIndex=t,r.exec(e);n=r.exec(e);)if(o.push(n),n[1])return o}(e,t),a=i.length;if(a>1&&/!doctype/i.test(i[0][0])){var s=i[1][0],c=!1,l=!1;a>3&&(/^public$/i.test(i[2][0])?(c=i[3][0],l=a>4&&i[4][0]):/^system$/i.test(i[2][0])&&(l=i[3][0]));var u=i[a-1];return n.startDTD(s,c,l),n.endDTD(),u.index+u[0].length}return-1}function g(e,t,n){var o=e.indexOf("?>",t);if(o){var r=e.substring(t,o).match(/^<\?(\S*)\s*([\s\S]*?)\s*$/);return r?(r[0].length,n.processingInstruction(r[1],r[2]),o+2):-1}return-1}function v(){this.attributeNames={}}s.prototype=new Error,s.prototype.name=s.name,c.prototype={parse:function(e,t,n){var r=this.domBuilder;r.startDocument(),h(t,t={}),function(e,t,n,r,i){function a(e){var t=e.slice(1,-1);return Object.hasOwnProperty.call(n,t)?n[t]:"#"===t.charAt(0)?function(e){if(e>65535){var t=55296+((e-=65536)>>10),n=56320+(1023&e);return String.fromCharCode(t,n)}return String.fromCharCode(e)}(parseInt(t.substr(1).replace("x","0x"))):(i.error("entity not found:"+e),e)}function c(t){if(t>S){var n=e.substring(S,t).replace(/&#?\w+;/g,a);_&&h(S),r.characters(n,0,t-S),S=t}}function h(t,n){for(;t>=x&&(n=b.exec(e));)y=n.index,x=y+n[0].length,_.lineNumber++;_.columnNumber=t-y+1}for(var y=0,x=0,b=/.*(?:\r\n?|\n)|.*$/g,_=r.locator,k=[{currentNSMap:t}],C={},S=0;;){try{var E=e.indexOf("<",S);if(E<0){if(!e.substr(S).match(/^\s*$/)){var A=r.doc,O=A.createTextNode(e.substr(S));A.appendChild(O),r.currentElement=O}return}switch(E>S&&c(E),e.charAt(E+1)){case"/":var w=e.indexOf(">",E+3),T=e.substring(E+2,w).replace(/[ \t\n\r]+$/g,""),R=k.pop();w<0?(T=e.substring(E+2).replace(/[\s<].*/,""),i.error("end tag name: "+T+" is not complete:"+R.tagName),w=E+1+T.length):T.match(/\s</)&&(T=T.replace(/[\s<].*/,""),i.error("end tag name: "+T+" maybe not complete"),w=E+1+T.length);var P=R.localNSMap,I=R.tagName==T;if(I||R.tagName&&R.tagName.toLowerCase()==T.toLowerCase()){if(r.endElement(R.uri,R.localName,T),P)for(var N in P)r.endPrefixMapping(N);I||i.fatalError("end tag name: "+T+" is not match the current start tagName:"+R.tagName)}else k.push(R);w++;break;case"?":_&&h(E),w=g(e,E,r);break;case"!":_&&h(E),w=m(e,E,r,i);break;default:_&&h(E);var B=new v,M=k[k.length-1].currentNSMap,L=(w=u(e,E,B,M,a,i),B.length);if(!B.closed&&f(e,w,B.tagName,C)&&(B.closed=!0,n.nbsp||i.warning("unclosed xml attribute")),_&&L){for(var D=l(_,{}),j=0;j<L;j++){var F=B[j];h(F.offset),F.locator=l(_,{})}r.locator=D,p(B,r,M)&&k.push(B),r.locator=_}else p(B,r,M)&&k.push(B);o.isHTML(B.uri)&&!B.closed?w=d(e,w,B.tagName,a,r):w++}}catch(H){if(H instanceof s)throw H;i.error("element parse error: "+H),w=-1}w>S?S=w:c(Math.max(E,S)+1)}}(e,t,n,r,this.errorHandler),r.endDocument()}},v.prototype={setTagName:function(e){if(!a.test(e))throw new Error("invalid tagName:"+e);this.tagName=e},addValue:function(e,t,n){if(!a.test(e))throw new Error("invalid attribute:"+e);this.attributeNames[e]=this.length,this[this.length++]={qName:e,value:t,offset:n}},length:0,getLocalName:function(e){return this[e].localName},getLocator:function(e){return this[e].locator},getQName:function(e){return this[e].qName},getURI:function(e){return this[e].uri},getValue:function(e){return this[e].value}},t.XMLReader=c,t.ParseError=s},function(e,t){function n(e){return(n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var o="a-zA-Z_À-ÖØ-öø-ÿͰ-ͽͿ-῿‌-‍⁰-↏Ⰰ-⿿、-퟿豈-﷏ﷰ-�",r=new RegExp("^([^"+o+"])|^((x|X)(m|M)(l|L))|([^"+o+"-.0-9·̀-ͯ‿⁀])","g"),i=/[^\x09\x0A\x0D\x20-\xFF\x85\xA0-\uD7FF\uE000-\uFDCF\uFDE0-\uFFFD]/gm,a=function(e){var t=[];if(e instanceof Object)for(var n in e)e.hasOwnProperty(n)&&t.push(n);return t},s=function(e,t){var o=function(e,n,o,i,a){var s=void 0!==t.indent?t.indent:"\t",c=t.prettyPrint?"\n"+new Array(i).join(s):"";t.removeIllegalNameCharacters&&(e=e.replace(r,"_"));var l=[c,"<",e,""];return n&&n.length>0?(l.push(">"),l.push(n),a&&l.push(c),l.push("</"),l.push(e),l.push(">")):l.push("/>"),l.join("")};return function e(r,s,c){var l=n(r);switch((Array.isArray?Array.isArray(r):r instanceof Array)?l="array":r instanceof Date&&(l="date"),l){case"array":var u=[];return r.map(function(t){u.push(e(t,0,c+1))}),t.prettyPrint&&u.push("\n"),u.join("");case"date":return r.toJSON?r.toJSON():r+"";case"object":var p=[];for(var d in r)if(r.hasOwnProperty(d))if(r[d]instanceof Array)for(var f in r[d])r[d].hasOwnProperty(f)&&p.push(o(d,e(r[d][f],0,c+1),0,c+1,a(r[d][f]).length));else p.push(o(d,e(r[d],0,c+1),0,c+1));return t.prettyPrint&&p.length>0&&p.push("\n"),p.join("");case"function":return r();default:return t.escape?(""+r).replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/'/g,"&apos;").replace(/"/g,"&quot;").replace(i,""):""+r}}(e,0,0)},c=function(e){var t=['<?xml version="1.0" encoding="UTF-8"'];return e&&t.push(' standalone="yes"'),t.push("?>"),t.join("")};e.exports=function(e,t){if(t||(t={xmlHeader:{standalone:!0},prettyPrint:!0,indent:"  "}),"string"==typeof e)try{e=JSON.parse(e.toString())}catch(i){return!1}var o="",r="";return t&&("object"==n(t)?(t.xmlHeader&&(o=c(!!t.xmlHeader.standalone)),void 0!==t.docType&&(r="<!DOCTYPE "+t.docType+">")):o=c()),[o,(t=t||{}).prettyPrint&&r?"\n":"",r,s(e,t)].join("").replace(/\n{2,}/g,"\n").replace(/\s+$/g,"")}},function(e,t,n){(function(e){var n,o,r,i;function a(e){return(a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e);
/*! For license information please see beacon_mp.min.js.LICENSE.txt */}i=function(){return function(e){var t={};function n(o){if(t[o])return t[o].exports;var r=t[o]={i:o,l:!1,exports:{}};return e[o].call(r.exports,r,r.exports,n),r.l=!0,r.exports}return n.m=e,n.c=t,n.d=function(e,t,o){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:o})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==a(e)&&e&&e.__esModule)return e;var o=Object.create(null);if(n.r(o),Object.defineProperty(o,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var r in e)n.d(o,r,function(t){return e[t]}.bind(null,r));return o},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=6)}([function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.BEACON_ATTA_REQUEST_URL=t.BEACON_ATTA_TOKEN=t.BEACON_ATTA_ID=t.BEACON_CONFIG_HTTPS_URL=t.BEACON_CONFIG_REQUEST_TIME=t.BEACON_CONFIG=t.BEACON_SENDING_IDS_KEY=t.BEACON_NORMAL_LOG_ID_KEY=t.BEACON_DRIECT_LOG_ID_KEY=t.BEACON_LASE_REPORT_TIME_KEY=t.BEACON_DEVICE_ID_KEY=t.BEACON_STORE_PREFIX=t.BEACON_LOG_ID_KEY=t.BEACON_IS_REALTIME_KEY=t.BEACON_DELAY_DEFAULT=t.BEACON_HTTPS_URL=t.BEACON_HTTP_URL=void 0,t.BEACON_HTTP_URL="http://oth.eve.mdt.qq.com:8080/analytics/v2_upload",t.BEACON_HTTPS_URL="https://otheve.beacon.qq.com/analytics/v2_upload",t.BEACON_DELAY_DEFAULT=3e3,t.BEACON_IS_REALTIME_KEY="A99",t.BEACON_LOG_ID_KEY="A100",t.BEACON_STORE_PREFIX="__BEACON_",t.BEACON_DEVICE_ID_KEY="__BEACON_deviceId",t.BEACON_LASE_REPORT_TIME_KEY="last_report_time",t.BEACON_DRIECT_LOG_ID_KEY="direct_log_id",t.BEACON_NORMAL_LOG_ID_KEY="normal_log_id",t.BEACON_SENDING_IDS_KEY="sending_event_ids",t.BEACON_CONFIG="beacon_config",t.BEACON_CONFIG_REQUEST_TIME="beacon_config_request_time",t.BEACON_CONFIG_HTTPS_URL="https://oth.str.beacon.qq.com/trpc.beacon.configserver.BeaconConfigService/QueryConfig",t.BEACON_ATTA_ID="00400014144",t.BEACON_ATTA_TOKEN="6478159937",t.BEACON_ATTA_REQUEST_URL="https://h.trace.qq.com/kv"},function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.getEventId=t.replaceSymbol=t.replace=t.assert=void 0;var o=n(0),r=n(9);function i(e){if("string"!=typeof e)return e;try{return e.replace(new RegExp("\\|","g"),"%7C").replace(new RegExp("\\&","g"),"%26").replace(new RegExp("\\=","g"),"%3D").replace(new RegExp("\\+","g"),"%2B")}catch(t){return""}}Object.defineProperty(t,"EventEmiter",{enumerable:!0,get:function(){return r.EventEmiter}}),t.assert=function(e,t){if(!e)throw t instanceof Error?t:new Error(t)},t.replace=function(e,t){for(var n={},o=0,r=Object.keys(e);o<r.length;o++){var a=r[o],s=e[a];if("string"==typeof s)n[i(a)]=i(s);else{if(t)throw new Error("value mast be string  !!!!");n[i(String(a))]=i(String(s))}}return n},t.replaceSymbol=i,t.getEventId=function(e){return String(e[o.BEACON_IS_REALTIME_KEY])+String(e[o.BEACON_LOG_ID_KEY])}},function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.createPipeline=void 0;var o=function(){};t.createPipeline=function(e){if(!e||!e.reduce||!e.length)throw new TypeError("createPipeline 方法需要传入至少有一个 pipe 的数组");return 1===e.length?function(t,n){e[0](t,n||o)}:e.reduce(function(e,t){return function(n,r){return void 0===r&&(r=o),e(n,function(e){return null==t?void 0:t(e,r)})}})}},function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0});var o=n(0),r=function(){function e(e,t,n,r){this.requestParams={},this.network=r,this.requestParams.attaid=o.BEACON_ATTA_ID,this.requestParams.token=o.BEACON_ATTA_TOKEN,this.requestParams.product_id=e.appkey,this.requestParams.platform=n,this.requestParams.uin=t.deviceId,this.requestParams.model="",this.requestParams.os=n,this.requestParams.app_version=e.appVersion,this.requestParams.sdk_version=t.sdkVersion,this.requestParams.error_stack=""}return e.prototype.reportError=function(e,t){this.requestParams._dc=Math.random(),this.requestParams.error_msg=t,this.requestParams.error_code=e,this.network.get(o.BEACON_ATTA_REQUEST_URL,{params:this.requestParams})},e}();t.default=r},function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.Config=void 0,t.Config=function(){}},function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0});var o=n(0),r=function(){function e(e,t,n,r){this.strategy={isEventUpOnOff:!0,httpsUploadUrl:o.BEACON_HTTPS_URL,requestInterval:30,blacklist:[],samplelist:[]},this.realSample={},this.appkey="",this.appkey=e.appkey,this.storage=n;try{var i=JSON.parse(this.storage.getItem(o.BEACON_CONFIG));i&&this.processData(i)}catch(a){}this.needRequestConfig()&&this.requestConfig(e.appVersion,t,r)}return e.prototype.requestConfig=function(e,t,n){var r=this;this.storage.setItem(o.BEACON_CONFIG_REQUEST_TIME,Date.now().toString()),n.post(o.BEACON_CONFIG_HTTPS_URL,{platformId:void 0===wx$1?"3":"4",mainAppKey:this.appkey,appVersion:e,sdkVersion:t.sdkVersion,osVersion:t.userAgent,model:"",packageName:"",params:{A3:t.deviceId}}).then(function(e){if(0==e.data.ret)try{var t=JSON.parse(e.data.beaconConfig);t&&(r.processData(t),r.storage.setItem(o.BEACON_CONFIG,e.data.beaconConfig))}catch(n){}else r.processData(null),r.storage.setItem(o.BEACON_CONFIG,"")}).catch(function(e){})},e.prototype.processData=function(e){var t,n,o,r,i;this.strategy.isEventUpOnOff=null!==(t=null==e?void 0:e.isEventUpOnOff)&&void 0!==t?t:this.strategy.isEventUpOnOff,this.strategy.httpsUploadUrl=null!==(n=null==e?void 0:e.httpsUploadUrl)&&void 0!==n?n:this.strategy.httpsUploadUrl,this.strategy.requestInterval=null!==(o=null==e?void 0:e.requestInterval)&&void 0!==o?o:this.strategy.requestInterval,this.strategy.blacklist=null!==(r=null==e?void 0:e.blacklist)&&void 0!==r?r:this.strategy.blacklist,this.strategy.samplelist=null!==(i=null==e?void 0:e.samplelist)&&void 0!==i?i:this.strategy.samplelist;for(var a=0,s=this.strategy.samplelist;a<s.length;a++){var c=s[a].split(",");2==c.length&&(this.realSample[c[0]]=c[1])}},e.prototype.needRequestConfig=function(){var e=Number(this.storage.getItem(o.BEACON_CONFIG_REQUEST_TIME));return Date.now()-e>60*this.strategy.requestInterval*1e3},e.prototype.getUploadUrl=function(){return this.strategy.httpsUploadUrl+"?appkey="+this.appkey},e.prototype.isBlackEvent=function(e){return-1!=this.strategy.blacklist.indexOf(e)},e.prototype.isEventUpOnOff=function(){return this.strategy.isEventUpOnOff},e.prototype.isSampleEvent=function(e){return!!Object.prototype.hasOwnProperty.call(this.realSample,e)&&this.realSample[e]<Math.floor(Math.random()*Math.floor(1e4))},e}();t.default=r},function(e,t,n){var o,r=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),i=this&&this.__assign||function(){return(i=Object.assign||function(e){for(var t,n=1,o=arguments.length;n<o;n++)for(var r in t=arguments[n])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e}).apply(this,arguments)},a=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.WeappOpts=void 0;var s=a(n(7)),c=a(n(3)),l=n(0),u=n(1),p=n(4),d=a(n(5)),f=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r(t,e),t}(p.Config);t.WeappOpts=f;var h=wx$1||qq,m=function(e){function t(t){var n=e.call(this,t)||this;return n.send=function(e,t,o){if(!n.config.appkey)throw new Error("please call init before");n.network.post(n.strategy.getUploadUrl(),e.data).then(function(o){100==o.data.result?n.delayTime=1e3*o.data.delayTime:n.delayTime=0,t&&t(e.data),n.cleanEvents(e.data.events)}).catch(function(t){var r=e.data.events;n.errorReport.reportError(t.code?t.code.toString():"600",t.message),o&&o(e.data),n.concatEvents(r)})},n.network=new g,n.storage=new v(t.appkey),n.initCommonInfo(t,n),n.errorReport=new c.default(n.config,n.commonInfo,"weapp",n.network),n.strategy=new d.default(n.config,n.commonInfo,n.storage,n.network),n.storage.setItem(l.BEACON_SENDING_IDS_KEY,JSON.stringify([])),n.onDirectUserAction("rqd_weapp_init",{}),setTimeout(function(){return n.lifeCycle.emit("init")},0),n.initDelayTime=t.delay?t.delay:2e3,n.cycleSend(n.initDelayTime),n}return r(t,e),t.prototype.initCommonInfo=function(e,t){h.getNetworkType({success:function(e){t.storage.setItem("nt",e.networkType)}}),this.commonInfo={deviceId:this.storage.createDeviceId(),language:"",query:"",userAgent:"",pixel:"",channelID:e.channelID?String(e.channelID):"",openid:e.openid?String(e.openid):"",unid:e.unionid?String(e.unionid):"",sdkVersion:"4.2.6-weapp"},this.config.appVersion=e.versionCode?String(e.versionCode):"",this.config.strictMode=e.strictMode},t.prototype.cycleSend=function(e){var t=this;setTimeout(function(){t.realSend(t.getEvents()),t.cycleSend(0==t.delayTime?t.initDelayTime:t.delayTime)},e)},t.prototype.onReport=function(e,t,n){var o=[],r="4.2.6-weapp_"+(n?l.BEACON_DRIECT_LOG_ID_KEY:l.BEACON_NORMAL_LOG_ID_KEY),a=Number(this.storage.getItem(r));a=a||1,t=i(i({},t),{A99:n?"Y":"N",A100:String(a),A72:this.commonInfo.sdkVersion}),a++,this.storage.setItem(r,String(a)),o.push({eventCode:e,eventTime:Date.now().toString(),mapValue:u.replace(t,this.config.strictMode)}),n&&0==this.delayTime?this.realSend(o):this.concatEvents(o)},t.prototype.realSend=function(e){var t,n;if(0!=e.length){var o=this.getSystemInfo(),r={appVersion:this.config.appVersion?u.replaceSymbol(this.config.appVersion):"",sdkVersion:"4.2.6-weapp",sdkId:"weapp",mainAppKey:this.config.appkey,platformId:4,common:u.replace(i(i({},this.additionalParams),{env:"undefined"==typeof qq?"wechat":"qq",A2:this.commonInfo.deviceId,A8:String(this.commonInfo.openid),A9:this.getSystemInfo().brand,A10:encodeURIComponent(o.model),A12:o.language,A23:this.commonInfo.channelID,A33:this.getNetworkType(),A50:String(this.commonInfo.unid),A95:o.version,A102:null===(n=null===(t=getCurrentPages())||void 0===t?void 0:t.pop())||void 0===n?void 0:n.route,A114:encodeURIComponent(o.system),A115:encodeURIComponent(o.platform),A116:o.windowWidth+"*"+o.windowHeight+"*"+o.pixelRatio,A117:this.getUserInfo(),A118:this.getLocation()}),!1),events:e};this._normalLogPipeline(r)}},t.prototype.setUserInfo=function(e){this.storage.setItem("ui",JSON.stringify(e.userInfo))},t.prototype.setLocation=function(e){this.storage.setItem("lt",JSON.stringify(e))},t.prototype.getSystemInfo=function(){return h.getSystemInfoSync()},t.prototype.getNetworkType=function(){return this.storage.getItem("nt")},t.prototype.getUserInfo=function(){return this.storage.getItem("ui")},t.prototype.getLocation=function(){return this.storage.getItem("lt")},t.prototype.concatEvents=function(e){var t=this.getLocalEvents();t=t||{};for(var n=0,o=e;n<o.length;n++){var r=o[n],i=u.getEventId(r.mapValue);this.removeSendingId(i),t[i]=r}this.setLocalEvents(t)},t.prototype.getEvents=function(){var e=[],t=[],n=this.getLocalEvents();if(!n)return e;try{t=JSON.parse(this.storage.getItem(l.BEACON_SENDING_IDS_KEY))}catch(s){}for(var o=0,r=Object.getOwnPropertyNames(n);o<r.length;o++){var i=r[o],a=n[i];-1==t.indexOf(i)&&(e.push(a),t.push(i))}return this.storage.setItem(l.BEACON_SENDING_IDS_KEY,JSON.stringify(t)),e},t.prototype.cleanEvents=function(e){for(var t=this.getLocalEvents(),n=0,o=e;n<o.length;n++){var r=o[n],i=u.getEventId(r.mapValue);this.removeSendingId(i),delete t[i]}this.setLocalEvents(t)},t.prototype.setLocalEvents=function(e){try{this.storage.setItem("BEACON_EVENT",JSON.stringify(e))}catch(t){}},t.prototype.getLocalEvents=function(){try{return JSON.parse(this.storage.getItem("BEACON_EVENT"))}catch(e){return{}}},t}(s.default);t.default=m;var g=function(){function e(){}return e.prototype.get=function(e,t){return new Promise(function(n,o){h.request({method:"GET",data:null==t?void 0:t.params,url:e,success:function(e){n(e)},fail:function(e){o(e)}})})},e.prototype.post=function(e,t,n){var o=this;return new Promise(function(r,i){h.request({method:"POST",url:e,data:t,success:function(e){var t={data:e.data,status:e.statusCode,statusText:"",headers:e.header,config:n,request:o};r(t)},fail:function(e){var t={message:e.errMsg,code:"600",request:o};i(t)}})})},e}(),v=function(){function e(e){this.appkey=e}return e.prototype.getItem=function(e){try{return h.getStorageSync(this.getStoreKey(e))}catch(t){return""}},e.prototype.removeItem=function(e){try{h.removeStorageSync(this.getStoreKey(e))}catch(t){}},e.prototype.setItem=function(e,t){try{h.setStorageSync(this.getStoreKey(e),t)}catch(n){}},e.prototype.createDeviceId=function(){var e=h.getStorageSync("beacon_u");return e&&""!=e||(e=this.getRandom(36),h.setStorageSync("beacon_u",e)),e},e.prototype.getRandom=function(e){return(1e6*Date.now()+Math.floor(1e6*Math.random())).toString(e)||""},e.prototype.getStoreKey=function(e){return"beaconV2__"+this.appkey+"_"+e},e}()},function(e,t,n){var o=this&&this.__createBinding||(Object.create?function(e,t,n,o){void 0===o&&(o=n),Object.defineProperty(e,o,{enumerable:!0,get:function(){return t[n]}})}:function(e,t,n,o){void 0===o&&(o=n),e[o]=t[n]}),r=this&&this.__exportStar||function(e,t){for(var n in e)"default"===n||t.hasOwnProperty(n)||o(t,e,n)},i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var a=i(n(8));t.default=a.default,r(n(10),t),r(n(2),t),r(n(11),t),r(n(1),t)},function(e,t,n){var o=this&&this.__assign||function(){return(o=Object.assign||function(e){for(var t,n=1,o=arguments.length;n<o;n++)for(var r in t=arguments[n])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e}).apply(this,arguments)};Object.defineProperty(t,"__esModule",{value:!0});var r=n(1),i=n(2),a=n(1),s=n(0),c=function(){function e(e){var t=this;this.lifeCycle=new r.EventEmiter,this.uploadJobQueue=[],this.additionalParams={},this.delayTime=0,this._normalLogPipeline=i.createPipeline([function(e){t.send({url:t.strategy.getUploadUrl(),data:e,method:"post",contentType:"application/json;charset=UTF-8"},function(){var n=t.config.onReportSuccess;"function"==typeof n&&n(JSON.stringify(e.events))},function(){var n=t.config.onReportFail;"function"==typeof n&&n(JSON.stringify(e.events))})}]),a.assert(Boolean(e.appkey),"appkey must be initial"),this.config=o({},e)}return e.prototype.onUserAction=function(e,t){this.preReport(e,t,!1)},e.prototype.onDirectUserAction=function(e,t){this.preReport(e,t,!0)},e.prototype.preReport=function(e,t,n){e?this.strategy.isEventUpOnOff()&&(this.strategy.isBlackEvent(e)||this.strategy.isSampleEvent(e)||this.onReport(e,t,n)):this.errorReport.reportError("602"," no eventCode")},e.prototype.addAdditionalParams=function(e){for(var t=0,n=Object.keys(e);t<n.length;t++){var o=n[t];this.additionalParams[o]=e[o]}},e.prototype.setChannelId=function(e){this.commonInfo.channelID=String(e)},e.prototype.setOpenId=function(e){this.commonInfo.openid=String(e)},e.prototype.setUnionid=function(e){this.commonInfo.unid=String(e)},e.prototype.getDeviceId=function(){return this.commonInfo.deviceId},e.prototype.getCommonInfo=function(){return this.commonInfo},e.prototype.removeSendingId=function(e){var t=JSON.parse(this.storage.getItem(s.BEACON_SENDING_IDS_KEY)),n=t.indexOf(e);-1!=n&&(t.splice(n,1),this.storage.setItem(s.BEACON_SENDING_IDS_KEY,JSON.stringify(t)))},e}();t.default=c},function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.EventEmiter=void 0;var o=function(){function e(){var e=this;this.emit=function(t,n){if(e){var o,r=e.__EventsList[t];if(null==r?void 0:r.length){r=r.slice();for(var i=0;i<r.length;i++){o=r[i];try{var a=o.callback.apply(e,[n]);if(1===o.type&&e.remove(t,o.callback),!1===a)break}catch(s){throw s}}}return e}},this.__EventsList={}}return e.prototype.indexOf=function(e,t){for(var n=0;n<e.length;n++)if(e[n].callback===t)return n;return-1},e.prototype.on=function(e,t,n){if(void 0===n&&(n=0),this){var o=this.__EventsList[e];if(o||(o=this.__EventsList[e]=[]),-1===this.indexOf(o,t)){var r={name:e,type:n||0,callback:t};return o.push(r),this}return this}},e.prototype.one=function(e,t){this.on(e,t,1)},e.prototype.remove=function(e,t){if(this){var n=this.__EventsList[e];if(!n)return null;if(!t){try{delete this.__EventsList[e]}catch(r){}return null}if(n.length){var o=this.indexOf(n,t);n.splice(o,1)}return this}},e}();t.EventEmiter=o},function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0})},function(e,t,n){var o=this&&this.__createBinding||(Object.create?function(e,t,n,o){void 0===o&&(o=n),Object.defineProperty(e,o,{enumerable:!0,get:function(){return t[n]}})}:function(e,t,n,o){void 0===o&&(o=n),e[o]=t[n]}),r=this&&this.__exportStar||function(e,t){for(var n in e)"default"===n||t.hasOwnProperty(n)||o(t,e,n)};Object.defineProperty(t,"__esModule",{value:!0}),r(n(3),t),r(n(4),t),r(n(5),t)}]).default},"object"==a(t)&&"object"==a(e)?e.exports=i():(o=[],void 0===(r="function"==typeof(n=i)?n.apply(t,o):n)||(e.exports=r))}).call(this,n(3)(e))},function(e,t,n){var o=n(9),r=n(0),i={};e.exports.transferToTaskMethod=function(e,t){i[t]=e[t],e[t]=function(e,n){e.SkipTask?i[t].call(this,e,n):this._addTask(t,e,n)}},e.exports.init=function(e){var t,n,a=[],s={},c=0,l=0,u=function(e){var t={id:e.id,Bucket:e.Bucket,Region:e.Region,Key:e.Key,FilePath:e.FilePath,state:e.state,loaded:e.loaded,size:e.size,speed:e.speed,percent:e.percent,hashPercent:e.hashPercent,error:e.error};return e.FilePath&&(t.FilePath=e.FilePath),t},p=(n=function(){t=0,e.emit("task-list-update",{list:r.map(a,u)}),e.emit("list-update",{list:r.map(a,u)})},function(){t||(t=setTimeout(n))}),d=function(){if(!(a.length<=e.options.UploadQueueSize)){for(var t=0;t<l&&t<a.length&&a.length>e.options.UploadQueueSize;){var n="waiting"===a[t].state||"checking"===a[t].state||"uploading"===a[t].state;a[t]&&n?t++:(s[a[t].id]&&delete s[a[t].id],a.splice(t,1),l--)}p()}},f=function t(){if(!(c>=e.options.FileParallelLimit)){for(;a[l]&&"waiting"!==a[l].state;)l++;if(!(l>=a.length)){var n=a[l];l++,c++,n.state="checking",n.params.onTaskStart&&n.params.onTaskStart(u(n)),!n.params.UploadData&&(n.params.UploadData={});var o=r.formatParams(n.api,n.params);i[n.api].call(e,o,function(o,r){e._isRunningTask(n.id)&&("checking"!==n.state&&"uploading"!==n.state||(n.state=o?"error":"success",o&&(n.error=o),c--,p(),t(),n.callback&&n.callback(o,r),"success"===n.state&&(n.params&&(delete n.params.UploadData,delete n.params.Body,delete n.params),delete n.callback)),d())}),p(),setTimeout(t)}}},h=function(t,n){var r=s[t];if(r){var i=r&&"waiting"===r.state,a=r&&("checking"===r.state||"uploading"===r.state);if("canceled"===n&&"canceled"!==r.state||"paused"===n&&i||"paused"===n&&a){if("paused"===n&&r.params.Body&&"function"==typeof r.params.Body.pipe)return;r.state=n,e.emit("inner-kill-task",{TaskId:t,toState:n});try{var l=r&&r.params&&r.params.UploadData.UploadId}catch(u){}"canceled"===n&&l&&o.removeUsing(l),p(),a&&(c--,f()),"canceled"===n&&(r.params&&(delete r.params.UploadData,delete r.params.Body,delete r.params),delete r.callback)}d()}};e._addTasks=function(t){r.each(t,function(t){e._addTask(t.api,t.params,t.callback,!0)}),p()},e._addTask=function(t,n,o,i){var c="postObject"===e.options.SimpleUploadMethod?"postObject":"putObject";"sliceUploadFile"!==t||r.canFileSlice()||(t=c),n=r.formatParams(t,n);var l=r.uuid();n.TaskId=l,n.onTaskReady&&n.onTaskReady(l);var u={params:n,callback:o,api:t,index:a.length,id:l,Bucket:n.Bucket,Region:n.Region,Key:n.Key,FilePath:n.FilePath||"",state:"waiting",loaded:0,size:0,speed:0,percent:0,hashPercent:0,error:null},h=n.onHashProgress;n.onHashProgress=function(t){e._isRunningTask(u.id)&&(u.hashPercent=t.percent,h&&h(t),p())};var m=n.onProgress;return n.onProgress=function(t){e._isRunningTask(u.id)&&("checking"===u.state&&(u.state="uploading"),u.loaded=t.loaded,u.size=t.total,u.speed=t.speed,u.percent=t.percent,m&&m(t),p())},r.getFileSize(t,n,function(e,t){e?o(e):(s[l]=u,a.push(u),u.size=t,!i&&p(),f(),d())}),l},e._isRunningTask=function(e){var t=s[e];return!(!t||"checking"!==t.state&&"uploading"!==t.state)},e.getTaskList=function(){return r.map(a,u)},e.cancelTask=function(e){h(e,"canceled")},e.pauseTask=function(e){h(e,"paused")},e.restartTask=function(e){var t=s[e];!t||"paused"!==t.state&&"error"!==t.state||(t.state="waiting",p(),l=Math.min(l,t.index),f())},e.isUploadRunning=function(){return c||l<a.length}}},function(e,t,n){var o=n(25),r=(n(5),n(0)),i=n(26);function a(e){var t={GrantFullControl:[],GrantWrite:[],GrantRead:[],GrantReadAcp:[],GrantWriteAcp:[],ACL:""},n={FULL_CONTROL:"GrantFullControl",WRITE:"GrantWrite",READ:"GrantRead",READ_ACP:"GrantReadAcp",WRITE_ACP:"GrantWriteAcp"},o=(e&&e.AccessControlList||{}).Grant;o&&(o=r.isArray(o)?o:[o]);var i={READ:0,WRITE:0,FULL_CONTROL:0};return o&&o.length&&r.each(o,function(o){"qcs::cam::anyone:anyone"===o.Grantee.ID||"http://cam.qcloud.com/groups/global/AllUsers"===o.Grantee.URI?i[o.Permission]=1:o.Grantee.ID!==e.Owner.ID&&t[n[o.Permission]].push('id="'+o.Grantee.ID+'"')}),i.FULL_CONTROL||i.WRITE&&i.READ?t.ACL="public-read-write":i.READ?t.ACL="public-read":t.ACL="private",r.each(n,function(e){t[e]=s(t[e].join(","))}),t}function s(e){var t,n,o=e.split(","),r={};for(t=0;t<o.length;)r[n=o[t].trim()]?o.splice(t,1):(r[n]=!0,o[t]=n,t++);return o.join(",")}function c(e){var t=e.bucket,n=t.substr(0,t.lastIndexOf("-")),o=t.substr(t.lastIndexOf("-")+1),i=e.domain,a=e.region,s=e.object;i||(i=["cn-south","cn-south-2","cn-north","cn-east","cn-southwest","sg"].indexOf(a)>-1?"{Region}.myqcloud.com":"cos.{Region}.myqcloud.com",e.ForcePathStyle||(i="{Bucket}."+i)),i=(i=i.replace(/\{\{AppId\}\}/gi,o).replace(/\{\{Bucket\}\}/gi,n).replace(/\{\{Region\}\}/gi,a).replace(/\{\{.*?\}\}/gi,"")).replace(/\{AppId\}/gi,o).replace(/\{BucketName\}/gi,n).replace(/\{Bucket\}/gi,t).replace(/\{Region\}/gi,a).replace(/\{.*?\}/gi,""),/^[a-zA-Z]+:\/\//.test(i)||(i="https://"+i),"/"===i.slice(-1)&&(i=i.slice(0,-1));var c=i;return e.ForcePathStyle&&(c+="/"+t),c+="/",s&&(c+=r.camSafeUrlEncode(s).replace(/%2F/g,"/")),e.isLocation&&(c=c.replace(/^https?:\/\//,"")),c}var l=function(e){if(!e.Bucket||!e.Region)return"";var t=void 0===e.UseAccelerate?this.options.UseAccelerate:e.UseAccelerate,n=(e.Url||c({ForcePathStyle:this.options.ForcePathStyle,protocol:this.options.Protocol,domain:this.options.Domain,bucket:e.Bucket,region:t?"accelerate":e.Region})).replace(/^https?:\/\/([^/]+)(\/.*)?$/,"$1");return new RegExp("^([a-z\\d-]+-\\d+\\.)?(cos|cosv6|ci|pic)\\.([a-z\\d-]+)\\.myqcloud\\.com$").test(n)?n:""};function u(e,t){var n=r.clone(e.Headers),o="";r.each(n,function(e,t){(""===e||["content-type","cache-control"].indexOf(t.toLowerCase())>-1)&&delete n[t],"host"===t.toLowerCase()&&(o=e)});var i=!1!==e.ForceSignHost;!o&&e.SignHost&&i&&(n.Host=e.SignHost);var a=!1,s=function(e,n){a||(a=!0,n&&n.XCosSecurityToken&&!n.SecurityToken&&((n=r.clone(n)).SecurityToken=n.XCosSecurityToken,delete n.XCosSecurityToken),t&&t(e,n))},c=this,l=e.Bucket||"",u=e.Region||"",p="name/cos:PostObject"!==e.Action&&e.Key?e.Key:"";c.options.ForcePathStyle&&l&&(p=l+"/"+p);var d="/"+p,f={},h=e.Scope;if(!h){var m=e.Action||"",g=e.ResourceKey||e.Key||"";h=e.Scope||[{action:m,bucket:l,region:u,prefix:g}]}var v=r.md5(JSON.stringify(h));c._StsCache=c._StsCache||[],function(){var e,t;for(e=c._StsCache.length-1;e>=0;e--){t=c._StsCache[e];var n=Math.round(r.getSkewTime(c.options.SystemClockOffset)/1e3)+30;if(t.StartTime&&n<t.StartTime||n>=t.ExpiredTime)c._StsCache.splice(e,1);else if(!t.ScopeLimit||t.ScopeLimit&&t.ScopeKey===v){f=t;break}}}();var y,x=function(){var t="";f.StartTime&&e.Expires?t=f.StartTime+";"+(f.StartTime+1*e.Expires):f.StartTime&&f.ExpiredTime&&(t=f.StartTime+";"+f.ExpiredTime);var o={Authorization:r.getAuth({SecretId:f.TmpSecretId,SecretKey:f.TmpSecretKey,Method:e.Method,Pathname:d,Query:e.Query,Headers:n,Expires:e.Expires,SystemClockOffset:c.options.SystemClockOffset,KeyTime:t,ForceSignHost:i}),SecurityToken:f.SecurityToken||f.XCosSecurityToken||"",Token:f.Token||"",ClientIP:f.ClientIP||"",ClientUA:f.ClientUA||""};s(null,o)},b=function(e){if(e.Authorization){var t=!1,n=e.Authorization;if(n)if(n.indexOf(" ")>-1)t=!1;else if(n.indexOf("q-sign-algorithm=")>-1&&n.indexOf("q-ak=")>-1&&n.indexOf("q-sign-time=")>-1&&n.indexOf("q-key-time=")>-1&&n.indexOf("q-url-param-list=")>-1)t=!0;else try{(n=atob(n)).indexOf("a=")>-1&&n.indexOf("k=")>-1&&n.indexOf("t=")>-1&&n.indexOf("r=")>-1&&n.indexOf("b=")>-1&&(t=!0)}catch(o){}if(!t)return r.error(new Error("getAuthorization callback params format error"))}else{if(!e.TmpSecretId)return r.error(new Error('getAuthorization callback params missing "TmpSecretId"'));if(!e.TmpSecretKey)return r.error(new Error('getAuthorization callback params missing "TmpSecretKey"'));if(!e.SecurityToken&&!e.XCosSecurityToken)return r.error(new Error('getAuthorization callback params missing "SecurityToken"'));if(!e.ExpiredTime)return r.error(new Error('getAuthorization callback params missing "ExpiredTime"'));if(e.ExpiredTime&&10!==e.ExpiredTime.toString().length)return r.error(new Error('getAuthorization callback params "ExpiredTime" should be 10 digits'));if(e.StartTime&&10!==e.StartTime.toString().length)return r.error(new Error('getAuthorization callback params "StartTime" should be 10 StartTime'))}return!1};if(f.ExpiredTime&&f.ExpiredTime-r.getSkewTime(c.options.SystemClockOffset)/1e3>60)x();else if(c.options.getAuthorization)c.options.getAuthorization.call(c,{Bucket:l,Region:u,Method:e.Method,Key:p,Pathname:d,Query:e.Query,Headers:n,Scope:h,SystemClockOffset:c.options.SystemClockOffset,ForceSignHost:i},function(e){"string"==typeof e&&(e={Authorization:e});var t=b(e);if(t)return s(t);e.Authorization?s(null,e):((f=e||{}).Scope=h,f.ScopeKey=v,c._StsCache.push(f),x())});else{if(!c.options.getSTS)return y={Authorization:r.getAuth({SecretId:e.SecretId||c.options.SecretId,SecretKey:e.SecretKey||c.options.SecretKey,Method:e.Method,Pathname:d,Query:e.Query,Headers:n,Expires:e.Expires,SystemClockOffset:c.options.SystemClockOffset,ForceSignHost:i}),SecurityToken:c.options.SecurityToken||c.options.XCosSecurityToken},s(null,y),y;c.options.getSTS.call(c,{Bucket:l,Region:u},function(e){(f=e||{}).Scope=h,f.ScopeKey=v,f.TmpSecretId||(f.TmpSecretId=f.SecretId),f.TmpSecretKey||(f.TmpSecretKey=f.SecretKey);var t=b(f);if(t)return s(t);c._StsCache.push(f),x()})}return""}function p(e){var t=!1,n=!1,o=e.headers&&(e.headers.date||e.headers.Date)||e.error&&e.error.ServerTime;try{var i=e.error.Code,a=e.error.Message;("RequestTimeTooSkewed"===i||"AccessDenied"===i&&"Request has expired"===a)&&(n=!0)}catch(c){}if(e)if(n&&o){var s=Date.parse(o);this.options.CorrectClockSkew&&Math.abs(r.getSkewTime(this.options.SystemClockOffset)-s)>=3e4&&(this.options.SystemClockOffset=s-Date.now(),t=!0)}else 5===Math.floor(e.statusCode/100)&&(t=!0);return t}function d(e,t){var n=this;!e.headers&&(e.headers={}),!e.qs&&(e.qs={}),e.VersionId&&(e.qs.versionId=e.VersionId),e.qs=r.clearKey(e.qs),e.headers&&(e.headers=r.clearKey(e.headers)),e.qs&&(e.qs=r.clearKey(e.qs));var o=r.clone(e.qs);e.action&&(o[e.action]="");var i=e.url||e.Url,a=e.SignHost||l.call(this,{Bucket:e.Bucket,Region:e.Region,Url:i}),s=e.tracker;!function r(i){var c=n.options.SystemClockOffset;s&&s.setParams({signStartTime:(new Date).getTime(),retryTimes:i-1}),u.call(n,{Bucket:e.Bucket||"",Region:e.Region||"",Method:e.method,Key:e.Key,Query:o,Headers:e.headers,SignHost:a,Action:e.Action,ResourceKey:e.ResourceKey,Scope:e.Scope,ForceSignHost:n.options.ForceSignHost},function(o,a){o?t(o):(s&&s.setParams({signEndTime:(new Date).getTime(),httpStartTime:(new Date).getTime()}),e.AuthData=a,f.call(n,e,function(o,a){s&&s.setParams({httpEndTime:(new Date).getTime()}),o&&i<2&&(c!==n.options.SystemClockOffset||p.call(n,o))?(e.headers&&(delete e.headers.Authorization,delete e.headers.token,delete e.headers.clientIP,delete e.headers.clientUA,e.headers["x-cos-security-token"]&&delete e.headers["x-cos-security-token"],e.headers["x-ci-security-token"]&&delete e.headers["x-ci-security-token"]),r(i+1)):t(o,a)}))})}(1)}function f(e,t){var n=this,i=e.TaskId;if(!i||n._isRunningTask(i)){var a=e.Bucket,s=e.Region,l=e.Key,u=e.method||"GET",p=e.url||e.Url,d=e.body,f=e.json,h=e.rawBody,m=n.options.HttpDNSServiceId;n.options.UseAccelerate&&(s="accelerate"),p=p||c({ForcePathStyle:n.options.ForcePathStyle,protocol:n.options.Protocol,domain:n.options.Domain,bucket:a,region:s,object:l}),e.action&&(p=p+"?"+e.action),e.qsStr&&(p=p.indexOf("?")>-1?p+"&"+e.qsStr:p+"?"+e.qsStr);var g={method:u,url:p,headers:e.headers,qs:e.qs,filePath:e.filePath,body:d,json:f,httpDNSServiceId:m},v="x-cos-security-token";r.isCIHost(p)&&(v="x-ci-security-token"),g.headers.Authorization=e.AuthData.Authorization,e.AuthData.Token&&(g.headers.token=e.AuthData.Token),e.AuthData.ClientIP&&(g.headers.clientIP=e.AuthData.ClientIP),e.AuthData.ClientUA&&(g.headers.clientUA=e.AuthData.ClientUA),e.AuthData.SecurityToken&&(g.headers[v]=e.AuthData.SecurityToken),g.headers&&(g.headers=r.clearKey(g.headers)),g=r.clearKey(g),e.onProgress&&"function"==typeof e.onProgress&&(g.onProgress=function(t){if(!i||n._isRunningTask(i)){var o=t?t.loaded:0;e.onProgress({loaded:o,total:t.total})}}),this.options.Timeout&&(g.timeout=this.options.Timeout),n.options.ForcePathStyle&&(g.pathStyle=n.options.ForcePathStyle),n.emit("before-send",g);var y=g.url.includes("accelerate."),x=g.qs?Object.keys(g.qs).map(function(e){return"".concat(e,"=").concat(g.qs[e])}).join("&"):"",b=x?g.url+"?"+x:g.url;e.tracker&&e.tracker.setParams({reqUrl:b,accelerate:y?"Y":"N"}),e.tracker&&e.tracker.parent&&e.tracker.parent.setParams({reqUrl:b,accelerate:y?"Y":"N"});var _=o(g,function(e,o,a){if("abort"!==e){var s,c=function(e,a){if(i&&n.off("inner-kill-task",k),!s){s=!0;var c={};o&&o.statusCode&&(c.statusCode=o.statusCode),o&&o.headers&&(c.headers=o.headers),e?(e=r.extend(e||{},c),t(e,null)):(a=r.extend(a||{},c),t(null,a)),_=null}};if(e)c({error:e});else{var l;if(h)(l={}).body=a;else try{l=a&&a.indexOf("<")>-1&&a.indexOf(">")>-1&&r.xml2json(a)||{}}catch(p){l=a||{}}var u=o.statusCode;2===Math.floor(u/100)?l.Error?c({error:l.Error}):c(null,l):c({error:l.Error||l})}}}),k=function e(t){t.TaskId===i&&(_&&_.abort&&_.abort(),n.off("inner-kill-task",e))};i&&n.on("inner-kill-task",k)}}var h={getService:function(e,t){"function"==typeof e&&(t=e,e={});var n=this.options.ServiceDomain,o=e.Region;n?(n=n.replace(/\{\{Region\}\}/gi,o||"").replace(/\{\{.*?\}\}/gi,""),/^[a-zA-Z]+:\/\//.test(n)||(n="https://"+n),"/"===n.slice(-1)&&(n=n.slice(0,-1))):n=o?"https://cos."+o+".myqcloud.com":"https://service.cos.myqcloud.com",n.replace(/^https?:\/\/([^/]+)(\/.*)?$/,"$1"),d.call(this,{Action:"name/cos:GetService",url:n,method:"GET",headers:e.Headers},function(e,n){if(e)return t(e);var o=n&&n.ListAllMyBucketsResult&&n.ListAllMyBucketsResult.Buckets&&n.ListAllMyBucketsResult.Buckets.Bucket||[];o=r.isArray(o)?o:[o];var i=n&&n.ListAllMyBucketsResult&&n.ListAllMyBucketsResult.Owner||{};t(null,{Buckets:o,Owner:i,statusCode:n.statusCode,headers:n.headers})})},putBucket:function(e,t){var n=this,o="";if(e.BucketAZConfig){var i={BucketAZConfig:e.BucketAZConfig};o=r.json2xml({CreateBucketConfiguration:i})}d.call(this,{Action:"name/cos:PutBucket",method:"PUT",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,body:o},function(o,r){if(o)return t(o);var i=c({protocol:n.options.Protocol,domain:n.options.Domain,bucket:e.Bucket,region:e.Region,isLocation:!0});t(null,{Location:i,statusCode:r.statusCode,headers:r.headers})})},headBucket:function(e,t){d.call(this,{Action:"name/cos:HeadBucket",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,method:"HEAD"},function(e,n){t(e,n)})},getBucket:function(e,t){var n={};n.prefix=e.Prefix||"",n.delimiter=e.Delimiter,n.marker=e.Marker,n["max-keys"]=e.MaxKeys,n["encoding-type"]=e.EncodingType,d.call(this,{Action:"name/cos:GetBucket",ResourceKey:n.prefix,method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,qs:n},function(e,n){if(e)return t(e);var o=n.ListBucketResult||{},i=o.Contents||[],a=o.CommonPrefixes||[];i=r.isArray(i)?i:[i],a=r.isArray(a)?a:[a];var s=r.clone(o);r.extend(s,{Contents:i,CommonPrefixes:a,statusCode:n.statusCode,headers:n.headers}),t(null,s)})},deleteBucket:function(e,t){d.call(this,{Action:"name/cos:DeleteBucket",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,method:"DELETE"},function(e,n){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:n.statusCode,headers:n.headers})})},putBucketAcl:function(e,t){var n=e.Headers,o="";if(e.AccessControlPolicy){var i=r.clone(e.AccessControlPolicy||{}),a=i.Grants||i.Grant;a=r.isArray(a)?a:[a],delete i.Grant,delete i.Grants,i.AccessControlList={Grant:a},o=r.json2xml({AccessControlPolicy:i}),n["Content-Type"]="application/xml",n["Content-MD5"]=r.binaryBase64(r.md5(o))}r.each(n,function(e,t){0===t.indexOf("x-cos-grant-")&&(n[t]=s(n[t]))}),d.call(this,{Action:"name/cos:PutBucketACL",method:"PUT",Bucket:e.Bucket,Region:e.Region,headers:n,action:"acl",body:o},function(e,n){if(e)return t(e);t(null,{statusCode:n.statusCode,headers:n.headers})})},getBucketAcl:function(e,t){d.call(this,{Action:"name/cos:GetBucketACL",method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"acl"},function(e,n){if(e)return t(e);var o=n.AccessControlPolicy||{},i=o.Owner||{},s=o.AccessControlList.Grant||[];s=r.isArray(s)?s:[s];var c=a(o);n.headers&&n.headers["x-cos-acl"]&&(c.ACL=n.headers["x-cos-acl"]),c=r.extend(c,{Owner:i,Grants:s,statusCode:n.statusCode,headers:n.headers}),t(null,c)})},putBucketCors:function(e,t){var n=(e.CORSConfiguration||{}).CORSRules||e.CORSRules||[];n=r.clone(r.isArray(n)?n:[n]),r.each(n,function(e){r.each(["AllowedOrigin","AllowedHeader","AllowedMethod","ExposeHeader"],function(t){var n=t+"s",o=e[n]||e[t]||[];delete e[n],e[t]=r.isArray(o)?o:[o]})});var o={CORSRule:n};e.ResponseVary&&(o.ResponseVary=e.ResponseVary);var i=r.json2xml({CORSConfiguration:o}),a=e.Headers;a["Content-Type"]="application/xml",a["Content-MD5"]=r.binaryBase64(r.md5(i)),d.call(this,{Action:"name/cos:PutBucketCORS",method:"PUT",Bucket:e.Bucket,Region:e.Region,body:i,action:"cors",headers:a},function(e,n){if(e)return t(e);t(null,{statusCode:n.statusCode,headers:n.headers})})},getBucketCors:function(e,t){d.call(this,{Action:"name/cos:GetBucketCORS",method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"cors"},function(e,n){if(e)if(404===e.statusCode&&e.error&&"NoSuchCORSConfiguration"===e.error.Code){var o={CORSRules:[],statusCode:e.statusCode};e.headers&&(o.headers=e.headers),t(null,o)}else t(e);else{var i=n.CORSConfiguration||{},a=i.CORSRules||i.CORSRule||[];a=r.clone(r.isArray(a)?a:[a]);var s=i.ResponseVary;r.each(a,function(e){r.each(["AllowedOrigin","AllowedHeader","AllowedMethod","ExposeHeader"],function(t){var n=t+"s",o=e[n]||e[t]||[];delete e[t],e[n]=r.isArray(o)?o:[o]})}),t(null,{CORSRules:a,ResponseVary:s,statusCode:n.statusCode,headers:n.headers})}})},deleteBucketCors:function(e,t){d.call(this,{Action:"name/cos:DeleteBucketCORS",method:"DELETE",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"cors"},function(e,n){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:n.statusCode||e.statusCode,headers:n.headers})})},getBucketLocation:function(e,t){d.call(this,{Action:"name/cos:GetBucketLocation",method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"location"},function(e,n){if(e)return t(e);t(null,n)})},getBucketPolicy:function(e,t){d.call(this,{Action:"name/cos:GetBucketPolicy",method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"policy",rawBody:!0},function(e,n){if(e)return e.statusCode&&403===e.statusCode?t({ErrorStatus:"Access Denied"}):e.statusCode&&405===e.statusCode?t({ErrorStatus:"Method Not Allowed"}):e.statusCode&&404===e.statusCode?t({ErrorStatus:"Policy Not Found"}):t(e);var o={};try{o=JSON.parse(n.body)}catch(r){}t(null,{Policy:o,statusCode:n.statusCode,headers:n.headers})})},putBucketPolicy:function(e,t){var n=e.Policy,o=n;try{"string"==typeof n?n=JSON.parse(o):o=JSON.stringify(n)}catch(a){t({error:"Policy format error"})}var i=e.Headers;i["Content-Type"]="application/json",i["Content-MD5"]=r.binaryBase64(r.md5(o)),d.call(this,{Action:"name/cos:PutBucketPolicy",method:"PUT",Bucket:e.Bucket,Region:e.Region,action:"policy",body:o,headers:i,json:!0},function(e,n){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:n.statusCode,headers:n.headers})})},deleteBucketPolicy:function(e,t){d.call(this,{Action:"name/cos:DeleteBucketPolicy",method:"DELETE",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"policy"},function(e,n){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:n.statusCode||e.statusCode,headers:n.headers})})},putBucketTagging:function(e,t){var n=e.Tagging||{},o=n.TagSet||n.Tags||e.Tags||[];o=r.clone(r.isArray(o)?o:[o]);var i=r.json2xml({Tagging:{TagSet:{Tag:o}}}),a=e.Headers;a["Content-Type"]="application/xml",a["Content-MD5"]=r.binaryBase64(r.md5(i)),d.call(this,{Action:"name/cos:PutBucketTagging",method:"PUT",Bucket:e.Bucket,Region:e.Region,body:i,action:"tagging",headers:a},function(e,n){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:n.statusCode,headers:n.headers})})},getBucketTagging:function(e,t){d.call(this,{Action:"name/cos:GetBucketTagging",method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"tagging"},function(e,n){if(e)if(404!==e.statusCode||!e.error||"Not Found"!==e.error&&"NoSuchTagSet"!==e.error.Code)t(e);else{var o={Tags:[],statusCode:e.statusCode};e.headers&&(o.headers=e.headers),t(null,o)}else{var i=[];try{i=n.Tagging.TagSet.Tag||[]}catch(a){}i=r.clone(r.isArray(i)?i:[i]),t(null,{Tags:i,statusCode:n.statusCode,headers:n.headers})}})},deleteBucketTagging:function(e,t){d.call(this,{Action:"name/cos:DeleteBucketTagging",method:"DELETE",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"tagging"},function(e,n){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:n.statusCode,headers:n.headers})})},putBucketLifecycle:function(e,t){var n=(e.LifecycleConfiguration||{}).Rules||e.Rules||[];n=r.clone(n);var o=r.json2xml({LifecycleConfiguration:{Rule:n}}),i=e.Headers;i["Content-Type"]="application/xml",i["Content-MD5"]=r.binaryBase64(r.md5(o)),d.call(this,{Action:"name/cos:PutBucketLifecycle",method:"PUT",Bucket:e.Bucket,Region:e.Region,body:o,action:"lifecycle",headers:i},function(e,n){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:n.statusCode,headers:n.headers})})},getBucketLifecycle:function(e,t){d.call(this,{Action:"name/cos:GetBucketLifecycle",method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"lifecycle"},function(e,n){if(e)if(404===e.statusCode&&e.error&&"NoSuchLifecycleConfiguration"===e.error.Code){var o={Rules:[],statusCode:e.statusCode};e.headers&&(o.headers=e.headers),t(null,o)}else t(e);else{var i=[];try{i=n.LifecycleConfiguration.Rule||[]}catch(a){}i=r.clone(r.isArray(i)?i:[i]),t(null,{Rules:i,statusCode:n.statusCode,headers:n.headers})}})},deleteBucketLifecycle:function(e,t){d.call(this,{Action:"name/cos:DeleteBucketLifecycle",method:"DELETE",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"lifecycle"},function(e,n){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:n.statusCode,headers:n.headers})})},putBucketVersioning:function(e,t){if(e.VersioningConfiguration){var n=e.VersioningConfiguration||{},o=r.json2xml({VersioningConfiguration:n}),i=e.Headers;i["Content-Type"]="application/xml",i["Content-MD5"]=r.binaryBase64(r.md5(o)),d.call(this,{Action:"name/cos:PutBucketVersioning",method:"PUT",Bucket:e.Bucket,Region:e.Region,body:o,action:"versioning",headers:i},function(e,n){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:n.statusCode,headers:n.headers})})}else t({error:"missing param VersioningConfiguration"})},getBucketVersioning:function(e,t){d.call(this,{Action:"name/cos:GetBucketVersioning",method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"versioning"},function(e,n){e||!n.VersioningConfiguration&&(n.VersioningConfiguration={}),t(e,n)})},putBucketReplication:function(e,t){var n=r.clone(e.ReplicationConfiguration),o=r.json2xml({ReplicationConfiguration:n});o=(o=o.replace(/<(\/?)Rules>/gi,"<$1Rule>")).replace(/<(\/?)Tags>/gi,"<$1Tag>");var i=e.Headers;i["Content-Type"]="application/xml",i["Content-MD5"]=r.binaryBase64(r.md5(o)),d.call(this,{Action:"name/cos:PutBucketReplication",method:"PUT",Bucket:e.Bucket,Region:e.Region,body:o,action:"replication",headers:i},function(e,n){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:n.statusCode,headers:n.headers})})},getBucketReplication:function(e,t){d.call(this,{Action:"name/cos:GetBucketReplication",method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"replication"},function(e,n){if(e)if(404!==e.statusCode||!e.error||"Not Found"!==e.error&&"ReplicationConfigurationnotFoundError"!==e.error.Code)t(e);else{var o={ReplicationConfiguration:{Rules:[]},statusCode:e.statusCode};e.headers&&(o.headers=e.headers),t(null,o)}else e||!n.ReplicationConfiguration&&(n.ReplicationConfiguration={}),n.ReplicationConfiguration.Rule&&(n.ReplicationConfiguration.Rules=n.ReplicationConfiguration.Rule,delete n.ReplicationConfiguration.Rule),t(e,n)})},deleteBucketReplication:function(e,t){d.call(this,{Action:"name/cos:DeleteBucketReplication",method:"DELETE",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"replication"},function(e,n){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:n.statusCode,headers:n.headers})})},putBucketWebsite:function(e,t){if(e.WebsiteConfiguration){var n=r.clone(e.WebsiteConfiguration||{}),o=n.RoutingRules||n.RoutingRule||[];o=r.isArray(o)?o:[o],delete n.RoutingRule,delete n.RoutingRules,o.length&&(n.RoutingRules={RoutingRule:o});var i=r.json2xml({WebsiteConfiguration:n}),a=e.Headers;a["Content-Type"]="application/xml",a["Content-MD5"]=r.binaryBase64(r.md5(i)),d.call(this,{Action:"name/cos:PutBucketWebsite",method:"PUT",Bucket:e.Bucket,Region:e.Region,body:i,action:"website",headers:a},function(e,n){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:n.statusCode,headers:n.headers})})}else t({error:"missing param WebsiteConfiguration"})},getBucketWebsite:function(e,t){d.call(this,{Action:"name/cos:GetBucketWebsite",method:"GET",Bucket:e.Bucket,Region:e.Region,Key:e.Key,headers:e.Headers,action:"website"},function(e,n){if(e)if(404===e.statusCode&&"NoSuchWebsiteConfiguration"===e.error.Code){var o={WebsiteConfiguration:{},statusCode:e.statusCode};e.headers&&(o.headers=e.headers),t(null,o)}else t(e);else{var i=n.WebsiteConfiguration||{};if(i.RoutingRules){var a=r.clone(i.RoutingRules.RoutingRule||[]);a=r.makeArray(a),i.RoutingRules=a}t(null,{WebsiteConfiguration:i,statusCode:n.statusCode,headers:n.headers})}})},deleteBucketWebsite:function(e,t){d.call(this,{Action:"name/cos:DeleteBucketWebsite",method:"DELETE",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"website"},function(e,n){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:n.statusCode,headers:n.headers})})},putBucketReferer:function(e,t){if(e.RefererConfiguration){var n=r.clone(e.RefererConfiguration||{}),o=n.DomainList||{},i=o.Domains||o.Domain||[];(i=r.isArray(i)?i:[i]).length&&(n.DomainList={Domain:i});var a=r.json2xml({RefererConfiguration:n}),s=e.Headers;s["Content-Type"]="application/xml",s["Content-MD5"]=r.binaryBase64(r.md5(a)),d.call(this,{Action:"name/cos:PutBucketReferer",method:"PUT",Bucket:e.Bucket,Region:e.Region,body:a,action:"referer",headers:s},function(e,n){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:n.statusCode,headers:n.headers})})}else t({error:"missing param RefererConfiguration"})},getBucketReferer:function(e,t){d.call(this,{Action:"name/cos:GetBucketReferer",method:"GET",Bucket:e.Bucket,Region:e.Region,Key:e.Key,headers:e.Headers,action:"referer"},function(e,n){if(e)if(404===e.statusCode&&"NoSuchRefererConfiguration"===e.error.Code){var o={WebsiteConfiguration:{},statusCode:e.statusCode};e.headers&&(o.headers=e.headers),t(null,o)}else t(e);else{var i=n.RefererConfiguration||{};if(i.DomainList){var a=r.makeArray(i.DomainList.Domain||[]);i.DomainList={Domains:a}}t(null,{RefererConfiguration:i,statusCode:n.statusCode,headers:n.headers})}})},putBucketDomain:function(e,t){var n=(e.DomainConfiguration||{}).DomainRule||e.DomainRule||[];n=r.clone(n);var o=r.json2xml({DomainConfiguration:{DomainRule:n}}),i=e.Headers;i["Content-Type"]="application/xml",i["Content-MD5"]=r.binaryBase64(r.md5(o)),d.call(this,{Action:"name/cos:PutBucketDomain",method:"PUT",Bucket:e.Bucket,Region:e.Region,body:o,action:"domain",headers:i},function(e,n){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:n.statusCode,headers:n.headers})})},getBucketDomain:function(e,t){d.call(this,{Action:"name/cos:GetBucketDomain",method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"domain"},function(e,n){if(e)return t(e);var o=[];try{o=n.DomainConfiguration.DomainRule||[]}catch(i){}o=r.clone(r.isArray(o)?o:[o]),t(null,{DomainRule:o,statusCode:n.statusCode,headers:n.headers})})},deleteBucketDomain:function(e,t){d.call(this,{Action:"name/cos:DeleteBucketDomain",method:"DELETE",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"domain"},function(e,n){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:n.statusCode,headers:n.headers})})},putBucketOrigin:function(e,t){var n=(e.OriginConfiguration||{}).OriginRule||e.OriginRule||[];n=r.clone(n);var o=r.json2xml({OriginConfiguration:{OriginRule:n}}),i=e.Headers;i["Content-Type"]="application/xml",i["Content-MD5"]=r.binaryBase64(r.md5(o)),d.call(this,{Action:"name/cos:PutBucketOrigin",method:"PUT",Bucket:e.Bucket,Region:e.Region,body:o,action:"origin",headers:i},function(e,n){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:n.statusCode,headers:n.headers})})},getBucketOrigin:function(e,t){d.call(this,{Action:"name/cos:GetBucketOrigin",method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"origin"},function(e,n){if(e)return t(e);var o=[];try{o=n.OriginConfiguration.OriginRule||[]}catch(i){}o=r.clone(r.isArray(o)?o:[o]),t(null,{OriginRule:o,statusCode:n.statusCode,headers:n.headers})})},deleteBucketOrigin:function(e,t){d.call(this,{Action:"name/cos:DeleteBucketOrigin",method:"DELETE",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"origin"},function(e,n){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:n.statusCode,headers:n.headers})})},putBucketLogging:function(e,t){var n=r.json2xml({BucketLoggingStatus:e.BucketLoggingStatus||""}),o=e.Headers;o["Content-Type"]="application/xml",o["Content-MD5"]=r.binaryBase64(r.md5(n)),d.call(this,{Action:"name/cos:PutBucketLogging",method:"PUT",Bucket:e.Bucket,Region:e.Region,body:n,action:"logging",headers:o},function(e,n){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:n.statusCode,headers:n.headers})})},getBucketLogging:function(e,t){d.call(this,{Action:"name/cos:GetBucketLogging",method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"logging"},function(e,n){if(e)return t(e);delete n.BucketLoggingStatus._xmlns,t(null,{BucketLoggingStatus:n.BucketLoggingStatus,statusCode:n.statusCode,headers:n.headers})})},putBucketInventory:function(e,t){var n=r.clone(e.InventoryConfiguration);if(n.OptionalFields){var o=n.OptionalFields||[];n.OptionalFields={Field:o}}if(n.Destination&&n.Destination.COSBucketDestination&&n.Destination.COSBucketDestination.Encryption){var i=n.Destination.COSBucketDestination.Encryption;Object.keys(i).indexOf("SSECOS")>-1&&(i["SSE-COS"]=i.SSECOS,delete i.SSECOS)}var a=r.json2xml({InventoryConfiguration:n}),s=e.Headers;s["Content-Type"]="application/xml",s["Content-MD5"]=r.binaryBase64(r.md5(a)),d.call(this,{Action:"name/cos:PutBucketInventory",method:"PUT",Bucket:e.Bucket,Region:e.Region,body:a,action:"inventory",qs:{id:e.Id},headers:s},function(e,n){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:n.statusCode,headers:n.headers})})},getBucketInventory:function(e,t){d.call(this,{Action:"name/cos:GetBucketInventory",method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"inventory",qs:{id:e.Id}},function(e,n){if(e)return t(e);var o=n.InventoryConfiguration;if(o&&o.OptionalFields&&o.OptionalFields.Field){var i=o.OptionalFields.Field;r.isArray(i)||(i=[i]),o.OptionalFields=i}if(o.Destination&&o.Destination.COSBucketDestination&&o.Destination.COSBucketDestination.Encryption){var a=o.Destination.COSBucketDestination.Encryption;Object.keys(a).indexOf("SSE-COS")>-1&&(a.SSECOS=a["SSE-COS"],delete a["SSE-COS"])}t(null,{InventoryConfiguration:o,statusCode:n.statusCode,headers:n.headers})})},listBucketInventory:function(e,t){d.call(this,{Action:"name/cos:ListBucketInventory",method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"inventory",qs:{"continuation-token":e.ContinuationToken}},function(e,n){if(e)return t(e);var o=n.ListInventoryConfigurationResult,i=o.InventoryConfiguration||[];i=r.isArray(i)?i:[i],delete o.InventoryConfiguration,r.each(i,function(e){if(e&&e.OptionalFields&&e.OptionalFields.Field){var t=e.OptionalFields.Field;r.isArray(t)||(t=[t]),e.OptionalFields=t}if(e.Destination&&e.Destination.COSBucketDestination&&e.Destination.COSBucketDestination.Encryption){var n=e.Destination.COSBucketDestination.Encryption;Object.keys(n).indexOf("SSE-COS")>-1&&(n.SSECOS=n["SSE-COS"],delete n["SSE-COS"])}}),o.InventoryConfigurations=i,r.extend(o,{statusCode:n.statusCode,headers:n.headers}),t(null,o)})},deleteBucketInventory:function(e,t){d.call(this,{Action:"name/cos:DeleteBucketInventory",method:"DELETE",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"inventory",qs:{id:e.Id}},function(e,n){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:n.statusCode,headers:n.headers})})},putBucketAccelerate:function(e,t){if(e.AccelerateConfiguration){var n={AccelerateConfiguration:e.AccelerateConfiguration||{}},o=r.json2xml(n),i={"Content-Type":"application/xml"};i["Content-MD5"]=r.binaryBase64(r.md5(o)),d.call(this,{Interface:"putBucketAccelerate",Action:"name/cos:PutBucketAccelerate",method:"PUT",Bucket:e.Bucket,Region:e.Region,body:o,action:"accelerate",headers:i},function(e,n){if(e)return t(e);t(null,{statusCode:n.statusCode,headers:n.headers})})}else t({error:"missing param AccelerateConfiguration"})},getBucketAccelerate:function(e,t){d.call(this,{Interface:"getBucketAccelerate",Action:"name/cos:GetBucketAccelerate",method:"GET",Bucket:e.Bucket,Region:e.Region,action:"accelerate"},function(e,n){e||!n.AccelerateConfiguration&&(n.AccelerateConfiguration={}),t(e,n)})},getObject:function(e,t){var n=e.Query||{},o=e.QueryString||"",i=e.tracker;i&&i.setParams({signStartTime:(new Date).getTime()}),n["response-content-type"]=e.ResponseContentType,n["response-content-language"]=e.ResponseContentLanguage,n["response-expires"]=e.ResponseExpires,n["response-cache-control"]=e.ResponseCacheControl,n["response-content-disposition"]=e.ResponseContentDisposition,n["response-content-encoding"]=e.ResponseContentEncoding,d.call(this,{Action:"name/cos:GetObject",method:"GET",Bucket:e.Bucket,Region:e.Region,Key:e.Key,VersionId:e.VersionId,headers:e.Headers,qs:n,qsStr:o,rawBody:!0,tracker:i},function(n,o){if(n){var i=n.statusCode;return e.Headers["If-Modified-Since"]&&i&&304===i?t(null,{NotModified:!0}):t(n)}t(null,{Body:o.body,ETag:r.attr(o.headers,"etag",""),statusCode:o.statusCode,headers:o.headers})})},headObject:function(e,t){d.call(this,{Action:"name/cos:HeadObject",method:"HEAD",Bucket:e.Bucket,Region:e.Region,Key:e.Key,VersionId:e.VersionId,headers:e.Headers},function(n,o){if(n){var i=n.statusCode;return e.Headers["If-Modified-Since"]&&i&&304===i?t(null,{NotModified:!0,statusCode:i}):t(n)}o.ETag=r.attr(o.headers,"etag",""),t(null,o)})},listObjectVersions:function(e,t){var n={};n.prefix=e.Prefix||"",n.delimiter=e.Delimiter,n["key-marker"]=e.KeyMarker,n["version-id-marker"]=e.VersionIdMarker,n["max-keys"]=e.MaxKeys,n["encoding-type"]=e.EncodingType,d.call(this,{Action:"name/cos:GetBucketObjectVersions",ResourceKey:n.prefix,method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,qs:n,action:"versions"},function(e,n){if(e)return t(e);var o=n.ListVersionsResult||{},i=o.DeleteMarker||[];i=r.isArray(i)?i:[i];var a=o.Version||[];a=r.isArray(a)?a:[a];var s=r.clone(o);delete s.DeleteMarker,delete s.Version,r.extend(s,{DeleteMarkers:i,Versions:a,statusCode:n.statusCode,headers:n.headers}),t(null,s)})},putObject:function(e,t){var n=this,o=e.ContentLength,a=r.throttleOnProgress.call(n,o,e.onProgress),s=e.Headers;s["Cache-Control"]||s["cache-control"]||(s["Cache-Control"]=""),s["Content-Type"]||s["content-type"]||(s["Content-Type"]=i.getType(e.Key)||"application/octet-stream");var l=e.UploadAddMetaMd5||n.options.UploadAddMetaMd5||n.options.UploadCheckContentMd5,u=e.tracker;l&&u&&u.setParams({md5StartTime:(new Date).getTime()}),r.getBodyMd5(l,e.Body,function(i){i&&(u&&u.setParams({md5EndTime:(new Date).getTime()}),n.options.UploadCheckContentMd5&&(s["Content-MD5"]=r.binaryBase64(i)),(e.UploadAddMetaMd5||n.options.UploadAddMetaMd5)&&(s["x-cos-meta-md5"]=i)),void 0!==e.ContentLength&&(s["Content-Length"]=e.ContentLength),a(null,!0),d.call(n,{Action:"name/cos:PutObject",TaskId:e.TaskId,method:"PUT",Bucket:e.Bucket,Region:e.Region,Key:e.Key,headers:e.Headers,qs:e.Query,body:e.Body,onProgress:a,tracker:u},function(i,s){if(i)return a(null,!0),t(i);a({loaded:o,total:o},!0);var l=c({ForcePathStyle:n.options.ForcePathStyle,protocol:n.options.Protocol,domain:n.options.Domain,bucket:e.Bucket,region:n.options.UseAccelerate?"accelerate":e.Region,object:e.Key});l=l.substr(l.indexOf("://")+3),s.Location=l,s.ETag=r.attr(s.headers,"etag",""),t(null,s)})})},postObject:function(e,t){var n=this,o={},i=e.FilePath;if(i){for(var a in o["Cache-Control"]=e.CacheControl,o["Content-Disposition"]=e.ContentDisposition,o["Content-Encoding"]=e.ContentEncoding,o["Content-MD5"]=e.ContentMD5,o["Content-Length"]=e.ContentLength,o["Content-Type"]=e.ContentType,o.Expect=e.Expect,o.Expires=e.Expires,o["x-cos-acl"]=e.ACL,o["x-cos-grant-read"]=e.GrantRead,o["x-cos-grant-write"]=e.GrantWrite,o["x-cos-grant-full-control"]=e.GrantFullControl,o["x-cos-storage-class"]=e.StorageClass,o["x-cos-mime-limit"]=e.MimeLimit,o["x-cos-traffic-limit"]=e.TrafficLimit,o["x-cos-server-side-encryption-customer-algorithm"]=e.SSECustomerAlgorithm,o["x-cos-server-side-encryption-customer-key"]=e.SSECustomerKey,o["x-cos-server-side-encryption-customer-key-MD5"]=e.SSECustomerKeyMD5,o["x-cos-server-side-encryption"]=e.ServerSideEncryption,o["x-cos-server-side-encryption-cos-kms-key-id"]=e.SSEKMSKeyId,o["x-cos-server-side-encryption-context"]=e.SSEContext,delete o["Content-Length"],delete o["content-length"],e)a.indexOf("x-cos-meta-")>-1&&(o[a]=e[a]);var s=r.throttleOnProgress.call(n,o["Content-Length"],e.onProgress);d.call(this,{Action:"name/cos:PostObject",method:"POST",Bucket:e.Bucket,Region:e.Region,Key:e.Key,headers:o,qs:e.Query,filePath:i,TaskId:e.TaskId,onProgress:s},function(o,r){if(s(null,!0),o)return t(o);if(r&&r.headers){var a=r.headers,l=a.etag||a.Etag||a.ETag||"",u=i.substr(i.lastIndexOf("/")+1),p=c({ForcePathStyle:n.options.ForcePathStyle,protocol:n.options.Protocol,domain:n.options.Domain,bucket:e.Bucket,region:e.Region,object:e.Key.replace(/\$\{filename\}/g,u),isLocation:!0});return t(null,{Location:p,statusCode:r.statusCode,headers:a,ETag:l})}t(null,r)})}else t({error:"missing param FilePath"})},deleteObject:function(e,t){d.call(this,{Action:"name/cos:DeleteObject",method:"DELETE",Bucket:e.Bucket,Region:e.Region,Key:e.Key,headers:e.Headers,VersionId:e.VersionId},function(e,n){if(e){var o=e.statusCode;return o&&204===o?t(null,{statusCode:o}):o&&404===o?t(null,{BucketNotFound:!0,statusCode:o}):t(e)}t(null,{statusCode:n.statusCode,headers:n.headers})})},getObjectAcl:function(e,t){d.call(this,{Action:"name/cos:GetObjectACL",method:"GET",Bucket:e.Bucket,Region:e.Region,Key:e.Key,headers:e.Headers,action:"acl"},function(e,n){if(e)return t(e);var o=n.AccessControlPolicy||{},i=o.Owner||{},s=o.AccessControlList&&o.AccessControlList.Grant||[];s=r.isArray(s)?s:[s];var c=a(o);n.headers&&n.headers["x-cos-acl"]&&(c.ACL=n.headers["x-cos-acl"]),c=r.extend(c,{Owner:i,Grants:s,statusCode:n.statusCode,headers:n.headers}),t(null,c)})},putObjectAcl:function(e,t){var n=e.Headers,o="";if(e.AccessControlPolicy){var i=r.clone(e.AccessControlPolicy||{}),a=i.Grants||i.Grant;a=r.isArray(a)?a:[a],delete i.Grant,delete i.Grants,i.AccessControlList={Grant:a},o=r.json2xml({AccessControlPolicy:i}),n["Content-Type"]="application/xml",n["Content-MD5"]=r.binaryBase64(r.md5(o))}r.each(n,function(e,t){0===t.indexOf("x-cos-grant-")&&(n[t]=s(n[t]))}),d.call(this,{Action:"name/cos:PutObjectACL",method:"PUT",Bucket:e.Bucket,Region:e.Region,Key:e.Key,action:"acl",headers:n,body:o},function(e,n){if(e)return t(e);t(null,{statusCode:n.statusCode,headers:n.headers})})},optionsObject:function(e,t){var n=e.Headers;n.Origin=e.Origin,n["Access-Control-Request-Method"]=e.AccessControlRequestMethod,n["Access-Control-Request-Headers"]=e.AccessControlRequestHeaders,d.call(this,{Action:"name/cos:OptionsObject",method:"OPTIONS",Bucket:e.Bucket,Region:e.Region,Key:e.Key,headers:n},function(e,n){if(e)return e.statusCode&&403===e.statusCode?t(null,{OptionsForbidden:!0,statusCode:e.statusCode}):t(e);var o=n.headers||{};t(null,{AccessControlAllowOrigin:o["access-control-allow-origin"],AccessControlAllowMethods:o["access-control-allow-methods"],AccessControlAllowHeaders:o["access-control-allow-headers"],AccessControlExposeHeaders:o["access-control-expose-headers"],AccessControlMaxAge:o["access-control-max-age"],statusCode:n.statusCode,headers:n.headers})})},putObjectCopy:function(e,t){var n=e.Headers;!n["Cache-Control"]&&n["cache-control"]&&(n["Cache-Control"]="");var o=e.CopySource||"",i=r.getSourceParams.call(this,o);if(i){var a=i.Bucket,s=i.Region,c=decodeURIComponent(i.Key);d.call(this,{Scope:[{action:"name/cos:GetObject",bucket:a,region:s,prefix:c},{action:"name/cos:PutObject",bucket:e.Bucket,region:e.Region,prefix:e.Key}],method:"PUT",Bucket:e.Bucket,Region:e.Region,Key:e.Key,VersionId:e.VersionId,headers:e.Headers},function(e,n){if(e)return t(e);var o=r.clone(n.CopyObjectResult||{});r.extend(o,{statusCode:n.statusCode,headers:n.headers}),t(null,o)})}else t({error:"CopySource format error"})},deleteMultipleObject:function(e,t){var n=e.Objects||[],o=e.Quiet;n=r.isArray(n)?n:[n];var i=r.json2xml({Delete:{Object:n,Quiet:o||!1}}),a=e.Headers;a["Content-Type"]="application/xml",a["Content-MD5"]=r.binaryBase64(r.md5(i));var s=r.map(n,function(t){return{action:"name/cos:DeleteObject",bucket:e.Bucket,region:e.Region,prefix:t.Key}});d.call(this,{Scope:s,method:"POST",Bucket:e.Bucket,Region:e.Region,body:i,action:"delete",headers:a},function(e,n){if(e)return t(e);var o=n.DeleteResult||{},i=o.Deleted||[],a=o.Error||[];i=r.isArray(i)?i:[i],a=r.isArray(a)?a:[a];var s=r.clone(o);r.extend(s,{Error:a,Deleted:i,statusCode:n.statusCode,headers:n.headers}),t(null,s)})},restoreObject:function(e,t){var n=e.Headers;if(e.RestoreRequest){var o=e.RestoreRequest||{},i=r.json2xml({RestoreRequest:o});n["Content-Type"]="application/xml",n["Content-MD5"]=r.binaryBase64(r.md5(i)),d.call(this,{Action:"name/cos:RestoreObject",method:"POST",Bucket:e.Bucket,Region:e.Region,Key:e.Key,VersionId:e.VersionId,body:i,action:"restore",headers:n},function(e,n){t(e,n)})}else t({error:"missing param RestoreRequest"})},putObjectTagging:function(e,t){var n=e.Tagging||{},o=n.TagSet||n.Tags||e.Tags||[];o=r.clone(r.isArray(o)?o:[o]);var i=r.json2xml({Tagging:{TagSet:{Tag:o}}}),a=e.Headers;a["Content-Type"]="application/xml",a["Content-MD5"]=r.binaryBase64(r.md5(i)),d.call(this,{Interface:"putObjectTagging",Action:"name/cos:PutObjectTagging",method:"PUT",Bucket:e.Bucket,Key:e.Key,Region:e.Region,body:i,action:"tagging",headers:a,VersionId:e.VersionId},function(e,n){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:n.statusCode,headers:n.headers})})},getObjectTagging:function(e,t){d.call(this,{Interface:"getObjectTagging",Action:"name/cos:GetObjectTagging",method:"GET",Key:e.Key,Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"tagging",VersionId:e.VersionId},function(e,n){if(e)if(404!==e.statusCode||!e.error||"Not Found"!==e.error&&"NoSuchTagSet"!==e.error.Code)t(e);else{var o={Tags:[],statusCode:e.statusCode};e.headers&&(o.headers=e.headers),t(null,o)}else{var i=[];try{i=n.Tagging.TagSet.Tag||[]}catch(a){}i=r.clone(r.isArray(i)?i:[i]),t(null,{Tags:i,statusCode:n.statusCode,headers:n.headers})}})},deleteObjectTagging:function(e,t){d.call(this,{Interface:"deleteObjectTagging",Action:"name/cos:DeleteObjectTagging",method:"DELETE",Bucket:e.Bucket,Region:e.Region,Key:e.Key,headers:e.Headers,action:"tagging",VersionId:e.VersionId},function(e,n){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:n.statusCode,headers:n.headers})})},appendObject:function(e,t){d.call(this,{Action:"name/cos:AppendObject",method:"POST",Bucket:e.Bucket,Region:e.Region,action:"append",Key:e.Key,body:e.Body,qs:{position:e.Position},headers:e.Headers},function(e,n){if(e)return t(e);t(null,n)})},uploadPartCopy:function(e,t){var n=e.CopySource||"",o=r.getSourceParams.call(this,n);if(o){var i=o.Bucket,a=o.Region,s=decodeURIComponent(o.Key);d.call(this,{Scope:[{action:"name/cos:GetObject",bucket:i,region:a,prefix:s},{action:"name/cos:PutObject",bucket:e.Bucket,region:e.Region,prefix:e.Key}],method:"PUT",Bucket:e.Bucket,Region:e.Region,Key:e.Key,VersionId:e.VersionId,qs:{partNumber:e.PartNumber,uploadId:e.UploadId},headers:e.Headers},function(e,n){if(e)return t(e);var o=r.clone(n.CopyPartResult||{});r.extend(o,{statusCode:n.statusCode,headers:n.headers}),t(null,o)})}else t({error:"CopySource format error"})},multipartInit:function(e,t){var n=e.Headers,o=e.tracker;n["Cache-Control"]||n["cache-control"]||(n["Cache-Control"]=""),n["Content-Type"]||n["content-type"]||(n["Content-Type"]=i.getType(e.Key)||"application/octet-stream"),d.call(this,{Action:"name/cos:InitiateMultipartUpload",method:"POST",Bucket:e.Bucket,Region:e.Region,Key:e.Key,action:"uploads",headers:e.Headers,qs:e.Query,tracker:o},function(e,n){return e?(o&&o.parent&&o.parent.setParams({errorNode:"multipartInit"}),t(e)):(n=r.clone(n||{}))&&n.InitiateMultipartUploadResult?t(null,r.extend(n.InitiateMultipartUploadResult,{statusCode:n.statusCode,headers:n.headers})):void t(null,n)})},multipartUpload:function(e,t){var n=this;r.getFileSize("multipartUpload",e,function(){var o=e.tracker,i=n.options.UploadCheckContentMd5;i&&o&&o.setParams({md5StartTime:(new Date).getTime()}),r.getBodyMd5(i,e.Body,function(a){a&&(e.Headers["Content-MD5"]=r.binaryBase64(a),i&&o&&o.setParams({md5EndTime:(new Date).getTime()})),o&&o.setParams({partNumber:e.PartNumber}),d.call(n,{Action:"name/cos:UploadPart",TaskId:e.TaskId,method:"PUT",Bucket:e.Bucket,Region:e.Region,Key:e.Key,qs:{partNumber:e.PartNumber,uploadId:e.UploadId},headers:e.Headers,onProgress:e.onProgress,body:e.Body||null,tracker:o},function(e,n){if(e)return o&&o.parent&&o.parent.setParams({errorNode:"multipartUpload"}),t(e);t(null,{ETag:r.attr(n.headers,"etag",{}),statusCode:n.statusCode,headers:n.headers})})})})},multipartComplete:function(e,t){for(var n=this,o=e.UploadId,i=e.Parts,a=e.tracker,s=0,l=i.length;s<l;s++)0!==i[s].ETag.indexOf('"')&&(i[s].ETag='"'+i[s].ETag+'"');var u=r.json2xml({CompleteMultipartUpload:{Part:i}}),p=e.Headers;p["Content-Type"]="application/xml",p["Content-MD5"]=r.binaryBase64(r.md5(u)),d.call(this,{Action:"name/cos:CompleteMultipartUpload",method:"POST",Bucket:e.Bucket,Region:e.Region,Key:e.Key,qs:{uploadId:o},body:u,headers:p,tracker:a},function(o,i){if(o)return a&&a.parent&&a.parent.setParams({errorNode:"multipartComplete"}),t(o);var s=c({ForcePathStyle:n.options.ForcePathStyle,protocol:n.options.Protocol,domain:n.options.Domain,bucket:e.Bucket,region:e.Region,object:e.Key,isLocation:!0}),l=i.CompleteMultipartUploadResult||{},u=r.extend(l,{Location:s,statusCode:i.statusCode,headers:i.headers});t(null,u)})},multipartList:function(e,t){var n={};n.delimiter=e.Delimiter,n["encoding-type"]=e.EncodingType,n.prefix=e.Prefix||"",n["max-uploads"]=e.MaxUploads,n["key-marker"]=e.KeyMarker,n["upload-id-marker"]=e.UploadIdMarker,n=r.clearKey(n);var o=e.tracker;o&&o.setParams({signStartTime:(new Date).getTime()}),d.call(this,{Action:"name/cos:ListMultipartUploads",ResourceKey:n.prefix,method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,qs:n,action:"uploads",tracker:o},function(e,n){if(e)return o&&o.parent&&o.parent.setParams({errorNode:"multipartList"}),t(e);if(n&&n.ListMultipartUploadsResult){var i=n.ListMultipartUploadsResult.Upload||[],a=n.ListMultipartUploadsResult.CommonPrefixes||[];a=r.isArray(a)?a:[a],i=r.isArray(i)?i:[i],n.ListMultipartUploadsResult.Upload=i,n.ListMultipartUploadsResult.CommonPrefixes=a}var s=r.clone(n.ListMultipartUploadsResult||{});r.extend(s,{statusCode:n.statusCode,headers:n.headers}),t(null,s)})},multipartListPart:function(e,t){var n={},o=e.tracker;n.uploadId=e.UploadId,n["encoding-type"]=e.EncodingType,n["max-parts"]=e.MaxParts,n["part-number-marker"]=e.PartNumberMarker,d.call(this,{Action:"name/cos:ListParts",method:"GET",Bucket:e.Bucket,Region:e.Region,Key:e.Key,headers:e.Headers,qs:n,tracker:o},function(e,n){if(e)return o&&o.parent&&o.parent.setParams({errorNode:"multipartListPart"}),t(e);var i=n.ListPartsResult||{},a=i.Part||[];a=r.isArray(a)?a:[a],i.Part=a;var s=r.clone(i);r.extend(s,{statusCode:n.statusCode,headers:n.headers}),t(null,s)})},multipartAbort:function(e,t){var n={};n.uploadId=e.UploadId,d.call(this,{Action:"name/cos:AbortMultipartUpload",method:"DELETE",Bucket:e.Bucket,Region:e.Region,Key:e.Key,headers:e.Headers,qs:n},function(e,n){if(e)return t(e);t(null,{statusCode:n.statusCode,headers:n.headers})})},request:function(e,t){d.call(this,{method:e.Method,Bucket:e.Bucket,Region:e.Region,Key:e.Key,action:e.Action,headers:e.Headers,qs:e.Query,body:e.Body,Url:e.Url,rawBody:e.RawBody},function(e,n){if(e)return t(e);n&&n.body&&(n.Body=n.body,delete n.body),t(e,n)})},getObjectUrl:function(e,t){var n=this,o=void 0===e.UseAccelerate?n.options.UseAccelerate:e.UseAccelerate,i=c({ForcePathStyle:n.options.ForcePathStyle,protocol:e.Protocol||n.options.Protocol,domain:e.Domain||n.options.Domain,bucket:e.Bucket,region:o?"accelerate":e.Region,object:e.Key}),a="";e.Query&&(a+=r.obj2str(e.Query)),e.QueryString&&(a+=(a?"&":"")+e.QueryString);var s=i;if(void 0!==e.Sign&&!e.Sign)return a&&(s+="?"+a),t(null,{Url:s}),s;var p=l.call(this,{Bucket:e.Bucket,Region:e.Region,UseAccelerate:e.UseAccelerate,Url:i}),d=u.call(this,{Action:"PUT"===(e.Method||"").toUpperCase()?"name/cos:PutObject":"name/cos:GetObject",Bucket:e.Bucket||"",Region:e.Region||"",Method:e.Method||"get",Key:e.Key,Expires:e.Expires,Headers:e.Headers,Query:e.Query,SignHost:p,ForceSignHost:!1!==e.ForceSignHost&&n.options.ForceSignHost},function(e,n){if(t)if(e)t(e);else{var o=i;o+="?"+(n.Authorization.indexOf("q-signature")>-1?(r=n.Authorization,s=r.match(/q-url-param-list.*?(?=&)/g)[0],c="q-url-param-list="+encodeURIComponent(s.replace(/q-url-param-list=/,"")).toLowerCase(),l=new RegExp(s,"g"),r.replace(l,c)):"sign="+encodeURIComponent(n.Authorization)),n.SecurityToken&&(o+="&x-cos-security-token="+n.SecurityToken),n.ClientIP&&(o+="&clientIP="+n.ClientIP),n.ClientUA&&(o+="&clientUA="+n.ClientUA),n.Token&&(o+="&token="+n.Token),a&&(o+="&"+a),setTimeout(function(){t(null,{Url:o})})}var r,s,c,l});return d?(s+="?"+d.Authorization+(d.SecurityToken?"&x-cos-security-token="+d.SecurityToken:""),a&&(s+="&"+a)):a&&(s+="?"+a),s},getAuth:function(e){return r.getAuth({SecretId:e.SecretId||this.options.SecretId||"",SecretKey:e.SecretKey||this.options.SecretKey||"",Bucket:e.Bucket,Region:e.Region,Method:e.Method,Key:e.Key,Query:e.Query,Headers:e.Headers,Expires:e.Expires,SystemClockOffset:this.options.SystemClockOffset})}};e.exports.init=function(e,t){t.transferToTaskMethod(h,"postObject"),t.transferToTaskMethod(h,"putObject"),r.each(h,function(t,n){e.prototype[n]=r.apiWrapper(n,t)})}},function(e,t){function n(e){return encodeURIComponent(e).replace(/!/g,"%21").replace(/'/g,"%27").replace(/\(/g,"%28").replace(/\)/g,"%29").replace(/\*/g,"%2A")}e.exports=function(e,t){var o,r=e.filePath,i=e.headers||{},a=e.url||e.Url,s=e.method,c=e.onProgress,l=e.httpDNSServiceId,u=function(e,n){var o=n.header,r={};if(o)for(var i in o)o.hasOwnProperty(i)&&(r[i.toLowerCase()]=o[i]);t(e,{statusCode:n.statusCode,headers:r},n.data)};if(r){var p,d=a.match(/^(https?:\/\/[^/]+\/)([^/]*\/?)(.*)$/);e.pathStyle?(p=decodeURIComponent(d[3]||""),a=d[1]+d[2]):(p=decodeURIComponent(d[2]+d[3]||""),a=d[1]);var f={key:p,success_action_status:200,Signature:i.Authorization},h=["Cache-Control","Content-Type","Content-Disposition","Content-Encoding","Expires","x-cos-storage-class","x-cos-security-token","x-ci-security-token"];for(var m in e.headers)e.headers.hasOwnProperty(m)&&(m.indexOf("x-cos-meta-")>-1||h.indexOf(m)>-1)&&(f[m]=e.headers[m]);i["x-cos-acl"]&&(f.acl=i["x-cos-acl"]),!f["Content-Type"]&&(f["Content-Type"]=""),(o=wx$1.uploadFile({url:a,method:s,name:"file",header:i,filePath:r,formData:f,timeout:e.timeout,success:function(e){u(null,e)},fail:function(e){u(e.errMsg,e)}})).onProgressUpdate(function(e){c&&c({loaded:e.totalBytesSent,total:e.totalBytesExpectedToSend,progress:e.progress/100})})}else{var g=e.qs&&function(e){var t,o,r,i=[],a=function(e){var t=[];for(var n in e)e.hasOwnProperty(n)&&t.push(n);return t.sort(function(e,t){return(e=e.toLowerCase())===(t=t.toLowerCase())?0:e>t?1:-1})}(e);for(t=0;t<a.length;t++)r=void 0===e[o=a[t]]||null===e[o]?"":""+e[o],o=n(o),r=n(r)||"",i.push(o+"="+r);return i.join("&")}(e.qs)||"";g&&(a+=(a.indexOf("?")>-1?"&":"?")+g),i["Content-Length"]&&delete i["Content-Length"];var v={url:a,method:s,header:i,dataType:"text",data:e.body,timeout:e.timeout,success:function(e){u(null,e)},fail:function(e){u(e.errMsg,e)}};l&&Object.assign(v,{enableHttpDNS:!0,httpDNSServiceId:l}),o=wx$1.request(v)}return o}},function(e,t,n){let o=n(27);e.exports=new o(n(28),n(29))},function(e,t,n){function o(){this._types=Object.create(null),this._extensions=Object.create(null);for(let e=0;e<arguments.length;e++)this.define(arguments[e]);this.define=this.define.bind(this),this.getType=this.getType.bind(this),this.getExtension=this.getExtension.bind(this)}o.prototype.define=function(e,t){for(let n in e){let o=e[n].map(function(e){return e.toLowerCase()});n=n.toLowerCase();for(let e=0;e<o.length;e++){const r=o[e];if("*"!==r[0]){if(!t&&r in this._types)throw new Error('Attempt to change mapping for "'+r+'" extension from "'+this._types[r]+'" to "'+n+'". Pass `force=true` to allow this, otherwise remove "'+r+'" from the list of extensions for "'+n+'".');this._types[r]=n}}if(t||!this._extensions[n]){const e=o[0];this._extensions[n]="*"!==e[0]?e:e.substr(1)}}},o.prototype.getType=function(e){let t=(e=String(e)).replace(/^.*[/\\]/,"").toLowerCase(),n=t.replace(/^.*\./,"").toLowerCase(),o=t.length<e.length;return(n.length<t.length-1||!o)&&this._types[n]||null},o.prototype.getExtension=function(e){return(e=/^\s*([^;\s]*)/.test(e)&&RegExp.$1)&&this._extensions[e.toLowerCase()]||null},e.exports=o},function(e,t){e.exports={"application/andrew-inset":["ez"],"application/applixware":["aw"],"application/atom+xml":["atom"],"application/atomcat+xml":["atomcat"],"application/atomdeleted+xml":["atomdeleted"],"application/atomsvc+xml":["atomsvc"],"application/atsc-dwd+xml":["dwd"],"application/atsc-held+xml":["held"],"application/atsc-rsat+xml":["rsat"],"application/bdoc":["bdoc"],"application/calendar+xml":["xcs"],"application/ccxml+xml":["ccxml"],"application/cdfx+xml":["cdfx"],"application/cdmi-capability":["cdmia"],"application/cdmi-container":["cdmic"],"application/cdmi-domain":["cdmid"],"application/cdmi-object":["cdmio"],"application/cdmi-queue":["cdmiq"],"application/cu-seeme":["cu"],"application/dash+xml":["mpd"],"application/davmount+xml":["davmount"],"application/docbook+xml":["dbk"],"application/dssc+der":["dssc"],"application/dssc+xml":["xdssc"],"application/ecmascript":["es","ecma"],"application/emma+xml":["emma"],"application/emotionml+xml":["emotionml"],"application/epub+zip":["epub"],"application/exi":["exi"],"application/express":["exp"],"application/fdt+xml":["fdt"],"application/font-tdpfr":["pfr"],"application/geo+json":["geojson"],"application/gml+xml":["gml"],"application/gpx+xml":["gpx"],"application/gxf":["gxf"],"application/gzip":["gz"],"application/hjson":["hjson"],"application/hyperstudio":["stk"],"application/inkml+xml":["ink","inkml"],"application/ipfix":["ipfix"],"application/its+xml":["its"],"application/java-archive":["jar","war","ear"],"application/java-serialized-object":["ser"],"application/java-vm":["class"],"application/javascript":["js","mjs"],"application/json":["json","map"],"application/json5":["json5"],"application/jsonml+json":["jsonml"],"application/ld+json":["jsonld"],"application/lgr+xml":["lgr"],"application/lost+xml":["lostxml"],"application/mac-binhex40":["hqx"],"application/mac-compactpro":["cpt"],"application/mads+xml":["mads"],"application/manifest+json":["webmanifest"],"application/marc":["mrc"],"application/marcxml+xml":["mrcx"],"application/mathematica":["ma","nb","mb"],"application/mathml+xml":["mathml"],"application/mbox":["mbox"],"application/mediaservercontrol+xml":["mscml"],"application/metalink+xml":["metalink"],"application/metalink4+xml":["meta4"],"application/mets+xml":["mets"],"application/mmt-aei+xml":["maei"],"application/mmt-usd+xml":["musd"],"application/mods+xml":["mods"],"application/mp21":["m21","mp21"],"application/mp4":["mp4s","m4p"],"application/msword":["doc","dot"],"application/mxf":["mxf"],"application/n-quads":["nq"],"application/n-triples":["nt"],"application/node":["cjs"],"application/octet-stream":["bin","dms","lrf","mar","so","dist","distz","pkg","bpk","dump","elc","deploy","exe","dll","deb","dmg","iso","img","msi","msp","msm","buffer"],"application/oda":["oda"],"application/oebps-package+xml":["opf"],"application/ogg":["ogx"],"application/omdoc+xml":["omdoc"],"application/onenote":["onetoc","onetoc2","onetmp","onepkg"],"application/oxps":["oxps"],"application/p2p-overlay+xml":["relo"],"application/patch-ops-error+xml":["xer"],"application/pdf":["pdf"],"application/pgp-encrypted":["pgp"],"application/pgp-signature":["asc","sig"],"application/pics-rules":["prf"],"application/pkcs10":["p10"],"application/pkcs7-mime":["p7m","p7c"],"application/pkcs7-signature":["p7s"],"application/pkcs8":["p8"],"application/pkix-attr-cert":["ac"],"application/pkix-cert":["cer"],"application/pkix-crl":["crl"],"application/pkix-pkipath":["pkipath"],"application/pkixcmp":["pki"],"application/pls+xml":["pls"],"application/postscript":["ai","eps","ps"],"application/provenance+xml":["provx"],"application/pskc+xml":["pskcxml"],"application/raml+yaml":["raml"],"application/rdf+xml":["rdf","owl"],"application/reginfo+xml":["rif"],"application/relax-ng-compact-syntax":["rnc"],"application/resource-lists+xml":["rl"],"application/resource-lists-diff+xml":["rld"],"application/rls-services+xml":["rs"],"application/route-apd+xml":["rapd"],"application/route-s-tsid+xml":["sls"],"application/route-usd+xml":["rusd"],"application/rpki-ghostbusters":["gbr"],"application/rpki-manifest":["mft"],"application/rpki-roa":["roa"],"application/rsd+xml":["rsd"],"application/rss+xml":["rss"],"application/rtf":["rtf"],"application/sbml+xml":["sbml"],"application/scvp-cv-request":["scq"],"application/scvp-cv-response":["scs"],"application/scvp-vp-request":["spq"],"application/scvp-vp-response":["spp"],"application/sdp":["sdp"],"application/senml+xml":["senmlx"],"application/sensml+xml":["sensmlx"],"application/set-payment-initiation":["setpay"],"application/set-registration-initiation":["setreg"],"application/shf+xml":["shf"],"application/sieve":["siv","sieve"],"application/smil+xml":["smi","smil"],"application/sparql-query":["rq"],"application/sparql-results+xml":["srx"],"application/srgs":["gram"],"application/srgs+xml":["grxml"],"application/sru+xml":["sru"],"application/ssdl+xml":["ssdl"],"application/ssml+xml":["ssml"],"application/swid+xml":["swidtag"],"application/tei+xml":["tei","teicorpus"],"application/thraud+xml":["tfi"],"application/timestamped-data":["tsd"],"application/toml":["toml"],"application/trig":["trig"],"application/ttml+xml":["ttml"],"application/ubjson":["ubj"],"application/urc-ressheet+xml":["rsheet"],"application/urc-targetdesc+xml":["td"],"application/voicexml+xml":["vxml"],"application/wasm":["wasm"],"application/widget":["wgt"],"application/winhlp":["hlp"],"application/wsdl+xml":["wsdl"],"application/wspolicy+xml":["wspolicy"],"application/xaml+xml":["xaml"],"application/xcap-att+xml":["xav"],"application/xcap-caps+xml":["xca"],"application/xcap-diff+xml":["xdf"],"application/xcap-el+xml":["xel"],"application/xcap-ns+xml":["xns"],"application/xenc+xml":["xenc"],"application/xhtml+xml":["xhtml","xht"],"application/xliff+xml":["xlf"],"application/xml":["xml","xsl","xsd","rng"],"application/xml-dtd":["dtd"],"application/xop+xml":["xop"],"application/xproc+xml":["xpl"],"application/xslt+xml":["*xsl","xslt"],"application/xspf+xml":["xspf"],"application/xv+xml":["mxml","xhvml","xvml","xvm"],"application/yang":["yang"],"application/yin+xml":["yin"],"application/zip":["zip"],"audio/3gpp":["*3gpp"],"audio/adpcm":["adp"],"audio/amr":["amr"],"audio/basic":["au","snd"],"audio/midi":["mid","midi","kar","rmi"],"audio/mobile-xmf":["mxmf"],"audio/mp3":["*mp3"],"audio/mp4":["m4a","mp4a"],"audio/mpeg":["mpga","mp2","mp2a","mp3","m2a","m3a"],"audio/ogg":["oga","ogg","spx","opus"],"audio/s3m":["s3m"],"audio/silk":["sil"],"audio/wav":["wav"],"audio/wave":["*wav"],"audio/webm":["weba"],"audio/xm":["xm"],"font/collection":["ttc"],"font/otf":["otf"],"font/ttf":["ttf"],"font/woff":["woff"],"font/woff2":["woff2"],"image/aces":["exr"],"image/apng":["apng"],"image/avif":["avif"],"image/bmp":["bmp"],"image/cgm":["cgm"],"image/dicom-rle":["drle"],"image/emf":["emf"],"image/fits":["fits"],"image/g3fax":["g3"],"image/gif":["gif"],"image/heic":["heic"],"image/heic-sequence":["heics"],"image/heif":["heif"],"image/heif-sequence":["heifs"],"image/hej2k":["hej2"],"image/hsj2":["hsj2"],"image/ief":["ief"],"image/jls":["jls"],"image/jp2":["jp2","jpg2"],"image/jpeg":["jpeg","jpg","jpe"],"image/jph":["jph"],"image/jphc":["jhc"],"image/jpm":["jpm"],"image/jpx":["jpx","jpf"],"image/jxr":["jxr"],"image/jxra":["jxra"],"image/jxrs":["jxrs"],"image/jxs":["jxs"],"image/jxsc":["jxsc"],"image/jxsi":["jxsi"],"image/jxss":["jxss"],"image/ktx":["ktx"],"image/ktx2":["ktx2"],"image/png":["png"],"image/sgi":["sgi"],"image/svg+xml":["svg","svgz"],"image/t38":["t38"],"image/tiff":["tif","tiff"],"image/tiff-fx":["tfx"],"image/webp":["webp"],"image/wmf":["wmf"],"message/disposition-notification":["disposition-notification"],"message/global":["u8msg"],"message/global-delivery-status":["u8dsn"],"message/global-disposition-notification":["u8mdn"],"message/global-headers":["u8hdr"],"message/rfc822":["eml","mime"],"model/3mf":["3mf"],"model/gltf+json":["gltf"],"model/gltf-binary":["glb"],"model/iges":["igs","iges"],"model/mesh":["msh","mesh","silo"],"model/mtl":["mtl"],"model/obj":["obj"],"model/step+xml":["stpx"],"model/step+zip":["stpz"],"model/step-xml+zip":["stpxz"],"model/stl":["stl"],"model/vrml":["wrl","vrml"],"model/x3d+binary":["*x3db","x3dbz"],"model/x3d+fastinfoset":["x3db"],"model/x3d+vrml":["*x3dv","x3dvz"],"model/x3d+xml":["x3d","x3dz"],"model/x3d-vrml":["x3dv"],"text/cache-manifest":["appcache","manifest"],"text/calendar":["ics","ifb"],"text/coffeescript":["coffee","litcoffee"],"text/css":["css"],"text/csv":["csv"],"text/html":["html","htm","shtml"],"text/jade":["jade"],"text/jsx":["jsx"],"text/less":["less"],"text/markdown":["markdown","md"],"text/mathml":["mml"],"text/mdx":["mdx"],"text/n3":["n3"],"text/plain":["txt","text","conf","def","list","log","in","ini"],"text/richtext":["rtx"],"text/rtf":["*rtf"],"text/sgml":["sgml","sgm"],"text/shex":["shex"],"text/slim":["slim","slm"],"text/spdx":["spdx"],"text/stylus":["stylus","styl"],"text/tab-separated-values":["tsv"],"text/troff":["t","tr","roff","man","me","ms"],"text/turtle":["ttl"],"text/uri-list":["uri","uris","urls"],"text/vcard":["vcard"],"text/vtt":["vtt"],"text/xml":["*xml"],"text/yaml":["yaml","yml"],"video/3gpp":["3gp","3gpp"],"video/3gpp2":["3g2"],"video/h261":["h261"],"video/h263":["h263"],"video/h264":["h264"],"video/iso.segment":["m4s"],"video/jpeg":["jpgv"],"video/jpm":["*jpm","jpgm"],"video/mj2":["mj2","mjp2"],"video/mp2t":["ts"],"video/mp4":["mp4","mp4v","mpg4"],"video/mpeg":["mpeg","mpg","mpe","m1v","m2v"],"video/ogg":["ogv"],"video/quicktime":["qt","mov"],"video/webm":["webm"]}},function(e,t){e.exports={"application/prs.cww":["cww"],"application/vnd.1000minds.decision-model+xml":["1km"],"application/vnd.3gpp.pic-bw-large":["plb"],"application/vnd.3gpp.pic-bw-small":["psb"],"application/vnd.3gpp.pic-bw-var":["pvb"],"application/vnd.3gpp2.tcap":["tcap"],"application/vnd.3m.post-it-notes":["pwn"],"application/vnd.accpac.simply.aso":["aso"],"application/vnd.accpac.simply.imp":["imp"],"application/vnd.acucobol":["acu"],"application/vnd.acucorp":["atc","acutc"],"application/vnd.adobe.air-application-installer-package+zip":["air"],"application/vnd.adobe.formscentral.fcdt":["fcdt"],"application/vnd.adobe.fxp":["fxp","fxpl"],"application/vnd.adobe.xdp+xml":["xdp"],"application/vnd.adobe.xfdf":["xfdf"],"application/vnd.ahead.space":["ahead"],"application/vnd.airzip.filesecure.azf":["azf"],"application/vnd.airzip.filesecure.azs":["azs"],"application/vnd.amazon.ebook":["azw"],"application/vnd.americandynamics.acc":["acc"],"application/vnd.amiga.ami":["ami"],"application/vnd.android.package-archive":["apk"],"application/vnd.anser-web-certificate-issue-initiation":["cii"],"application/vnd.anser-web-funds-transfer-initiation":["fti"],"application/vnd.antix.game-component":["atx"],"application/vnd.apple.installer+xml":["mpkg"],"application/vnd.apple.keynote":["key"],"application/vnd.apple.mpegurl":["m3u8"],"application/vnd.apple.numbers":["numbers"],"application/vnd.apple.pages":["pages"],"application/vnd.apple.pkpass":["pkpass"],"application/vnd.aristanetworks.swi":["swi"],"application/vnd.astraea-software.iota":["iota"],"application/vnd.audiograph":["aep"],"application/vnd.balsamiq.bmml+xml":["bmml"],"application/vnd.blueice.multipass":["mpm"],"application/vnd.bmi":["bmi"],"application/vnd.businessobjects":["rep"],"application/vnd.chemdraw+xml":["cdxml"],"application/vnd.chipnuts.karaoke-mmd":["mmd"],"application/vnd.cinderella":["cdy"],"application/vnd.citationstyles.style+xml":["csl"],"application/vnd.claymore":["cla"],"application/vnd.cloanto.rp9":["rp9"],"application/vnd.clonk.c4group":["c4g","c4d","c4f","c4p","c4u"],"application/vnd.cluetrust.cartomobile-config":["c11amc"],"application/vnd.cluetrust.cartomobile-config-pkg":["c11amz"],"application/vnd.commonspace":["csp"],"application/vnd.contact.cmsg":["cdbcmsg"],"application/vnd.cosmocaller":["cmc"],"application/vnd.crick.clicker":["clkx"],"application/vnd.crick.clicker.keyboard":["clkk"],"application/vnd.crick.clicker.palette":["clkp"],"application/vnd.crick.clicker.template":["clkt"],"application/vnd.crick.clicker.wordbank":["clkw"],"application/vnd.criticaltools.wbs+xml":["wbs"],"application/vnd.ctc-posml":["pml"],"application/vnd.cups-ppd":["ppd"],"application/vnd.curl.car":["car"],"application/vnd.curl.pcurl":["pcurl"],"application/vnd.dart":["dart"],"application/vnd.data-vision.rdz":["rdz"],"application/vnd.dbf":["dbf"],"application/vnd.dece.data":["uvf","uvvf","uvd","uvvd"],"application/vnd.dece.ttml+xml":["uvt","uvvt"],"application/vnd.dece.unspecified":["uvx","uvvx"],"application/vnd.dece.zip":["uvz","uvvz"],"application/vnd.denovo.fcselayout-link":["fe_launch"],"application/vnd.dna":["dna"],"application/vnd.dolby.mlp":["mlp"],"application/vnd.dpgraph":["dpg"],"application/vnd.dreamfactory":["dfac"],"application/vnd.ds-keypoint":["kpxx"],"application/vnd.dvb.ait":["ait"],"application/vnd.dvb.service":["svc"],"application/vnd.dynageo":["geo"],"application/vnd.ecowin.chart":["mag"],"application/vnd.enliven":["nml"],"application/vnd.epson.esf":["esf"],"application/vnd.epson.msf":["msf"],"application/vnd.epson.quickanime":["qam"],"application/vnd.epson.salt":["slt"],"application/vnd.epson.ssf":["ssf"],"application/vnd.eszigno3+xml":["es3","et3"],"application/vnd.ezpix-album":["ez2"],"application/vnd.ezpix-package":["ez3"],"application/vnd.fdf":["fdf"],"application/vnd.fdsn.mseed":["mseed"],"application/vnd.fdsn.seed":["seed","dataless"],"application/vnd.flographit":["gph"],"application/vnd.fluxtime.clip":["ftc"],"application/vnd.framemaker":["fm","frame","maker","book"],"application/vnd.frogans.fnc":["fnc"],"application/vnd.frogans.ltf":["ltf"],"application/vnd.fsc.weblaunch":["fsc"],"application/vnd.fujitsu.oasys":["oas"],"application/vnd.fujitsu.oasys2":["oa2"],"application/vnd.fujitsu.oasys3":["oa3"],"application/vnd.fujitsu.oasysgp":["fg5"],"application/vnd.fujitsu.oasysprs":["bh2"],"application/vnd.fujixerox.ddd":["ddd"],"application/vnd.fujixerox.docuworks":["xdw"],"application/vnd.fujixerox.docuworks.binder":["xbd"],"application/vnd.fuzzysheet":["fzs"],"application/vnd.genomatix.tuxedo":["txd"],"application/vnd.geogebra.file":["ggb"],"application/vnd.geogebra.tool":["ggt"],"application/vnd.geometry-explorer":["gex","gre"],"application/vnd.geonext":["gxt"],"application/vnd.geoplan":["g2w"],"application/vnd.geospace":["g3w"],"application/vnd.gmx":["gmx"],"application/vnd.google-apps.document":["gdoc"],"application/vnd.google-apps.presentation":["gslides"],"application/vnd.google-apps.spreadsheet":["gsheet"],"application/vnd.google-earth.kml+xml":["kml"],"application/vnd.google-earth.kmz":["kmz"],"application/vnd.grafeq":["gqf","gqs"],"application/vnd.groove-account":["gac"],"application/vnd.groove-help":["ghf"],"application/vnd.groove-identity-message":["gim"],"application/vnd.groove-injector":["grv"],"application/vnd.groove-tool-message":["gtm"],"application/vnd.groove-tool-template":["tpl"],"application/vnd.groove-vcard":["vcg"],"application/vnd.hal+xml":["hal"],"application/vnd.handheld-entertainment+xml":["zmm"],"application/vnd.hbci":["hbci"],"application/vnd.hhe.lesson-player":["les"],"application/vnd.hp-hpgl":["hpgl"],"application/vnd.hp-hpid":["hpid"],"application/vnd.hp-hps":["hps"],"application/vnd.hp-jlyt":["jlt"],"application/vnd.hp-pcl":["pcl"],"application/vnd.hp-pclxl":["pclxl"],"application/vnd.hydrostatix.sof-data":["sfd-hdstx"],"application/vnd.ibm.minipay":["mpy"],"application/vnd.ibm.modcap":["afp","listafp","list3820"],"application/vnd.ibm.rights-management":["irm"],"application/vnd.ibm.secure-container":["sc"],"application/vnd.iccprofile":["icc","icm"],"application/vnd.igloader":["igl"],"application/vnd.immervision-ivp":["ivp"],"application/vnd.immervision-ivu":["ivu"],"application/vnd.insors.igm":["igm"],"application/vnd.intercon.formnet":["xpw","xpx"],"application/vnd.intergeo":["i2g"],"application/vnd.intu.qbo":["qbo"],"application/vnd.intu.qfx":["qfx"],"application/vnd.ipunplugged.rcprofile":["rcprofile"],"application/vnd.irepository.package+xml":["irp"],"application/vnd.is-xpr":["xpr"],"application/vnd.isac.fcs":["fcs"],"application/vnd.jam":["jam"],"application/vnd.jcp.javame.midlet-rms":["rms"],"application/vnd.jisp":["jisp"],"application/vnd.joost.joda-archive":["joda"],"application/vnd.kahootz":["ktz","ktr"],"application/vnd.kde.karbon":["karbon"],"application/vnd.kde.kchart":["chrt"],"application/vnd.kde.kformula":["kfo"],"application/vnd.kde.kivio":["flw"],"application/vnd.kde.kontour":["kon"],"application/vnd.kde.kpresenter":["kpr","kpt"],"application/vnd.kde.kspread":["ksp"],"application/vnd.kde.kword":["kwd","kwt"],"application/vnd.kenameaapp":["htke"],"application/vnd.kidspiration":["kia"],"application/vnd.kinar":["kne","knp"],"application/vnd.koan":["skp","skd","skt","skm"],"application/vnd.kodak-descriptor":["sse"],"application/vnd.las.las+xml":["lasxml"],"application/vnd.llamagraphics.life-balance.desktop":["lbd"],"application/vnd.llamagraphics.life-balance.exchange+xml":["lbe"],"application/vnd.lotus-1-2-3":["123"],"application/vnd.lotus-approach":["apr"],"application/vnd.lotus-freelance":["pre"],"application/vnd.lotus-notes":["nsf"],"application/vnd.lotus-organizer":["org"],"application/vnd.lotus-screencam":["scm"],"application/vnd.lotus-wordpro":["lwp"],"application/vnd.macports.portpkg":["portpkg"],"application/vnd.mapbox-vector-tile":["mvt"],"application/vnd.mcd":["mcd"],"application/vnd.medcalcdata":["mc1"],"application/vnd.mediastation.cdkey":["cdkey"],"application/vnd.mfer":["mwf"],"application/vnd.mfmp":["mfm"],"application/vnd.micrografx.flo":["flo"],"application/vnd.micrografx.igx":["igx"],"application/vnd.mif":["mif"],"application/vnd.mobius.daf":["daf"],"application/vnd.mobius.dis":["dis"],"application/vnd.mobius.mbk":["mbk"],"application/vnd.mobius.mqy":["mqy"],"application/vnd.mobius.msl":["msl"],"application/vnd.mobius.plc":["plc"],"application/vnd.mobius.txf":["txf"],"application/vnd.mophun.application":["mpn"],"application/vnd.mophun.certificate":["mpc"],"application/vnd.mozilla.xul+xml":["xul"],"application/vnd.ms-artgalry":["cil"],"application/vnd.ms-cab-compressed":["cab"],"application/vnd.ms-excel":["xls","xlm","xla","xlc","xlt","xlw"],"application/vnd.ms-excel.addin.macroenabled.12":["xlam"],"application/vnd.ms-excel.sheet.binary.macroenabled.12":["xlsb"],"application/vnd.ms-excel.sheet.macroenabled.12":["xlsm"],"application/vnd.ms-excel.template.macroenabled.12":["xltm"],"application/vnd.ms-fontobject":["eot"],"application/vnd.ms-htmlhelp":["chm"],"application/vnd.ms-ims":["ims"],"application/vnd.ms-lrm":["lrm"],"application/vnd.ms-officetheme":["thmx"],"application/vnd.ms-outlook":["msg"],"application/vnd.ms-pki.seccat":["cat"],"application/vnd.ms-pki.stl":["*stl"],"application/vnd.ms-powerpoint":["ppt","pps","pot"],"application/vnd.ms-powerpoint.addin.macroenabled.12":["ppam"],"application/vnd.ms-powerpoint.presentation.macroenabled.12":["pptm"],"application/vnd.ms-powerpoint.slide.macroenabled.12":["sldm"],"application/vnd.ms-powerpoint.slideshow.macroenabled.12":["ppsm"],"application/vnd.ms-powerpoint.template.macroenabled.12":["potm"],"application/vnd.ms-project":["mpp","mpt"],"application/vnd.ms-word.document.macroenabled.12":["docm"],"application/vnd.ms-word.template.macroenabled.12":["dotm"],"application/vnd.ms-works":["wps","wks","wcm","wdb"],"application/vnd.ms-wpl":["wpl"],"application/vnd.ms-xpsdocument":["xps"],"application/vnd.mseq":["mseq"],"application/vnd.musician":["mus"],"application/vnd.muvee.style":["msty"],"application/vnd.mynfc":["taglet"],"application/vnd.neurolanguage.nlu":["nlu"],"application/vnd.nitf":["ntf","nitf"],"application/vnd.noblenet-directory":["nnd"],"application/vnd.noblenet-sealer":["nns"],"application/vnd.noblenet-web":["nnw"],"application/vnd.nokia.n-gage.ac+xml":["*ac"],"application/vnd.nokia.n-gage.data":["ngdat"],"application/vnd.nokia.n-gage.symbian.install":["n-gage"],"application/vnd.nokia.radio-preset":["rpst"],"application/vnd.nokia.radio-presets":["rpss"],"application/vnd.novadigm.edm":["edm"],"application/vnd.novadigm.edx":["edx"],"application/vnd.novadigm.ext":["ext"],"application/vnd.oasis.opendocument.chart":["odc"],"application/vnd.oasis.opendocument.chart-template":["otc"],"application/vnd.oasis.opendocument.database":["odb"],"application/vnd.oasis.opendocument.formula":["odf"],"application/vnd.oasis.opendocument.formula-template":["odft"],"application/vnd.oasis.opendocument.graphics":["odg"],"application/vnd.oasis.opendocument.graphics-template":["otg"],"application/vnd.oasis.opendocument.image":["odi"],"application/vnd.oasis.opendocument.image-template":["oti"],"application/vnd.oasis.opendocument.presentation":["odp"],"application/vnd.oasis.opendocument.presentation-template":["otp"],"application/vnd.oasis.opendocument.spreadsheet":["ods"],"application/vnd.oasis.opendocument.spreadsheet-template":["ots"],"application/vnd.oasis.opendocument.text":["odt"],"application/vnd.oasis.opendocument.text-master":["odm"],"application/vnd.oasis.opendocument.text-template":["ott"],"application/vnd.oasis.opendocument.text-web":["oth"],"application/vnd.olpc-sugar":["xo"],"application/vnd.oma.dd2+xml":["dd2"],"application/vnd.openblox.game+xml":["obgx"],"application/vnd.openofficeorg.extension":["oxt"],"application/vnd.openstreetmap.data+xml":["osm"],"application/vnd.openxmlformats-officedocument.presentationml.presentation":["pptx"],"application/vnd.openxmlformats-officedocument.presentationml.slide":["sldx"],"application/vnd.openxmlformats-officedocument.presentationml.slideshow":["ppsx"],"application/vnd.openxmlformats-officedocument.presentationml.template":["potx"],"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":["xlsx"],"application/vnd.openxmlformats-officedocument.spreadsheetml.template":["xltx"],"application/vnd.openxmlformats-officedocument.wordprocessingml.document":["docx"],"application/vnd.openxmlformats-officedocument.wordprocessingml.template":["dotx"],"application/vnd.osgeo.mapguide.package":["mgp"],"application/vnd.osgi.dp":["dp"],"application/vnd.osgi.subsystem":["esa"],"application/vnd.palm":["pdb","pqa","oprc"],"application/vnd.pawaafile":["paw"],"application/vnd.pg.format":["str"],"application/vnd.pg.osasli":["ei6"],"application/vnd.picsel":["efif"],"application/vnd.pmi.widget":["wg"],"application/vnd.pocketlearn":["plf"],"application/vnd.powerbuilder6":["pbd"],"application/vnd.previewsystems.box":["box"],"application/vnd.proteus.magazine":["mgz"],"application/vnd.publishare-delta-tree":["qps"],"application/vnd.pvi.ptid1":["ptid"],"application/vnd.quark.quarkxpress":["qxd","qxt","qwd","qwt","qxl","qxb"],"application/vnd.rar":["rar"],"application/vnd.realvnc.bed":["bed"],"application/vnd.recordare.musicxml":["mxl"],"application/vnd.recordare.musicxml+xml":["musicxml"],"application/vnd.rig.cryptonote":["cryptonote"],"application/vnd.rim.cod":["cod"],"application/vnd.rn-realmedia":["rm"],"application/vnd.rn-realmedia-vbr":["rmvb"],"application/vnd.route66.link66+xml":["link66"],"application/vnd.sailingtracker.track":["st"],"application/vnd.seemail":["see"],"application/vnd.sema":["sema"],"application/vnd.semd":["semd"],"application/vnd.semf":["semf"],"application/vnd.shana.informed.formdata":["ifm"],"application/vnd.shana.informed.formtemplate":["itp"],"application/vnd.shana.informed.interchange":["iif"],"application/vnd.shana.informed.package":["ipk"],"application/vnd.simtech-mindmapper":["twd","twds"],"application/vnd.smaf":["mmf"],"application/vnd.smart.teacher":["teacher"],"application/vnd.software602.filler.form+xml":["fo"],"application/vnd.solent.sdkm+xml":["sdkm","sdkd"],"application/vnd.spotfire.dxp":["dxp"],"application/vnd.spotfire.sfs":["sfs"],"application/vnd.stardivision.calc":["sdc"],"application/vnd.stardivision.draw":["sda"],"application/vnd.stardivision.impress":["sdd"],"application/vnd.stardivision.math":["smf"],"application/vnd.stardivision.writer":["sdw","vor"],"application/vnd.stardivision.writer-global":["sgl"],"application/vnd.stepmania.package":["smzip"],"application/vnd.stepmania.stepchart":["sm"],"application/vnd.sun.wadl+xml":["wadl"],"application/vnd.sun.xml.calc":["sxc"],"application/vnd.sun.xml.calc.template":["stc"],"application/vnd.sun.xml.draw":["sxd"],"application/vnd.sun.xml.draw.template":["std"],"application/vnd.sun.xml.impress":["sxi"],"application/vnd.sun.xml.impress.template":["sti"],"application/vnd.sun.xml.math":["sxm"],"application/vnd.sun.xml.writer":["sxw"],"application/vnd.sun.xml.writer.global":["sxg"],"application/vnd.sun.xml.writer.template":["stw"],"application/vnd.sus-calendar":["sus","susp"],"application/vnd.svd":["svd"],"application/vnd.symbian.install":["sis","sisx"],"application/vnd.syncml+xml":["xsm"],"application/vnd.syncml.dm+wbxml":["bdm"],"application/vnd.syncml.dm+xml":["xdm"],"application/vnd.syncml.dmddf+xml":["ddf"],"application/vnd.tao.intent-module-archive":["tao"],"application/vnd.tcpdump.pcap":["pcap","cap","dmp"],"application/vnd.tmobile-livetv":["tmo"],"application/vnd.trid.tpt":["tpt"],"application/vnd.triscape.mxs":["mxs"],"application/vnd.trueapp":["tra"],"application/vnd.ufdl":["ufd","ufdl"],"application/vnd.uiq.theme":["utz"],"application/vnd.umajin":["umj"],"application/vnd.unity":["unityweb"],"application/vnd.uoml+xml":["uoml"],"application/vnd.vcx":["vcx"],"application/vnd.visio":["vsd","vst","vss","vsw"],"application/vnd.visionary":["vis"],"application/vnd.vsf":["vsf"],"application/vnd.wap.wbxml":["wbxml"],"application/vnd.wap.wmlc":["wmlc"],"application/vnd.wap.wmlscriptc":["wmlsc"],"application/vnd.webturbo":["wtb"],"application/vnd.wolfram.player":["nbp"],"application/vnd.wordperfect":["wpd"],"application/vnd.wqd":["wqd"],"application/vnd.wt.stf":["stf"],"application/vnd.xara":["xar"],"application/vnd.xfdl":["xfdl"],"application/vnd.yamaha.hv-dic":["hvd"],"application/vnd.yamaha.hv-script":["hvs"],"application/vnd.yamaha.hv-voice":["hvp"],"application/vnd.yamaha.openscoreformat":["osf"],"application/vnd.yamaha.openscoreformat.osfpvg+xml":["osfpvg"],"application/vnd.yamaha.smaf-audio":["saf"],"application/vnd.yamaha.smaf-phrase":["spf"],"application/vnd.yellowriver-custom-menu":["cmp"],"application/vnd.zul":["zir","zirz"],"application/vnd.zzazz.deck+xml":["zaz"],"application/x-7z-compressed":["7z"],"application/x-abiword":["abw"],"application/x-ace-compressed":["ace"],"application/x-apple-diskimage":["*dmg"],"application/x-arj":["arj"],"application/x-authorware-bin":["aab","x32","u32","vox"],"application/x-authorware-map":["aam"],"application/x-authorware-seg":["aas"],"application/x-bcpio":["bcpio"],"application/x-bdoc":["*bdoc"],"application/x-bittorrent":["torrent"],"application/x-blorb":["blb","blorb"],"application/x-bzip":["bz"],"application/x-bzip2":["bz2","boz"],"application/x-cbr":["cbr","cba","cbt","cbz","cb7"],"application/x-cdlink":["vcd"],"application/x-cfs-compressed":["cfs"],"application/x-chat":["chat"],"application/x-chess-pgn":["pgn"],"application/x-chrome-extension":["crx"],"application/x-cocoa":["cco"],"application/x-conference":["nsc"],"application/x-cpio":["cpio"],"application/x-csh":["csh"],"application/x-debian-package":["*deb","udeb"],"application/x-dgc-compressed":["dgc"],"application/x-director":["dir","dcr","dxr","cst","cct","cxt","w3d","fgd","swa"],"application/x-doom":["wad"],"application/x-dtbncx+xml":["ncx"],"application/x-dtbook+xml":["dtb"],"application/x-dtbresource+xml":["res"],"application/x-dvi":["dvi"],"application/x-envoy":["evy"],"application/x-eva":["eva"],"application/x-font-bdf":["bdf"],"application/x-font-ghostscript":["gsf"],"application/x-font-linux-psf":["psf"],"application/x-font-pcf":["pcf"],"application/x-font-snf":["snf"],"application/x-font-type1":["pfa","pfb","pfm","afm"],"application/x-freearc":["arc"],"application/x-futuresplash":["spl"],"application/x-gca-compressed":["gca"],"application/x-glulx":["ulx"],"application/x-gnumeric":["gnumeric"],"application/x-gramps-xml":["gramps"],"application/x-gtar":["gtar"],"application/x-hdf":["hdf"],"application/x-httpd-php":["php"],"application/x-install-instructions":["install"],"application/x-iso9660-image":["*iso"],"application/x-iwork-keynote-sffkey":["*key"],"application/x-iwork-numbers-sffnumbers":["*numbers"],"application/x-iwork-pages-sffpages":["*pages"],"application/x-java-archive-diff":["jardiff"],"application/x-java-jnlp-file":["jnlp"],"application/x-keepass2":["kdbx"],"application/x-latex":["latex"],"application/x-lua-bytecode":["luac"],"application/x-lzh-compressed":["lzh","lha"],"application/x-makeself":["run"],"application/x-mie":["mie"],"application/x-mobipocket-ebook":["prc","mobi"],"application/x-ms-application":["application"],"application/x-ms-shortcut":["lnk"],"application/x-ms-wmd":["wmd"],"application/x-ms-wmz":["wmz"],"application/x-ms-xbap":["xbap"],"application/x-msaccess":["mdb"],"application/x-msbinder":["obd"],"application/x-mscardfile":["crd"],"application/x-msclip":["clp"],"application/x-msdos-program":["*exe"],"application/x-msdownload":["*exe","*dll","com","bat","*msi"],"application/x-msmediaview":["mvb","m13","m14"],"application/x-msmetafile":["*wmf","*wmz","*emf","emz"],"application/x-msmoney":["mny"],"application/x-mspublisher":["pub"],"application/x-msschedule":["scd"],"application/x-msterminal":["trm"],"application/x-mswrite":["wri"],"application/x-netcdf":["nc","cdf"],"application/x-ns-proxy-autoconfig":["pac"],"application/x-nzb":["nzb"],"application/x-perl":["pl","pm"],"application/x-pilot":["*prc","*pdb"],"application/x-pkcs12":["p12","pfx"],"application/x-pkcs7-certificates":["p7b","spc"],"application/x-pkcs7-certreqresp":["p7r"],"application/x-rar-compressed":["*rar"],"application/x-redhat-package-manager":["rpm"],"application/x-research-info-systems":["ris"],"application/x-sea":["sea"],"application/x-sh":["sh"],"application/x-shar":["shar"],"application/x-shockwave-flash":["swf"],"application/x-silverlight-app":["xap"],"application/x-sql":["sql"],"application/x-stuffit":["sit"],"application/x-stuffitx":["sitx"],"application/x-subrip":["srt"],"application/x-sv4cpio":["sv4cpio"],"application/x-sv4crc":["sv4crc"],"application/x-t3vm-image":["t3"],"application/x-tads":["gam"],"application/x-tar":["tar"],"application/x-tcl":["tcl","tk"],"application/x-tex":["tex"],"application/x-tex-tfm":["tfm"],"application/x-texinfo":["texinfo","texi"],"application/x-tgif":["*obj"],"application/x-ustar":["ustar"],"application/x-virtualbox-hdd":["hdd"],"application/x-virtualbox-ova":["ova"],"application/x-virtualbox-ovf":["ovf"],"application/x-virtualbox-vbox":["vbox"],"application/x-virtualbox-vbox-extpack":["vbox-extpack"],"application/x-virtualbox-vdi":["vdi"],"application/x-virtualbox-vhd":["vhd"],"application/x-virtualbox-vmdk":["vmdk"],"application/x-wais-source":["src"],"application/x-web-app-manifest+json":["webapp"],"application/x-x509-ca-cert":["der","crt","pem"],"application/x-xfig":["fig"],"application/x-xliff+xml":["*xlf"],"application/x-xpinstall":["xpi"],"application/x-xz":["xz"],"application/x-zmachine":["z1","z2","z3","z4","z5","z6","z7","z8"],"audio/vnd.dece.audio":["uva","uvva"],"audio/vnd.digital-winds":["eol"],"audio/vnd.dra":["dra"],"audio/vnd.dts":["dts"],"audio/vnd.dts.hd":["dtshd"],"audio/vnd.lucent.voice":["lvp"],"audio/vnd.ms-playready.media.pya":["pya"],"audio/vnd.nuera.ecelp4800":["ecelp4800"],"audio/vnd.nuera.ecelp7470":["ecelp7470"],"audio/vnd.nuera.ecelp9600":["ecelp9600"],"audio/vnd.rip":["rip"],"audio/x-aac":["aac"],"audio/x-aiff":["aif","aiff","aifc"],"audio/x-caf":["caf"],"audio/x-flac":["flac"],"audio/x-m4a":["*m4a"],"audio/x-matroska":["mka"],"audio/x-mpegurl":["m3u"],"audio/x-ms-wax":["wax"],"audio/x-ms-wma":["wma"],"audio/x-pn-realaudio":["ram","ra"],"audio/x-pn-realaudio-plugin":["rmp"],"audio/x-realaudio":["*ra"],"audio/x-wav":["*wav"],"chemical/x-cdx":["cdx"],"chemical/x-cif":["cif"],"chemical/x-cmdf":["cmdf"],"chemical/x-cml":["cml"],"chemical/x-csml":["csml"],"chemical/x-xyz":["xyz"],"image/prs.btif":["btif"],"image/prs.pti":["pti"],"image/vnd.adobe.photoshop":["psd"],"image/vnd.airzip.accelerator.azv":["azv"],"image/vnd.dece.graphic":["uvi","uvvi","uvg","uvvg"],"image/vnd.djvu":["djvu","djv"],"image/vnd.dvb.subtitle":["*sub"],"image/vnd.dwg":["dwg"],"image/vnd.dxf":["dxf"],"image/vnd.fastbidsheet":["fbs"],"image/vnd.fpx":["fpx"],"image/vnd.fst":["fst"],"image/vnd.fujixerox.edmics-mmr":["mmr"],"image/vnd.fujixerox.edmics-rlc":["rlc"],"image/vnd.microsoft.icon":["ico"],"image/vnd.ms-dds":["dds"],"image/vnd.ms-modi":["mdi"],"image/vnd.ms-photo":["wdp"],"image/vnd.net-fpx":["npx"],"image/vnd.pco.b16":["b16"],"image/vnd.tencent.tap":["tap"],"image/vnd.valve.source.texture":["vtf"],"image/vnd.wap.wbmp":["wbmp"],"image/vnd.xiff":["xif"],"image/vnd.zbrush.pcx":["pcx"],"image/x-3ds":["3ds"],"image/x-cmu-raster":["ras"],"image/x-cmx":["cmx"],"image/x-freehand":["fh","fhc","fh4","fh5","fh7"],"image/x-icon":["*ico"],"image/x-jng":["jng"],"image/x-mrsid-image":["sid"],"image/x-ms-bmp":["*bmp"],"image/x-pcx":["*pcx"],"image/x-pict":["pic","pct"],"image/x-portable-anymap":["pnm"],"image/x-portable-bitmap":["pbm"],"image/x-portable-graymap":["pgm"],"image/x-portable-pixmap":["ppm"],"image/x-rgb":["rgb"],"image/x-tga":["tga"],"image/x-xbitmap":["xbm"],"image/x-xpixmap":["xpm"],"image/x-xwindowdump":["xwd"],"message/vnd.wfa.wsc":["wsc"],"model/vnd.collada+xml":["dae"],"model/vnd.dwf":["dwf"],"model/vnd.gdl":["gdl"],"model/vnd.gtw":["gtw"],"model/vnd.mts":["mts"],"model/vnd.opengex":["ogex"],"model/vnd.parasolid.transmit.binary":["x_b"],"model/vnd.parasolid.transmit.text":["x_t"],"model/vnd.sap.vds":["vds"],"model/vnd.usdz+zip":["usdz"],"model/vnd.valve.source.compiled-map":["bsp"],"model/vnd.vtu":["vtu"],"text/prs.lines.tag":["dsc"],"text/vnd.curl":["curl"],"text/vnd.curl.dcurl":["dcurl"],"text/vnd.curl.mcurl":["mcurl"],"text/vnd.curl.scurl":["scurl"],"text/vnd.dvb.subtitle":["sub"],"text/vnd.fly":["fly"],"text/vnd.fmi.flexstor":["flx"],"text/vnd.graphviz":["gv"],"text/vnd.in3d.3dml":["3dml"],"text/vnd.in3d.spot":["spot"],"text/vnd.sun.j2me.app-descriptor":["jad"],"text/vnd.wap.wml":["wml"],"text/vnd.wap.wmlscript":["wmls"],"text/x-asm":["s","asm"],"text/x-c":["c","cc","cxx","cpp","h","hh","dic"],"text/x-component":["htc"],"text/x-fortran":["f","for","f77","f90"],"text/x-handlebars-template":["hbs"],"text/x-java-source":["java"],"text/x-lua":["lua"],"text/x-markdown":["mkd"],"text/x-nfo":["nfo"],"text/x-opml":["opml"],"text/x-org":["*org"],"text/x-pascal":["p","pas"],"text/x-processing":["pde"],"text/x-sass":["sass"],"text/x-scss":["scss"],"text/x-setext":["etx"],"text/x-sfv":["sfv"],"text/x-suse-ymp":["ymp"],"text/x-uuencode":["uu"],"text/x-vcalendar":["vcs"],"text/x-vcard":["vcf"],"video/vnd.dece.hd":["uvh","uvvh"],"video/vnd.dece.mobile":["uvm","uvvm"],"video/vnd.dece.pd":["uvp","uvvp"],"video/vnd.dece.sd":["uvs","uvvs"],"video/vnd.dece.video":["uvv","uvvv"],"video/vnd.dvb.file":["dvb"],"video/vnd.fvt":["fvt"],"video/vnd.mpegurl":["mxu","m4u"],"video/vnd.ms-playready.media.pyv":["pyv"],"video/vnd.uvvu.mp4":["uvu","uvvu"],"video/vnd.vivo":["viv"],"video/x-f4v":["f4v"],"video/x-fli":["fli"],"video/x-flv":["flv"],"video/x-m4v":["m4v"],"video/x-matroska":["mkv","mk3d","mks"],"video/x-mng":["mng"],"video/x-ms-asf":["asf","asx"],"video/x-ms-vob":["vob"],"video/x-ms-wm":["wm"],"video/x-ms-wmv":["wmv"],"video/x-ms-wmx":["wmx"],"video/x-ms-wvx":["wvx"],"video/x-msvideo":["avi"],"video/x-sgi-movie":["movie"],"video/x-smv":["smv"],"x-conference/x-cooltalk":["ice"]}},function(e,t,n){function o(){
/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */
o=function(){return e};var e={},t=Object.prototype,n=t.hasOwnProperty,i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",s=i.asyncIterator||"@@asyncIterator",c=i.toStringTag||"@@toStringTag";function l(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{l({},"")}catch(w){l=function(e,t,n){return e[t]=n}}function u(e,t,n,o){var r=t&&t.prototype instanceof f?t:f,i=Object.create(r.prototype),a=new E(o||[]);return i._invoke=function(e,t,n){var o="suspendedStart";return function(r,i){if("executing"===o)throw new Error("Generator is already running");if("completed"===o){if("throw"===r)throw i;return{value:void 0,done:!0}}for(n.method=r,n.arg=i;;){var a=n.delegate;if(a){var s=k(a,n);if(s){if(s===d)continue;return s}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===o)throw o="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o="executing";var c=p(e,t,n);if("normal"===c.type){if(o=n.done?"completed":"suspendedYield",c.arg===d)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(o="completed",n.method="throw",n.arg=c.arg)}}}(e,n,a),i}function p(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(o){return{type:"throw",arg:o}}}e.wrap=u;var d={};function f(){}function h(){}function m(){}var g={};l(g,a,function(){return this});var v=Object.getPrototypeOf,y=v&&v(v(A([])));y&&y!==t&&n.call(y,a)&&(g=y);var x=m.prototype=f.prototype=Object.create(g);function b(e){["next","throw","return"].forEach(function(t){l(e,t,function(e){return this._invoke(t,e)})})}function _(e,t){function o(i,a,s,c){var l=p(e[i],e,a);if("throw"!==l.type){var u=l.arg,d=u.value;return d&&"object"==r(d)&&n.call(d,"__await")?t.resolve(d.__await).then(function(e){o("next",e,s,c)},function(e){o("throw",e,s,c)}):t.resolve(d).then(function(e){u.value=e,s(u)},function(e){return o("throw",e,s,c)})}c(l.arg)}var i;this._invoke=function(e,n){function r(){return new t(function(t,r){o(e,n,t,r)})}return i=i?i.then(r,r):r()}}function k(e,t){var n=e.iterator[t.method];if(void 0===n){if(t.delegate=null,"throw"===t.method){if(e.iterator.return&&(t.method="return",t.arg=void 0,k(e,t),"throw"===t.method))return d;t.method="throw",t.arg=new TypeError("The iterator does not provide a 'throw' method")}return d}var o=p(n,e.iterator,t.arg);if("throw"===o.type)return t.method="throw",t.arg=o.arg,t.delegate=null,d;var r=o.arg;return r?r.done?(t[e.resultName]=r.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,d):r:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,d)}function C(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function S(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function E(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(C,this),this.reset(!0)}function A(e){if(e){var t=e[a];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,r=function t(){for(;++o<e.length;)if(n.call(e,o))return t.value=e[o],t.done=!1,t;return t.value=void 0,t.done=!0,t};return r.next=r}}return{next:O}}function O(){return{value:void 0,done:!0}}return h.prototype=m,l(x,"constructor",m),l(m,"constructor",h),h.displayName=l(m,c,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===h||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,m):(e.__proto__=m,l(e,c,"GeneratorFunction")),e.prototype=Object.create(x),e},e.awrap=function(e){return{__await:e}},b(_.prototype),l(_.prototype,s,function(){return this}),e.AsyncIterator=_,e.async=function(t,n,o,r,i){void 0===i&&(i=Promise);var a=new _(u(t,n,o,r),i);return e.isGeneratorFunction(n)?a:a.next().then(function(e){return e.done?e.value:a.next()})},b(x),l(x,c,"Generator"),l(x,a,function(){return this}),l(x,"toString",function(){return"[object Generator]"}),e.keys=function(e){var t=[];for(var n in e)t.push(n);return t.reverse(),function n(){for(;t.length;){var o=t.pop();if(o in e)return n.value=o,n.done=!1,n}return n.done=!0,n}},e.values=A,E.prototype={constructor:E,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(S),!e)for(var t in this)"t"===t.charAt(0)&&n.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function o(n,o){return a.type="throw",a.arg=e,t.next=n,o&&(t.method="next",t.arg=void 0),!!o}for(var r=this.tryEntries.length-1;r>=0;--r){var i=this.tryEntries[r],a=i.completion;if("root"===i.tryLoc)return o("end");if(i.tryLoc<=this.prev){var s=n.call(i,"catchLoc"),c=n.call(i,"finallyLoc");if(s&&c){if(this.prev<i.catchLoc)return o(i.catchLoc,!0);if(this.prev<i.finallyLoc)return o(i.finallyLoc)}else if(s){if(this.prev<i.catchLoc)return o(i.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return o(i.finallyLoc)}}}},abrupt:function(e,t){for(var o=this.tryEntries.length-1;o>=0;--o){var r=this.tryEntries[o];if(r.tryLoc<=this.prev&&n.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var i=r;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=e,a.arg=t,i?(this.method="next",this.next=i.finallyLoc,d):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),d},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),S(n),d}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var o=n.completion;if("throw"===o.type){var r=o.arg;S(n)}return r}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,n){return this.delegate={iterator:A(e),resultName:t,nextLoc:n},"next"===this.method&&(this.arg=void 0),d}},e}function r(e){return(r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function i(e,t,n,o,r,i,a){try{var s=e[i](a),c=s.value}catch(l){return void n(l)}s.done?t(c):Promise.resolve(c).then(o,r)}function a(e){return function(){var t=this,n=arguments;return new Promise(function(o,r){var a=e.apply(t,n);function s(e){i(a,o,r,s,c,"next",e)}function c(e){i(a,o,r,s,c,"throw",e)}s(void 0)})}}var s=n(9),c=n(31),l=n(8).EventProxy,u=n(0),p=n(6);function d(e,t){var n=e.TaskId,o=e.Bucket,r=e.Region,i=e.Key,a=e.StorageClass,p=this,d={},m=e.FileSize,g=e.SliceSize,v=Math.ceil(m/g),y=0,x=u.throttleOnProgress.call(p,m,e.onHashProgress),b=new l;b.on("error",function(e){if(p._isRunningTask(n))return t(e)}),b.on("upload_id_available",function(e){var n={},o=[];u.each(e.PartList,function(e){n[e.PartNumber]=e});for(var r=1;r<=v;r++){var i=n[r];i?(i.PartNumber=r,i.Uploaded=!0):i={PartNumber:r,ETag:null,Uploaded:!1},o.push(i)}e.PartList=o,t(null,e)}),b.on("no_available_upload_id",function(){if(p._isRunningTask(n)){var s=u.extend({Bucket:o,Region:r,Key:i,Headers:u.clone(e.Headers),Query:u.clone(e.Query),StorageClass:a,calledBySdk:"sliceUploadFile",tracker:e.tracker},e);p.multipartInit(s,function(e,o){if(p._isRunningTask(n)){if(e)return b.emit("error",e);var r=o.UploadId;if(!r)return t({Message:"no upload id"});b.emit("upload_id_available",{UploadId:r,PartList:[]})}})}}),b.on("has_and_check_upload_id",function(t){t=t.reverse(),c.eachLimit(t,1,function(t,a){p._isRunningTask(n)&&(s.using[t]?a():h.call(p,{Bucket:o,Region:r,Key:i,UploadId:t,tracker:e.tracker},function(o,r){if(p._isRunningTask(n)){if(o)return s.removeUsing(t),b.emit("error",o);var i=r.PartList;i.forEach(function(e){e.PartNumber*=1,e.Size*=1,e.ETag=e.ETag||""}),function(t,n){var o=t.length;0===o?n(null,!0):o>v||o>1&&Math.max(t[0].Size,t[1].Size)!==g?n(null,!1):function r(i){if(i<o){var a=t[i];s=a.PartNumber,c=function(e,t){t&&t.ETag===a.ETag&&t.Size===a.Size?r(i+1):n(null,!1)},l=g*(s-1),p=Math.min(l+g,m),f=p-l,d[s]?c(0,{PartNumber:s,ETag:d[s],Size:f}):u.fileSlice(e.FilePath,l,p,function(e){try{var t=u.getFileMd5(e)}catch(o){return c()}var n='"'+t+'"';d[s]=n,y+=f,c(0,{PartNumber:s,ETag:n,Size:f}),x({loaded:y,total:m})})}else n(null,!0);var s,c,l,p,f}(0)}(i,function(e,o){if(p._isRunningTask(n))return e?b.emit("error",e):void(o?a({UploadId:t,PartList:i}):a())})}}))},function(e){p._isRunningTask(n)&&(x(null,!0),e&&e.UploadId?b.emit("upload_id_available",e):b.emit("no_available_upload_id"))})}),b.on("seek_local_avail_upload_id",function(t){var a=s.getFileId(e.FileStat,e.ChunkSize,o,i),c=s.getUploadIdList(a);a&&c?function a(l){if(l>=c.length)b.emit("has_and_check_upload_id",t);else{var d=c[l];if(!u.isInArray(t,d))return s.removeUploadId(d),void a(l+1);s.using[d]?a(l+1):h.call(p,{Bucket:o,Region:r,Key:i,UploadId:d,tracker:e.tracker},function(e,t){p._isRunningTask(n)&&(e?(s.removeUploadId(d),a(l+1)):b.emit("upload_id_available",{UploadId:d,PartList:t.PartList}))})}}(0):b.emit("has_and_check_upload_id",t)}),b.on("get_remote_upload_id_list",function(){f.call(p,{Bucket:o,Region:r,Key:i,tracker:e.tracker},function(t,r){if(p._isRunningTask(n)){if(t)return b.emit("error",t);var c=u.filter(r.UploadList,function(e){return e.Key===i&&(!a||e.StorageClass.toUpperCase()===a.toUpperCase())}).reverse().map(function(e){return e.UploadId||e.UploadID});if(c.length)b.emit("seek_local_avail_upload_id",c);else{var l,d=s.getFileId(e.FileStat,e.ChunkSize,o,i);d&&(l=s.getUploadIdList(d))&&u.each(l,function(e){s.removeUploadId(e)}),b.emit("no_available_upload_id")}}})}),b.emit("get_remote_upload_id_list")}function f(e,t){var n=this,o=[],r={Bucket:e.Bucket,Region:e.Region,Prefix:e.Key,calledBySdk:e.calledBySdk||"sliceUploadFile",tracker:e.tracker};!function e(){n.multipartList(r,function(n,i){if(n)return t(n);o.push.apply(o,i.Upload||[]),"true"===i.IsTruncated?(r.KeyMarker=i.NextKeyMarker,r.UploadIdMarker=i.NextUploadIdMarker,e()):t(null,{UploadList:o})})}()}function h(e,t){var n=this,o=[],r={Bucket:e.Bucket,Region:e.Region,Key:e.Key,UploadId:e.UploadId,calledBySdk:"sliceUploadFile",tracker:e.tracker};!function e(){n.multipartListPart(r,function(n,i){if(n)return t(n);o.push.apply(o,i.Part||[]),"true"===i.IsTruncated?(r.PartNumberMarker=i.NextPartNumberMarker,e()):t(null,{PartList:o})})}()}function m(e,t){var n=this,o=e.TaskId,r=e.Bucket,i=e.Region,a=e.Key,s=e.UploadData,l=e.FileSize,p=e.SliceSize,d=Math.min(e.AsyncLimit||n.options.ChunkParallelLimit||1,256),f=e.FilePath,h=Math.ceil(l/p),m=0,v=e.ServerSideEncryption,y=u.filter(s.PartList,function(e){return e.Uploaded&&(m+=e.PartNumber>=h&&l%p||p),!e.Uploaded}),x=e.onProgress;c.eachLimit(y,d,function(t,c){if(n._isRunningTask(o)){var u=t.PartNumber,d=Math.min(l,t.PartNumber*p)-(t.PartNumber-1)*p,h=0;g.call(n,{TaskId:o,Bucket:r,Region:i,Key:a,SliceSize:p,FileSize:l,PartNumber:u,ServerSideEncryption:v,FilePath:f,UploadData:s,onProgress:function(e){m+=e.loaded-h,h=e.loaded,x({loaded:m,total:l})},tracker:e.tracker},function(e,r){n._isRunningTask(o)&&(e?m-=h:(m+=d-h,t.ETag=r.ETag),x({loaded:m,total:l}),c(e||null,r))})}},function(e){if(n._isRunningTask(o))return e?t(e):void t(null,{UploadId:s.UploadId,SliceList:s.PartList})})}function g(e,t){var n=this,o=e.TaskId,r=e.Bucket,i=e.Region,a=e.Key,s=e.FileSize,l=e.FilePath,p=1*e.PartNumber,d=e.SliceSize,f=e.ServerSideEncryption,h=e.UploadData,m=n.options.ChunkRetryTimes+1,g=e.Headers||{},v=d*(p-1),y=d,x=v+d;x>s&&(y=(x=s)-v);var b=["x-cos-traffic-limit","x-cos-mime-limit"],_={};u.each(g,function(e,t){b.indexOf(t)>-1&&(_[t]=e)}),u.fileSlice(l,v,x,function(s){var l=u.getFileMd5(s),d=l?u.binaryBase64(l):null,g=h.PartList[p-1];c.retry(m,function(t){n._isRunningTask(o)&&n.multipartUpload({TaskId:o,Bucket:r,Region:i,Key:a,ContentLength:y,PartNumber:p,UploadId:h.UploadId,ServerSideEncryption:f,Body:s,Headers:_,onProgress:e.onProgress,ContentMD5:d,calledBySdk:"sliceUploadFile",tracker:e.tracker},function(e,r){if(n._isRunningTask(o))return e?t(e):(g.Uploaded=!0,t(null,r))})},function(e,r){if(n._isRunningTask(o))return t(e,r)})})}function v(e,t){var n=e.Bucket,o=e.Region,r=e.Key,i=e.UploadId,a=e.SliceList,s=this,l=this.options.ChunkRetryTimes+1,u=a.map(function(e){return{PartNumber:e.PartNumber,ETag:e.ETag}});c.retry(l,function(t){s.multipartComplete({Bucket:n,Region:o,Key:r,UploadId:i,Parts:u,calledBySdk:"sliceUploadFile",tracker:e.tracker},t)},function(e,n){t(e,n)})}function y(e,t){var n=e.Bucket,o=e.Region,r=e.Key,i=e.AbortArray,a=e.AsyncLimit||1,s=this,l=0,u=new Array(i.length);c.eachLimit(i,a,function(t,i){var a=l;if(r&&r!==t.Key)return u[a]={error:{KeyNotMatch:!0}},void i(null);var c=t.UploadId||t.UploadID;s.multipartAbort({Bucket:n,Region:o,Key:t.Key,Headers:e.Headers,UploadId:c},function(e){var r={Bucket:n,Region:o,Key:t.Key,UploadId:c};u[a]={error:e,task:r},i(null)}),l++},function(e){if(e)return t(e);for(var n=[],o=[],r=0,i=u.length;r<i;r++){var a=u[r];a.task&&(a.error?o.push(a.task):n.push(a.task))}return t(null,{successList:n,errorList:o})})}function x(){return(x=a(o().mark(function e(t,n){var i,a,s,c,l,d,f,h,m,g,v;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return i=this,a=void 0===t.SliceSize?i.options.SliceSize:t.SliceSize,s=[],e.prev=3,e.next=6,u.getFileSizeByPath(t.FilePath);case 6:c=e.sent,e.next=12;break;case 9:e.prev=9,e.t0=e.catch(3),n({error:e.t0});case 12:l={TaskId:""},i.options.EnableTracker&&(d=i.options.UseAccelerate||"string"==typeof i.options.Domain&&i.options.Domain.includes("accelerate."),t.tracker=new p({bucket:t.Bucket,region:t.Region,apiName:"uploadFile",fileKey:t.Key,fileSize:c,accelerate:d,deepTracker:i.options.DeepTracker,customId:i.options.CustomId,delay:i.options.TrackerDelay})),u.each(t,function(e,t){"object"!==r(e)&&"function"!=typeof e&&(l[t]=e)}),f=t.onTaskReady,t.onTaskReady=function(e){l.TaskId=e,f&&f(e)},h=t.onFileFinish,m=function(e,o){t.tracker&&t.tracker.formatResult(e,o),h&&h(e,o,l),n&&n(e,o)},g="postObject"===i.options.SimpleUploadMethod?"postObject":"putObject",v=c>a?"sliceUploadFile":g,s.push({api:v,params:t,callback:m}),i._addTasks(s);case 23:case"end":return e.stop()}},e,this,[[3,9]])}))).apply(this,arguments)}function b(){return b=a(o().mark(function e(t,n){var i,s,c,l,d,f,h,m,g,v,y;return o().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return i=this,s=void 0===t.SliceSize?i.options.SliceSize:t.SliceSize,c=0,l=0,d=u.throttleOnProgress.call(i,l,t.onProgress),f=t.files.length,h=t.onFileFinish,m=Array(f),g=function(e,t,o){d(null,!0),h&&h(e,t,o),m[o.Index]={options:o,error:e,data:t},--f<=0&&n&&n(null,{files:m})},v=[],y=function(){return t.files.map(function(e,t){return new Promise((n=a(o().mark(function n(a){var f,h,m,y,x,b,_,k,C,S;return o().wrap(function(n){for(;;)switch(n.prev=n.next){case 0:return f=0,n.prev=1,n.next=4,u.getFileSizeByPath(e.FilePath);case 4:f=n.sent,n.next=9;break;case 7:n.prev=7,n.t0=n.catch(1);case 9:h={Index:t,TaskId:""},c+=f,i.options.EnableTracker&&(m=i.options.UseAccelerate||"string"==typeof i.options.Domain&&i.options.Domain.includes("accelerate."),e.tracker=new p({bucket:e.Bucket,region:e.Region,apiName:"uploadFiles",fileKey:e.Key,fileSize:f,accelerate:m,deepTracker:i.options.DeepTracker,customId:i.options.CustomId,delay:i.options.TrackerDelay})),u.each(e,function(e,t){"object"!==r(e)&&"function"!=typeof e&&(h[t]=e)}),y=e.onTaskReady,e.onTaskReady=function(e){h.TaskId=e,y&&y(e)},x=0,b=e.onProgress,e.onProgress=function(e){l=l-x+e.loaded,x=e.loaded,b&&b(e),d({loaded:l,total:c})},_=e.onFileFinish,k=function(t,n){e.tracker&&e.tracker.formatResult(t,n),_&&_(t,n),g&&g(t,n,h)},C="postObject"===i.options.SimpleUploadMethod?"postObject":"putObject",S=f>s?"sliceUploadFile":C,v.push({api:S,params:e,callback:k}),a(!0);case 24:case"end":return n.stop()}},n,null,[[1,7]])})),function(e){return n.apply(this,arguments)}));var n})},e.next=13,Promise.all(y());case 13:i._addTasks(v);case 14:case"end":return e.stop()}},e,this)})),b.apply(this,arguments)}function _(e,t){var n=e.TaskId,o=e.Bucket,r=e.Region,i=e.Key,a=e.CopySource,s=e.UploadId,l=1*e.PartNumber,u=e.CopySourceRange,p=this.options.ChunkRetryTimes+1,d=this;c.retry(p,function(t){d.uploadPartCopy({TaskId:n,Bucket:o,Region:r,Key:i,CopySource:a,UploadId:s,PartNumber:l,CopySourceRange:u,onProgress:e.onProgress},function(e,n){t(e||null,n)})},function(e,n){return t(e,n)})}var k={sliceUploadFile:function(e,t){var n=this;if(!u.canFileSlice())return e.SkipTask=!0,void("postObject"===n.options.SimpleUploadMethod?n.postObject(e,t):n.putObject(e,t));var o,r,i=new l,a=e.TaskId,c=e.Bucket,p=e.Region,f=e.Key,h=e.FilePath,g=e.ChunkSize||e.SliceSize||n.options.ChunkSize,y=e.AsyncLimit,x=e.StorageClass,b=e.ServerSideEncryption,_=e.onHashProgress,k=e.tracker;k&&k.setParams({chunkSize:g}),i.on("error",function(o){if(n._isRunningTask(a)){var r={UploadId:e.UploadData.UploadId||"",err:o,error:o};return t(r)}}),i.on("upload_complete",function(n){var o=u.extend({UploadId:e.UploadData.UploadId||""},n);t(null,o)}),i.on("upload_slice_complete",function(e){v.call(n,{Bucket:c,Region:p,Key:f,UploadId:e.UploadId,SliceList:e.SliceList,tracker:k},function(t,c){if(n._isRunningTask(a)){if(s.removeUsing(e.UploadId),t)return r(null,!0),i.emit("error",t);s.removeUploadId(e.UploadId),r({loaded:o,total:o},!0),i.emit("upload_complete",c)}})}),i.on("get_upload_data_finish",function(t){var l=s.getFileId(e.FileStat,e.ChunkSize,c,f);l&&s.saveUploadId(l,t.UploadId,n.options.UploadIdCacheLimit),s.setUsing(t.UploadId),r(null,!0),m.call(n,{TaskId:a,Bucket:c,Region:p,Key:f,FilePath:h,FileSize:o,SliceSize:g,AsyncLimit:y,ServerSideEncryption:b,UploadData:t,onProgress:r,tracker:k},function(e,t){if(n._isRunningTask(a))return e?(r(null,!0),i.emit("error",e)):void i.emit("upload_slice_complete",t)})}),i.on("get_file_size_finish",function(){if(r=u.throttleOnProgress.call(n,o,e.onProgress),e.UploadData.UploadId)i.emit("get_upload_data_finish",e.UploadData);else{var t=u.extend({TaskId:a,Bucket:c,Region:p,Key:f,Headers:e.Headers,StorageClass:x,FilePath:h,FileSize:o,SliceSize:g,onHashProgress:_,tracker:k},e);t.FileSize=o,d.call(n,t,function(t,o){if(n._isRunningTask(a)){if(t)return i.emit("error",t);e.UploadData.UploadId=o.UploadId,e.UploadData.PartList=o.PartList,i.emit("get_upload_data_finish",e.UploadData)}})}}),o=e.ContentLength,delete e.ContentLength,!e.Headers&&(e.Headers={}),u.each(e.Headers,function(t,n){"content-length"===n.toLowerCase()&&delete e.Headers[n]}),function(){for(var t=[1,2,4,8,16,32,64,128,256,512,1024,2048,4096,5120],r=1048576,i=0;i<t.length&&!(o/(r=1024*t[i]*1024)<=n.options.MaxPartNumber);i++);e.ChunkSize=e.SliceSize=g=Math.max(g,r)}(),0===o?(e.Body="",e.ContentLength=0,e.SkipTask=!0,n.putObject(e,function(e,n){if(e)return t(e);t(null,n)})):i.emit("get_file_size_finish")},abortUploadTask:function(e,t){var n=e.Bucket,o=e.Region,r=e.Key,i=e.UploadId,a=e.Level||"task",s=e.AsyncLimit,c=this,u=new l;if(u.on("error",function(e){return t(e)}),u.on("get_abort_array",function(i){y.call(c,{Bucket:n,Region:o,Key:r,Headers:e.Headers,AsyncLimit:s,AbortArray:i},function(e,n){if(e)return t(e);t(null,n)})}),"bucket"===a)f.call(c,{Bucket:n,Region:o,calledBySdk:"abortUploadTask"},function(e,n){if(e)return t(e);u.emit("get_abort_array",n.UploadList||[])});else if("file"===a){if(!r)return t({error:"abort_upload_task_no_key"});f.call(c,{Bucket:n,Region:o,Key:r,calledBySdk:"abortUploadTask"},function(e,n){if(e)return t(e);u.emit("get_abort_array",n.UploadList||[])})}else{if("task"!==a)return t({error:"abort_unknown_level"});if(!i)return t({error:"abort_upload_task_no_id"});if(!r)return t({error:"abort_upload_task_no_key"});u.emit("get_abort_array",[{Key:r,UploadId:i}])}},uploadFile:function(e,t){return x.apply(this,arguments)},uploadFiles:function(e,t){return b.apply(this,arguments)},sliceCopyFile:function(e,t){var n=new l,o=this,r=e.Bucket,i=e.Region,a=e.Key,p=e.CopySource,d=u.getSourceParams.call(this,p);if(d){var f=d.Bucket,m=d.Region,g=decodeURIComponent(d.Key),v=void 0===e.CopySliceSize?o.options.CopySliceSize:e.CopySliceSize;v=Math.max(0,v);var y,x,b=e.CopyChunkSize||this.options.CopyChunkSize,k=this.options.CopyChunkParallelLimit,C=this.options.ChunkRetryTimes+1,S=0,E=0,A={},O={},w={};n.on("copy_slice_complete",function(n){u.each(e.Headers,function(e,t){t.toLowerCase().indexOf("x-cos-meta-")});var l=u.map(n.PartList,function(e){return{PartNumber:e.PartNumber,ETag:e.ETag}});c.retry(C,function(e){o.multipartComplete({Bucket:r,Region:i,Key:a,UploadId:n.UploadId,Parts:l,calledBySdk:"sliceCopyFile"},e)},function(e,o){if(s.removeUsing(n.UploadId),e)return x(null,!0),t(e);s.removeUploadId(n.UploadId),x({loaded:y,total:y},!0),t(null,o)})}),n.on("get_copy_data_finish",function(e){var l=s.getCopyFileId(p,A,b,r,a);l&&s.saveUploadId(l,e.UploadId,o.options.UploadIdCacheLimit),s.setUsing(e.UploadId);var d=u.filter(e.PartList,function(e){return e.Uploaded&&(E+=e.PartNumber>=S&&y%b||b),!e.Uploaded});c.eachLimit(d,k,function(t,n){var s=t.PartNumber,l=t.CopySourceRange,u=t.end-t.start,d=0;c.retry(C,function(t){_.call(o,{Bucket:r,Region:i,Key:a,CopySource:p,UploadId:e.UploadId,PartNumber:s,CopySourceRange:l,onProgress:function(e){E+=e.loaded-d,d=e.loaded,x({loaded:E,total:y})}},t)},function(e,o){if(e)return n(e);x({loaded:E,total:y}),E+=u-d,t.ETag=o.ETag,n(e||null,o)})},function(o){if(o)return s.removeUsing(e.UploadId),x(null,!0),t(o);n.emit("copy_slice_complete",e)})}),n.on("get_chunk_size_finish",function(){var c=function(){o.multipartInit({Bucket:r,Region:i,Key:a,Headers:w},function(o,r){if(o)return t(o);e.UploadId=r.UploadId,n.emit("get_copy_data_finish",{UploadId:e.UploadId,PartList:e.PartList})})},l=s.getCopyFileId(p,A,b,r,a),d=s.getUploadIdList(l);if(!l||!d)return c();!function t(l){if(l>=d.length)return c();var p=d[l];if(s.using[p])return t(l+1);h.call(o,{Bucket:r,Region:i,Key:a,UploadId:p},function(o,r){if(o)s.removeUploadId(p),t(l+1);else{if(s.using[p])return t(l+1);var i={},a=0;u.each(r.PartList,function(e){var t=parseInt(e.Size),n=a+t-1;i[e.PartNumber+"|"+a+"|"+n]=e.ETag,a+=t}),u.each(e.PartList,function(e){var t=i[e.PartNumber+"|"+e.start+"|"+e.end];t&&(e.ETag=t,e.Uploaded=!0)}),n.emit("get_copy_data_finish",{UploadId:p,PartList:e.PartList})}})}(0)}),n.on("get_file_size_finish",function(){var r;if(function(){for(var t=[1,2,4,8,16,32,64,128,256,512,1024,2048,4096,5120],n=1048576,r=0;r<t.length&&!(y/(n=1024*t[r]*1024)<=o.options.MaxPartNumber);r++);e.ChunkSize=b=Math.max(b,n),S=Math.ceil(y/b);for(var i=[],a=1;a<=S;a++){var s=(a-1)*b,c=a*b<y?a*b-1:y-1,l={PartNumber:a,start:s,end:c,CopySourceRange:"bytes="+s+"-"+c};i.push(l)}e.PartList=i}(),(r="Replaced"===e.Headers["x-cos-metadata-directive"]?e.Headers:O)["x-cos-storage-class"]=e.Headers["x-cos-storage-class"]||O["x-cos-storage-class"],r=u.clearKey(r),"ARCHIVE"===O["x-cos-storage-class"]||"DEEP_ARCHIVE"===O["x-cos-storage-class"]){var i=O["x-cos-restore"];if(!i||'ongoing-request="true"'===i)return void t({error:"Unrestored archive object is not allowed to be copied"})}delete r["x-cos-copy-source"],delete r["x-cos-metadata-directive"],delete r["x-cos-copy-source-If-Modified-Since"],delete r["x-cos-copy-source-If-Unmodified-Since"],delete r["x-cos-copy-source-If-Match"],delete r["x-cos-copy-source-If-None-Match"],n.emit("get_chunk_size_finish")}),o.headObject({Bucket:f,Region:m,Key:g},function(r,i){if(r)r.statusCode&&404===r.statusCode?t({ErrorStatus:g+" Not Exist"}):t(r);else if(void 0!==(y=e.FileSize=i.headers["content-length"])&&y)if(x=u.throttleOnProgress.call(o,y,e.onProgress),y<=v)e.Headers["x-cos-metadata-directive"]||(e.Headers["x-cos-metadata-directive"]="Copy"),o.putObjectCopy(e,function(e,n){if(e)return x(null,!0),t(e);x({loaded:y,total:y},!0),t(e,n)});else{var a=i.headers;A=a,O={"Cache-Control":a["cache-control"],"Content-Disposition":a["content-disposition"],"Content-Encoding":a["content-encoding"],"Content-Type":a["content-type"],Expires:a.expires,"x-cos-storage-class":a["x-cos-storage-class"]},u.each(a,function(e,t){var n="x-cos-meta-";0===t.indexOf(n)&&t.length>11&&(O[t]=e)}),n.emit("get_file_size_finish")}else t({error:'get Content-Length error, please add "Content-Length" to CORS ExposeHeader setting.'})})}else t({error:"CopySource format error"})}};e.exports.init=function(e,t){t.transferToTaskMethod(k,"sliceUploadFile"),u.each(k,function(t,n){e.prototype[n]=u.apiWrapper(n,t)})}},function(e,t){e.exports={eachLimit:function(e,t,n,o){if(o=o||function(){},!e.length||t<=0)return o();var r=0,i=0,a=0;!function s(){if(r>=e.length)return o();for(;a<t&&i<e.length;)a+=1,n(e[(i+=1)-1],function(t){t?(o(t),o=function(){}):(a-=1,(r+=1)>=e.length?o():s())})}()},retry:function(e,t,n){e<1?n():function o(r){t(function(t,i){t&&r<e?o(r+1):n(t,i)})}(1)}}}])},module.exports=t2()},function(e,t){var n={getType:function(e){return null===e?"null":void 0===e?"undefined":Object.prototype.toString.call(e).slice(8,-1).toLowerCase()},isFunction:function(e){return!e||"function"===this.getType(e)},getFileMessage:function(e,t){var n={};return n.tempFilePath=e.tempFilePath,n.type=e.tempFilePath.substring(e.tempFilePath.lastIndexOf(".")+1),n.name="string"==typeof t?t:"来自小程序",n.size=e.size,n},noop:function(){}};e.exports=n},function(e,t){t.UploaderEvent={video_progress:"video_progress",media_progress:"media_progress"}},function(e,t,n){var o,r="object"==typeof Reflect?Reflect:null,i=r&&"function"==typeof r.apply?r.apply:function(e,t,n){return Function.prototype.apply.call(e,t,n)};o=r&&"function"==typeof r.ownKeys?r.ownKeys:Object.getOwnPropertySymbols?function(e){return Object.getOwnPropertyNames(e).concat(Object.getOwnPropertySymbols(e))}:function(e){return Object.getOwnPropertyNames(e)};var a=Number.isNaN||function(e){return e!=e};function s(){s.init.call(this)}e.exports=s,s.EventEmitter=s,s.prototype._events=void 0,s.prototype._eventsCount=0,s.prototype._maxListeners=void 0;var c=10;function l(e){return void 0===e._maxListeners?s.defaultMaxListeners:e._maxListeners}function u(e,t,n,o){var r,i,a,s;if("function"!=typeof n)throw new TypeError('The "listener" argument must be of type Function. Received type '+typeof n);if(void 0===(i=e._events)?(i=e._events=Object.create(null),e._eventsCount=0):(void 0!==i.newListener&&(e.emit("newListener",t,n.listener?n.listener:n),i=e._events),a=i[t]),void 0===a)a=i[t]=n,++e._eventsCount;else if("function"==typeof a?a=i[t]=o?[n,a]:[a,n]:o?a.unshift(n):a.push(n),(r=l(e))>0&&a.length>r&&!a.warned){a.warned=!0;var c=new Error("Possible EventEmitter memory leak detected. "+a.length+" "+String(t)+" listeners added. Use emitter.setMaxListeners() to increase limit");c.name="MaxListenersExceededWarning",c.emitter=e,c.type=t,c.count=a.length,s=c,console&&console.warn&&console.warn(s)}return e}function p(){for(var e=[],t=0;t<arguments.length;t++)e.push(arguments[t]);this.fired||(this.target.removeListener(this.type,this.wrapFn),this.fired=!0,i(this.listener,this.target,e))}function d(e,t,n){var o={fired:!1,wrapFn:void 0,target:e,type:t,listener:n},r=p.bind(o);return r.listener=n,o.wrapFn=r,r}function f(e,t,n){var o=e._events;if(void 0===o)return[];var r=o[t];return void 0===r?[]:"function"==typeof r?n?[r.listener||r]:[r]:n?function(e){for(var t=new Array(e.length),n=0;n<t.length;++n)t[n]=e[n].listener||e[n];return t}(r):m(r,r.length)}function h(e){var t=this._events;if(void 0!==t){var n=t[e];if("function"==typeof n)return 1;if(void 0!==n)return n.length}return 0}function m(e,t){for(var n=new Array(t),o=0;o<t;++o)n[o]=e[o];return n}Object.defineProperty(s,"defaultMaxListeners",{enumerable:!0,get:function(){return c},set:function(e){if("number"!=typeof e||e<0||a(e))throw new RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received '+e+".");c=e}}),s.init=function(){void 0!==this._events&&this._events!==Object.getPrototypeOf(this)._events||(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0},s.prototype.setMaxListeners=function(e){if("number"!=typeof e||e<0||a(e))throw new RangeError('The value of "n" is out of range. It must be a non-negative number. Received '+e+".");return this._maxListeners=e,this},s.prototype.getMaxListeners=function(){return l(this)},s.prototype.emit=function(e){for(var t=[],n=1;n<arguments.length;n++)t.push(arguments[n]);var o="error"===e,r=this._events;if(void 0!==r)o=o&&void 0===r.error;else if(!o)return!1;if(o){var a;if(t.length>0&&(a=t[0]),a instanceof Error)throw a;var s=new Error("Unhandled error."+(a?" ("+a.message+")":""));throw s.context=a,s}var c=r[e];if(void 0===c)return!1;if("function"==typeof c)i(c,this,t);else{var l=c.length,u=m(c,l);for(n=0;n<l;++n)i(u[n],this,t)}return!0},s.prototype.addListener=function(e,t){return u(this,e,t,!1)},s.prototype.on=s.prototype.addListener,s.prototype.prependListener=function(e,t){return u(this,e,t,!0)},s.prototype.once=function(e,t){if("function"!=typeof t)throw new TypeError('The "listener" argument must be of type Function. Received type '+typeof t);return this.on(e,d(this,e,t)),this},s.prototype.prependOnceListener=function(e,t){if("function"!=typeof t)throw new TypeError('The "listener" argument must be of type Function. Received type '+typeof t);return this.prependListener(e,d(this,e,t)),this},s.prototype.removeListener=function(e,t){var n,o,r,i,a;if("function"!=typeof t)throw new TypeError('The "listener" argument must be of type Function. Received type '+typeof t);if(void 0===(o=this._events))return this;if(void 0===(n=o[e]))return this;if(n===t||n.listener===t)0===--this._eventsCount?this._events=Object.create(null):(delete o[e],o.removeListener&&this.emit("removeListener",e,n.listener||t));else if("function"!=typeof n){for(r=-1,i=n.length-1;i>=0;i--)if(n[i]===t||n[i].listener===t){a=n[i].listener,r=i;break}if(r<0)return this;0===r?n.shift():function(e,t){for(;t+1<e.length;t++)e[t]=e[t+1];e.pop()}(n,r),1===n.length&&(o[e]=n[0]),void 0!==o.removeListener&&this.emit("removeListener",e,a||t)}return this},s.prototype.off=s.prototype.removeListener,s.prototype.removeAllListeners=function(e){var t,n,o;if(void 0===(n=this._events))return this;if(void 0===n.removeListener)return 0===arguments.length?(this._events=Object.create(null),this._eventsCount=0):void 0!==n[e]&&(0===--this._eventsCount?this._events=Object.create(null):delete n[e]),this;if(0===arguments.length){var r,i=Object.keys(n);for(o=0;o<i.length;++o)"removeListener"!==(r=i[o])&&this.removeAllListeners(r);return this.removeAllListeners("removeListener"),this._events=Object.create(null),this._eventsCount=0,this}if("function"==typeof(t=n[e]))this.removeListener(e,t);else if(void 0!==t)for(o=t.length-1;o>=0;o--)this.removeListener(e,t[o]);return this},s.prototype.listeners=function(e){return f(this,e,!0)},s.prototype.rawListeners=function(e){return f(this,e,!1)},s.listenerCount=function(e,t){return"function"==typeof e.listenerCount?e.listenerCount(t):h.call(e,t)},s.prototype.listenerCount=h,s.prototype.eventNames=function(){return this._eventsCount>0?o(this._events):[]}},function(e,t,n){function o(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,o)}return n}function r(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?o(Object(n),!0).forEach(function(t){a(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):o(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function i(e,t,n){return t&&function(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}(e.prototype,t),e}function a(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}n(0);var s=n(6),c={report_prepare:"report_prepare",report_apply:"report_apply",report_cos_upload:"report_cos_upload",report_commit:"report_commit",report_done:"report_done"},l=1e4,u=10001,p=20001,d=10002,f=40001;t.reportEvent=c,t.VodReporter=function(){function e(t,n){var o,r,i,c;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),a(this,"uploader",void 0),a(this,"options",void 0),a(this,"baseReportData",{version:s.version,platform:4e3,device:(o=wx$1.getSystemInfoSync(),r=o.brand,i=o.model,c=o.version,"".concat(r,"-").concat(i,"-wx").concat(c))}),a(this,"reportUrl","https://vodreport.qcloud.com/ugcupload_new"),this.uploader=t,this.options=n,this.init()}return i(e,[{key:"init",value:function(){this.uploader.on(c.report_prepare,this.onPrepare.bind(this)),this.uploader.on(c.report_apply,this.onApply.bind(this)),this.uploader.on(c.report_cos_upload,this.onCosUpload.bind(this)),this.uploader.on(c.report_commit,this.onCommit.bind(this)),this.uploader.on(c.report_done,this.onDone.bind(this))}},{key:"onPrepare",value:function(e){var t=this.uploader;try{var n={appId:t.appId,reqType:l,errCode:0,vodErrCode:0,errMsg:"",reqTimeCost:Number(new Date)-Number(e.requestStartTime),reqTime:Number(e.requestStartTime)};e.err&&(n.errCode=1,n.vodErrCode=e.err.code,n.errMsg=e.err.message),e.data&&(n.cosRegion=e.data.region),this.report(n)}catch(o){console.log("onPrepare",o)}}},{key:"onApply",value:function(e){try{var t=this.uploader;if(!t.videoFileMessage)return;var n=t.videoFileMessage;Object.assign(this.baseReportData,{appId:t.appId,fileSize:n.size,fileName:n.name,fileType:n.type,vodSessionKey:t.vodSessionKey,reqKey:t.reqKey,reportId:t.reportId});var o={reqType:u,errCode:0,vodErrCode:0,errMsg:"",reqTimeCost:Number(new Date)-Number(e.requestStartTime),reqTime:Number(e.requestStartTime)};e.err&&(o.errCode=1,o.vodErrCode=e.err.code,o.errMsg=e.err.message),e.data&&(this.baseReportData.cosRegion=e.data.storageRegion),this.report(o)}catch(r){console.error("onApply",r)}}},{key:"onCosUpload",value:function(e){try{var t={reqType:p,errCode:0,cosErrCode:"",errMsg:"",reqTimeCost:Number(new Date)-Number(e.requestStartTime),reqTime:Number(e.requestStartTime)};e.err&&(t.errCode=1,t.cosErrCode=e.err.error?e.err.error.Code:e.err,e.err&&"error"===e.err.error&&(t.cosErrCode="cors error"),t.errMsg=JSON.stringify(e.err)),this.report(t)}catch(n){console.error("onCosUpload",n)}}},{key:"onCommit",value:function(e){try{var t={reqType:d,errCode:0,vodErrCode:0,errMsg:"",reqTimeCost:Number(new Date)-Number(e.requestStartTime),reqTime:Number(e.requestStartTime)};e.err&&(t.errCode=1,t.vodErrCode=e.err.code,t.errMsg=e.err.message),e.data&&(this.baseReportData.fileId=e.data.fileId),this.report(t)}catch(n){console.error("onCommit",n)}}},{key:"onDone",value:function(e){try{var t={reqType:f,errCode:e.err&&e.err.code,reqTimeCost:Number(new Date)-Number(e.requestStartTime),reqTime:Number(e.requestStartTime)};this.report(t)}catch(n){console.error("onDone",n)}}},{key:"report",value:function(e){e=r(r({},this.baseReportData),e),this.send(e)}},{key:"send",value:function(e){console.log("上报: ",e),wx$1.request({method:"POST",url:this.reportUrl,data:e,dataType:"json",fail:function(e){console.log(e)}})}}]),e}()},function(e){e.exports=JSON.parse('{"name":"vod-wx-sdk-v2","version":"1.1.1","description":"Tencent cloud vod sdk for wechat mini program","main":"dist/vod-wx-sdk-v2.js","miniprogram":"dist","scripts":{"build":"webpack --config webpack.config.js","dev":"webpack --config webpack.dev.js --watch"},"repository":{"type":"git","url":"git+https://github.com/tencentyun/vod-wx-sdk-v2.git"},"keywords":["vod","tencentcloud","qcloud","wechat"],"author":"alsotang <<EMAIL>>","contributors":["_windmill <<EMAIL>>"],"license":"MIT","bugs":{"url":"https://github.com/tencentyun/vod-wx-sdk-v2/issues"},"homepage":"https://github.com/tencentyun/vod-wx-sdk-v2#readme","devDependencies":{"@babel/core":"^7.12.10","@babel/plugin-proposal-class-properties":"^7.12.1","@babel/preset-env":"^7.12.11","babel-loader":"^8.2.2","eslint":"^5.16.0","eslint-config-airbnb-base":"^13.2.0","eslint-config-prettier":"^5.1.0","eslint-plugin-import":"^2.22.1","eslint-plugin-prettier":"^3.3.1","webpack":"^4.46.0","webpack-cli":"^3.3.12"},"dependencies":{"cos-wx-sdk-v5":"^1.4.6"}}')}]))})(vodWxSdkV2);const VodUploader=getDefaultExportFromCjs(vodWxSdkV2);exports.VodUploader=VodUploader,exports._export_sfc=_export_sfc,exports.computed=computed,exports.createSSRApp=createSSRApp,exports.defineComponent=defineComponent,exports.e=e,exports.f=f,exports.index=index,exports.n=n,exports.o=o,exports.onHide=onHide,exports.onLaunch=onLaunch,exports.onMounted=onMounted,exports.onShow=onShow,exports.onUnmounted=onUnmounted,exports.ref=ref,exports.t=t,exports.wx$1=wx$1;
