"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "index",
  setup(__props) {
    const recentTasks = common_vendor.ref([]);
    common_vendor.onMounted(() => {
      loadRecentTasks();
    });
    const loadRecentTasks = async () => {
      try {
        const res = await common_vendor.wx$1.cloud.callFunction({
          name: "get-recent-tasks",
          data: { limit: 3 }
        });
        recentTasks.value = res.result || [];
      } catch (error) {
        console.error("加载最近任务失败:", error);
      }
    };
    const startUpload = () => {
      common_vendor.index.navigateTo({
        url: "/pages/upload/upload"
      });
    };
    const viewAllHistory = () => {
      common_vendor.index.switchTab({
        url: "/pages/history/history"
      });
    };
    const viewTaskDetail = (task) => {
      if (task.status === "processing") {
        common_vendor.index.navigateTo({
          url: `/pages/process/process?fileId=${task.originalVideoFileId}`
        });
      } else if (task.status === "completed") {
        common_vendor.index.navigateTo({
          url: `/pages/result/result?taskId=${task._id}`
        });
      }
    };
    const getStatusText = (status) => {
      switch (status) {
        case "processing":
          return "处理中";
        case "completed":
          return "已完成";
        case "failed":
          return "失败";
        default:
          return "未知";
      }
    };
    const formatTime = (timestamp) => {
      if (!timestamp)
        return "";
      const date = new Date(timestamp);
      const now = /* @__PURE__ */ new Date();
      const diff = now.getTime() - date.getTime();
      if (diff < 6e4)
        return "刚刚";
      if (diff < 36e5)
        return `${Math.floor(diff / 6e4)}分钟前`;
      if (diff < 864e5)
        return `${Math.floor(diff / 36e5)}小时前`;
      return `${date.getMonth() + 1}-${date.getDate()}`;
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_assets._imports_0,
        b: common_vendor.o(startUpload),
        c: recentTasks.value.length > 0
      }, recentTasks.value.length > 0 ? {
        d: common_vendor.o(viewAllHistory),
        e: common_vendor.f(recentTasks.value, (task, k0, i0) => {
          return {
            a: common_vendor.t(task.fileName || "未命名视频"),
            b: common_vendor.t(formatTime(task.createTime)),
            c: common_vendor.t(getStatusText(task.status)),
            d: common_vendor.n(task.status),
            e: task._id,
            f: common_vendor.o(($event) => viewTaskDetail(task), task._id)
          };
        })
      } : {});
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-83a5a03c"]]);
wx.createPage(MiniProgramPage);
