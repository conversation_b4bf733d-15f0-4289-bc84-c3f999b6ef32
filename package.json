{"name": "video-translate-app", "version": "1.0.0", "description": "视语翻译 - AI智能视频字幕生成小程序", "scripts": {"dev": "npm run dev:mp-weixin", "dev:custom": "uni -p", "dev:h5": "uni", "dev:h5:ssr": "uni --ssr", "dev:mp-alipay": "uni -p mp-alipay", "dev:mp-baidu": "uni -p mp-baidu", "dev:mp-jd": "uni -p mp-jd", "dev:mp-kuaishou": "uni -p mp-kua<PERSON>ou", "dev:mp-lark": "uni -p mp-lark", "dev:mp-qq": "uni -p mp-qq", "dev:mp-toutiao": "uni -p mp-to<PERSON><PERSON>", "dev:mp-weixin": "uni -p mp-weixin", "dev:mp-xhs": "uni -p mp-xhs", "dev:quickapp-webview": "uni -p quickapp-webview", "dev:quickapp-webview-huawei": "uni -p quickapp-webview-huawei", "dev:quickapp-webview-union": "uni -p quickapp-webview-union", "build:custom": "uni build -p", "build:h5": "uni build", "build:h5:ssr": "uni build --ssr", "build:mp-alipay": "uni build -p mp-alipay", "build:mp-baidu": "uni build -p mp-baidu", "build:mp-jd": "uni build -p mp-jd", "build:mp-kuaishou": "uni build -p mp-kuaishou", "build:mp-lark": "uni build -p mp-lark", "build:mp-qq": "uni build -p mp-qq", "build:mp-toutiao": "uni build -p mp-to<PERSON>ao", "build:mp-weixin": "uni build -p mp-weixin", "build:mp-xhs": "uni build -p mp-xhs", "build:quickapp-webview": "uni build -p quickapp-webview", "build:quickapp-webview-huawei": "uni build -p quickapp-webview-huawei", "build:quickapp-webview-union": "uni build -p quickapp-webview-union", "type-check": "vue-tsc --noEmit"}, "dependencies": {"@dcloudio/uni-app": "3.0.0-4030620241128001", "@dcloudio/uni-app-harmony": "3.0.0-4030620241128001", "@dcloudio/uni-app-plus": "3.0.0-4030620241128001", "@dcloudio/uni-components": "3.0.0-4030620241128001", "@dcloudio/uni-h5": "3.0.0-4030620241128001", "@dcloudio/uni-mp-alipay": "3.0.0-4030620241128001", "@dcloudio/uni-mp-baidu": "3.0.0-4030620241128001", "@dcloudio/uni-mp-jd": "3.0.0-4030620241128001", "@dcloudio/uni-mp-kuaishou": "3.0.0-4030620241128001", "@dcloudio/uni-mp-lark": "3.0.0-4030620241128001", "@dcloudio/uni-mp-qq": "3.0.0-4030620241128001", "@dcloudio/uni-mp-toutiao": "3.0.0-4030620241128001", "@dcloudio/uni-mp-weixin": "3.0.0-4030620241128001", "@dcloudio/uni-mp-xhs": "3.0.0-4030620241128001", "@dcloudio/uni-quickapp-webview": "3.0.0-4030620241128001", "vod-wx-sdk-v2": "^1.1.2", "vue": "^3.4.21", "vue-i18n": "^9.1.9"}, "devDependencies": {"@dcloudio/types": "^3.4.8", "@dcloudio/uni-automator": "3.0.0-4030620241128001", "@dcloudio/uni-cli-shared": "3.0.0-4030620241128001", "@dcloudio/uni-stacktracey": "3.0.0-4030620241128001", "@dcloudio/vite-plugin-uni": "3.0.0-4030620241128001", "@vue/runtime-core": "^3.4.21", "@vue/tsconfig": "^0.1.3", "typescript": "^4.9.4", "vite": "5.2.8", "vue-tsc": "^1.0.24"}}