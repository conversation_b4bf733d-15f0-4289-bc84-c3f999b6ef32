/*! *****************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */
var e=function(){return e=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++)for(var o in n=arguments[t])Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o]);return e},e.apply(this,arguments)};function n(){for(var e=0,n=0,t=arguments.length;n<t;n++)e+=arguments[n].length;var r=Array(e),o=0;for(n=0;n<t;n++)for(var i=arguments[n],u=0,a=i.length;u<a;u++,o++)r[o]=i[u];return r}var t="undefined"!=typeof crypto&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto)||"undefined"!=typeof msCrypto&&"function"==typeof msCrypto.getRandomValues&&msCrypto.getRandomValues.bind(msCrypto),r=new Uint8Array(16);function o(){if(!t)throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return t(r)}for(var i=[],u=0;u<256;++u)i[u]=(u+256).toString(16).substr(1);function a(e,n,t){var r=n&&t||0;"string"==typeof e&&(n="binary"===e?new Array(16):null,e=null);var u=(e=e||{}).random||(e.rng||o)();if(u[6]=15&u[6]|64,u[8]=63&u[8]|128,n)for(var a=0;a<16;++a)n[r+a]=u[a];return n||function(e,n){var t=n||0,r=i;return[r[e[t++]],r[e[t++]],r[e[t++]],r[e[t++]],"-",r[e[t++]],r[e[t++]],"-",r[e[t++]],r[e[t++]],"-",r[e[t++]],r[e[t++]],"-",r[e[t++]],r[e[t++]],r[e[t++]],r[e[t++]],r[e[t++]],r[e[t++]]].join("")}(u)}var c=Object.prototype.hasOwnProperty,s=function(e){return null==e},l=Array.isArray,f=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g;function d(e,n){if(l(e))return e;if(n&&(t=n,r=e,c.call(t,r)))return[e];var t,r,o=[];return e.replace(f,(function(e,n,t,r){return o.push(t?r.replace(/\\(\\)?/g,"$1"):n||e),r})),o}function p(e,n){var t,r=d(n,e);for(t=r.shift();!s(t);){if(null==(e=e[t]))return;t=r.shift()}return e}function g(e){return e._uid||e.uid}function v(e){return!!(null==e?void 0:e.getElementById)}function m(e){return v(e)?e.vm:e}function h(e){return v(e)?e.vm.$basePage:e.$page}function _(e){return v(e.$page)?e.$basePage:e.$page}var y=new Map;function T(e){var n,t;if(!function(e){if(e){var n=e.tagName;return 0===n.indexOf("UNI-")||"BODY"===n||0===n.indexOf("V-UNI-")||e.__isUniElement}return!1}(e))throw Error("no such element");var r,o,i,u={elementId:(r=e,o=r._id,o||(o=a(),r._id=o,y.set(o,{id:o,element:r})),o),tagName:e.tagName.toLocaleLowerCase().replace("uni-","")};e.__vue__?(i=e.__vue__)&&(i.$parent&&i.$parent.$el===e&&(i=i.$parent),i&&!(null===(n=i.$options)||void 0===n?void 0:n.isReserved)&&(u.nodeId=g(i))):(i=e.__vueParentComponent)&&(i.subTree.el===e&&(i=i.parent),(null===(t=i.type)||void 0===t?void 0:t.__reserved)||(u.nodeId=g(i)));return"video"===u.tagName&&(u.videoId=u.nodeId),u}function w(e){return e.__vue__?{isVue3:!1,vm:e.__vue__}:{isVue3:!0,vm:e.__vueParentComponent}}function E(e){var n=w(e),t=n.isVue3,r=n.vm;return t?r.exposed.$getMain():r.$refs.main}var S={input:{input:function(e,n){var t=w(e),r=t.isVue3,o=t.vm;r?o.exposed&&o.exposed.$triggerInput({value:n}):(o.valueSync=n,o.$triggerInput({},{value:n}))}},textarea:{input:function(e,n){var t=w(e),r=t.isVue3,o=t.vm;r?o.exposed&&o.exposed.$triggerInput({value:n}):(o.valueSync=n,o.$triggerInput({},{value:n}))}},"scroll-view":{scrollTo:function(e,n,t){var r=E(e);r.scrollLeft=n,r.scrollTop=t},scrollTop:function(e){return E(e).scrollTop},scrollLeft:function(e){return E(e).scrollLeft},scrollWidth:function(e){return E(e).scrollWidth},scrollHeight:function(e){return E(e).scrollHeight}},swiper:{swipeTo:function(e,n){e.__vue__.current=n}},"movable-view":{moveTo:function(e,n,t){e.__vue__._animationTo(n,t)}},switch:{tap:function(e){e.click()}},slider:{slideTo:function(e,n){var t=e.__vue__,r=t.$refs["uni-slider"],o=r.offsetWidth,i=r.getBoundingClientRect().left;t.value=n,t._onClick({x:(n-t.min)*o/(t.max-t.min)+i})}}};function b(e){var n,t=e.map((function(e){return function(e){if(document.createTouch)return document.createTouch(window,e.target,e.identifier,e.pageX,e.pageY,e.screenX,e.screenY,e.clientX,e.clientY);return new Touch(e)}(e)}));return document.createTouchList?(n=document).createTouchList.apply(n,t):t}var P={getWindow:function(e){return window},getDocument:function(e){return document},getEl:function(e){var n=y.get(e);if(!n)throw Error("element destroyed");return n.element},getOffset:function(e){var n=e.getBoundingClientRect();return Promise.resolve({left:n.left+window.pageXOffset,top:n.top+window.pageYOffset})},querySelector:function(e,n){return"page"===n&&(n="body"),Promise.resolve(T(e.querySelector(n)))},querySelectorAll:function(e,n){var t=[],r=document.querySelectorAll(n);return[].forEach.call(r,(function(e){try{t.push(T(e))}catch(e){}})),Promise.resolve({elements:t})},queryProperties:function(e,n){return Promise.resolve({properties:n.map((function(n){var t=p(e,n.replace(/-([a-z])/g,(function(e){return e[1].toUpperCase()})));return"document.documentElement.scrollTop"===n&&0===t&&(t=p(e,"document.body.scrollTop")),t}))})},queryAttributes:function(e,n){return Promise.resolve({attributes:n.map((function(n){return String(e.getAttribute(n))}))})},queryStyles:function(e,n){var t=getComputedStyle(e);return Promise.resolve({styles:n.map((function(e){return t[e]}))})},queryHTML:function(e,n){return Promise.resolve({html:(t="outer"===n?e.outerHTML:e.innerHTML,t.replace(/\n/g,"").replace(/(<uni-text[^>]*>)(<span[^>]*>[^<]*<\/span>)(.*?<\/uni-text>)/g,"$1$3").replace(/<\/?[^>]*>/g,(function(e){return-1<e.indexOf("<body")?"<page>":"</body>"===e?"</page>":0!==e.indexOf("<uni-")&&0!==e.indexOf("</uni-")?"":e.replace(/uni-/g,"").replace(/ role=""/g,"").replace(/ aria-label=""/g,"")})))});var t},dispatchTapEvent:function(e){return e.click(),Promise.resolve()},dispatchLongpressEvent:function(e){return new Promise((function(n){e.dispatchEvent(new TouchEvent("touchstart",{cancelable:!0,bubbles:!0,touches:b([{identifier:1,target:e,pageX:0,pageY:0,clientX:0,clientY:0,screenX:0,screenY:0}]),targetTouches:b([]),changedTouches:b([{identifier:1,target:e,pageX:0,pageY:0,clientX:0,clientY:0,screenX:0,screenY:0}])})),setTimeout((function(){n()}),400)}))},dispatchTouchEvent:function(e,n,t){t||(t={}),t.touches||(t.touches=[]),t.changedTouches||(t.changedTouches=[]),t.touches.length||t.touches.push({identifier:1,target:e}),t.touches.forEach((function(n){n.target=e})),t.changedTouches.forEach((function(n){n.target=e}));var r=b(t.touches),o=b(t.changedTouches),i=b([]);return e.dispatchEvent(new TouchEvent(n,{cancelable:!0,bubbles:!0,touches:r,targetTouches:i,changedTouches:o})),Promise.resolve()},callFunction:function(e,t,r){var o=p(S,t);return o?Promise.resolve({result:o.apply(null,n([e],r))}):Promise.reject(Error(t+" not exists"))},triggerEvent:function(e,n,t){var r=e.__vue__;if(null==r?void 0:r.$trigger)r.$trigger(n,{},t);else switch(n){case"focus":e.focus();break;case"blur":e.getElementsByTagName("input")[0].blur()}return Promise.resolve()}};var M,O=Object.assign({},function(e){return{"Page.getElement":function(n){return e.querySelector(e.getDocument(n.pageId),n.selector)},"Page.getElements":function(n){return e.querySelectorAll(e.getDocument(n.pageId),n.selector)},"Page.getWindowProperties":function(n){return e.queryProperties(e.getWindow(n.pageId),n.names)}}}(P),function(e){var n=function(n){return e.getEl(n.elementId,n.pageId)};return{"Element.getElement":function(t){return e.querySelector(n(t),t.selector)},"Element.getElements":function(t){return e.querySelectorAll(n(t),t.selector)},"Element.getDOMProperties":function(t){return e.queryProperties(n(t),t.names)},"Element.getProperties":function(t){var r=n(t),o=r.__vue__||r.attr||{};return r.__vueParentComponent&&(o=Object.assign({},o,r.__vueParentComponent.attrs,r.__vueParentComponent.props)),e.queryProperties(o,t.names)},"Element.getOffset":function(t){return e.getOffset(n(t))},"Element.getAttributes":function(t){return e.queryAttributes(n(t),t.names)},"Element.getStyles":function(t){return e.queryStyles(n(t),t.names)},"Element.getHTML":function(t){return e.queryHTML(n(t),t.type)},"Element.tap":function(t){return e.dispatchTapEvent(n(t))},"Element.longpress":function(t){return e.dispatchLongpressEvent(n(t))},"Element.touchstart":function(t){return e.dispatchTouchEvent(n(t),"touchstart",t)},"Element.touchmove":function(t){return e.dispatchTouchEvent(n(t),"touchmove",t)},"Element.touchend":function(t){return e.dispatchTouchEvent(n(t),"touchend",t)},"Element.callFunction":function(t){return e.callFunction(n(t),t.functionName,t.args)},"Element.triggerEvent":function(t){return e.triggerEvent(n(t),t.type,t.detail)}}}(P));function I(e){return UniViewJSBridge.publishHandler("onAutoMessageReceive",e)}function x(e){var n;return e.__wxWebviewId__?e.__wxWebviewId__:e.privateProperties?e.privateProperties.slaveId:_(e)?null===(n=_(e))||void 0===n?void 0:n.id:void 0}function C(e){return e.route||e.uri}function $(e){return e.options||e.$page&&e.$page.options||{}}function k(e){return{id:x(e),path:C(e),query:$(e)}}function A(e){var n=m(function(e){return getCurrentPages().find((function(n){return x(m(n))===e}))}(e));return n&&n.$vm}function N(e,n){return e._uid===n||e.uid===n}function W(e,n,t){var r,o,i,u,a,c,s,l,f,d,p,g,v;if(void 0===t&&(t=!1),t)if(e.component&&N(e.component,n))v=e.component;else{var m=[];e.children instanceof Array?m=e.children:(null===(o=null===(r=e.component)||void 0===r?void 0:r.subTree)||void 0===o?void 0:o.children)&&(null===(u=null===(i=e.component)||void 0===i?void 0:i.subTree)||void 0===u?void 0:u.children)instanceof Array?m=e.component.subTree.children:(null===(l=null===(s=null===(c=null===(a=e.component)||void 0===a?void 0:a.subTree)||void 0===c?void 0:c.component)||void 0===s?void 0:s.subTree)||void 0===l?void 0:l.children)&&(null===(g=null===(p=null===(d=null===(f=e.component)||void 0===f?void 0:f.subTree)||void 0===d?void 0:d.component)||void 0===p?void 0:p.subTree)||void 0===g?void 0:g.children)instanceof Array&&(m=e.component.subTree.component.subTree.children),m.find((function(e){return v=W(e,n,!0)}))}else e&&(N(e,n)?v=e:e.$children.find((function(e){return v=W(e,n)})));return v}function V(e,n){var t=A(e);if(t)return D(t)?W(t.$.subTree,n,!0):W(t,n)}function B(n,t){var r,o=n.$data||n.data;return n.exposed?o=e(e({},o),n.exposed):n.$&&n.$.exposed&&(o=e(e({},o),n.$.exposed)),n&&(r=t?p(o,t):Object.assign({},o)),Promise.resolve({data:r})}function X(e,n){if(e){var t=D(e);Object.keys(n).forEach((function(r){t?(e.$data||e.data)[r]=n[r]:e[r]=n[r]}))}return Promise.resolve()}function q(e,n,t){return D(e)&&(e=e.$vm||e.ctx),new Promise((function(r,o){var i,u;if(!e)return o(M.VM_NOT_EXISTS);if(!e[n]&&!(null===(u=e.$.exposed)||void 0===u?void 0:u[n]))return o(M.METHOD_NOT_EXISTS);var a,c=e[n]?e[n].apply(e,t):(i=e.$.exposed)[n].apply(i,t);!(a=c)||"object"!=typeof a&&"function"!=typeof a||"function"!=typeof a.then?r({result:c}):c.then((function(e){r({result:e})}))}))}function D(e){return!e.$children}function U(){return"undefined"!=typeof window&&(window.__uniapp_x_||window.__uniapp_x_postMessage)}UniViewJSBridge.subscribe("sendAutoMessage",(function(e){var n=e.id,t=e.method,r=e.params,o={id:n};if("ping"==t)return o.result="pong",void I(o);var i=O[t];if(!i)return o.error={message:t+" unimplemented"},I(o);try{i(r).then((function(e){e&&(o.result=e)})).catch((function(e){o.error={message:e.message}})).finally((function(){I(o)}))}catch(e){o.error={message:e.message},I(o)}})),function(e){e.VM_NOT_EXISTS="VM_NOT_EXISTS",e.METHOD_NOT_EXISTS="METHOD_NOT_EXISTS"}(M||(M={}));var L=1,R={};function H(e,n){var t,r=0;n&&(r=L++,R[r]=n);var o={data:{id:r,type:"automator",data:e}};console.log("postMessageToUniXWebView",o),(null===(t=null===window||void 0===window?void 0:window.__uniapp_x_)||void 0===t?void 0:t.postMessage)?window.__uniapp_x_.postMessage(JSON.stringify(o)):(null===window||void 0===window?void 0:window.__uniapp_x_postMessage)&&window.__uniapp_x_postMessage({data:o})}var j=new Map,Y=function(n){return new Promise((function(t,r){var o=j.values().next().value;if(o){var i=n.method;if("onOpen"===i)return J(o,t);if(i.startsWith("on"))return o.instance[i]((function(e){t(e)}));"sendMessage"===i&&(i="send"),o.instance[i](e(e({},n),{success:function(e){t({result:e}),"close"===i&&j.delete(j.keys().next().value)},fail:function(e){r(e)}}))}else r({errMsg:"socketTask not exists."})}))};function J(e,n){if(e.isOpend)n({data:e.openData});else{var t=setInterval((function(){e.isOpend&&(clearInterval(t),n(e.openData))}),200);setTimeout((function(){clearInterval(t)}),2e3)}}var F=["stopRecord","getRecorderManager","pauseVoice","stopVoice","pauseBackgroundAudio","stopBackgroundAudio","getBackgroundAudioManager","createAudioContext","createInnerAudioContext","createVideoContext","createCameraContext","createMapContext","canIUse","startAccelerometer","stopAccelerometer","startCompass","stopCompass","hideToast","hideLoading","showNavigationBarLoading","hideNavigationBarLoading","navigateBack","createAnimation","pageScrollTo","createSelectorQuery","createCanvasContext","createContext","drawCanvas","hideKeyboard","stopPullDownRefresh","arrayBufferToBase64","base64ToArrayBuffer"],z=new Map,G=["onCompassChange","onThemeChange","onUserCaptureScreen","onWindowResize","onMemoryWarning","onAccelerometerChange","onKeyboardHeightChange","onNetworkStatusChange","onPushMessage","onLocationChange","onGetWifiList","onWifiConnected","onWifiConnectedWithPartialInfo","onSocketOpen","onSocketError","onSocketMessage","onSocketClose"],K={},Q=/^\$|Sync$|Window$|WindowStyle$|sendHostEvent|sendNativeEvent|restoreGlobal|requireGlobal|getCurrentSubNVue|getMenuButtonBoundingClientRect|^report|interceptors|Interceptor$|getSubNVueById|requireNativePlugin|upx2px|hideKeyboard|canIUse|^create|Sync$|Manager$|base64ToArrayBuffer|arrayBufferToBase64|getLocale|setLocale|invokePushCallback|getWindowInfo|getDeviceInfo|getAppBaseInfo|getSystemSetting|getAppAuthorizeSetting|initUTS|requireUTS|registerUTS/,Z=/^on|^off/;function ee(e){return Q.test(e)||-1!==F.indexOf(e)}var ne={getPageStack:function(){return Promise.resolve({pageStack:getCurrentPages().map((function(e){return k(m(e))}))})},getCurrentPage:function(){var e=getCurrentPages(),n=e.length;return new Promise((function(t,r){n?t(k(m(e[n-1]))):r(Error("getCurrentPages().length=0"))}))},callUniMethod:function(n,t){var r=n.method,o=n.args;return new Promise((function(n,i){if("connectSocket"!==r){var u,a;if(G.includes(r)){z.has(r)||z.set(r,new Map);var c=o[0],s=function(e){t({id:c,result:{method:r,data:e}})};return r.startsWith("onSocket")?Y({method:r.replace("Socket","")}).then((function(e){return s(e)})).catch((function(e){return s(e)})):(z.get(r).set(c,s),uni[r](s)),n({result:null})}if(r.startsWith("off")&&G.includes(r.replace("off","on"))){var l=r.replace("off","on");if(z.has(l)){var f=o[0];if(void 0!==f){var d=z.get(l).get(f);uni[r](d),z.get(l).delete(f)}else{z.get(l).forEach((function(e){uni[r](e)})),z.delete(l)}}return n({result:null})}if(r.indexOf("Socket")>0)return Y(e({method:r.replace("Socket","")},o[0])).then((function(e){return n(e)})).catch((function(e){return i(e)}));if(!uni[r])return i(Error("uni."+r+" not exists"));if(ee(r))return n({result:uni[r].apply(uni,o)});var p=[Object.assign({},o[0]||{},{success:function(e){setTimeout((function(){n({result:e})}),"pageScrollTo"===r?350:0)},fail:function(e){i(Error(e.errMsg.replace(r+":fail ","")))}})];uni[r].apply(uni,p)}else(u=o[0].id,a=o[0].url,new Promise((function(e,n){var t=uni.connectSocket({url:a,success:function(){e({result:{errMsg:"connectSocket:ok"}})},fail:function(){n({result:{errMsg:"connectSocket:fail"}})}});j.set(u,{instance:t,isOpend:!1}),t.onOpen((function(e){j.get(u).isOpend=!0,j.get(u).openData=e}))}))).then((function(e){return n(e)})).catch((function(e){return i(e)}))}))},mockUniMethod:function(e){var n=e.method;if(!uni[n])throw Error("uni."+n+" not exists");if(!function(e){return!Z.test(e)}(n))throw Error("You can't mock uni."+n);var t,r=e.result,o=e.functionDeclaration;return s(r)&&s(o)?(K[n]&&(uni[n]=K[n],delete K[n]),Promise.resolve()):(t=s(o)?ee(n)?function(){return r}:function(e){setTimeout((function(){r.errMsg&&-1!==r.errMsg.indexOf(":fail")?e.fail&&e.fail(r):e.success&&e.success(r),e.complete&&e.complete(r)}),4)}:function(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];return new Function("return "+o)().apply(t,n.concat(e.args))},t.origin=K[n]||uni[n],K[n]||(K[n]=uni[n]),uni[n]=t,Promise.resolve())},captureScreenshot:function(e){return new Promise((function(n,t){U()?H({action:"captureScreenshot",args:e},(function(e,r){e?t(Error("captureScreenshot fail: "+e)):n(r)})):t(Error("captureScreenshot fail: supported only on the app platform."))}))},socketEmitter:function(n){return new Promise((function(t,r){(function(n){return new Promise((function(t,r){if(j.has(n.id)){var o=j.get(n.id),i=o.instance,u=n.method,a=n.id;if("onOpen"==u)return J(o,t);if(u.startsWith("on"))return i[u]((function(e){t({method:"Socket."+u,id:a,data:e})}));i[u](e(e({},n),{success:function(e){t(e),"close"===u&&j.delete(n.id)},fail:function(e){r(e)}}))}else r({errMsg:"socketTask not exists."})}))})(n).then((function(e){return t(e)})).catch((function(e){return r(e)}))}))}},te=ne,re={getData:function(e){return B(A(e.pageId),e.path)},setData:function(e){return X(A(e.pageId),e.data)},callMethod:function(e){var n,t=((n={})[M.VM_NOT_EXISTS]="Page["+e.pageId+"] not exists",n[M.METHOD_NOT_EXISTS]="page."+e.method+" not exists",n);return new Promise((function(n,r){q(A(e.pageId),e.method,e.args).then((function(e){return n(e)})).catch((function(e){r(Error(t[e]))}))}))},callMethodWithCallback:function(e){var n,t=((n={})[M.VM_NOT_EXISTS]="callMethodWithCallback:fail, Page["+e.pageId+"] not exists",n[M.METHOD_NOT_EXISTS]="callMethodWithCallback:fail, page."+e.method+" not exists",n),r=e.args[e.args.length-1];q(A(e.pageId),e.method,e.args).catch((function(e){r({errMsg:t[e]})}))}};function oe(e){return e.nodeId||e.elementId}var ie={getData:function(e){return B(V(e.pageId,oe(e)),e.path)},setData:function(e){return X(V(e.pageId,oe(e)),e.data)},callMethod:function(e){var n,t=oe(e),r=((n={})[M.VM_NOT_EXISTS]="Component["+e.pageId+":"+t+"] not exists",n[M.METHOD_NOT_EXISTS]="component."+e.method+" not exists",n);return new Promise((function(n,o){q(V(e.pageId,t),e.method,e.args).then((function(e){return n(e)})).catch((function(e){o(Error(r[e]))}))}))}};window.initRuntimeAutomator=de,window.onPostMessageFromUniXWebView=function(e,n,t){console.log("onPostMessageFromUniXWebView",e,n,t,R);var r=R[e];r&&(delete R[e],r(t,n))};var ue={};Object.keys(te).forEach((function(e){ue["App."+e]=te[e]})),Object.keys(re).forEach((function(e){ue["Page."+e]=re[e]})),Object.keys(ie).forEach((function(e){ue["Element."+e]=ie[e]}));var ae,ce,se=process.env.UNI_AUTOMATOR_WS_ENDPOINT;function le(e){ce.send({data:JSON.stringify(e)})}function fe(e){var n=JSON.parse(e.data),t=n.id,r=n.method,o=n.params,i={id:t},u=ue[r];if(!u){if(ae){var a=ae(t,r,o,i);if(!0===a)return;u=a}if(!u)return i.error={message:r+" unimplemented"},le(i)}try{u(o,le).then((function(e){e&&(i.result=e)})).catch((function(e){i.error={message:e.message}})).finally((function(){le(i)}))}catch(e){i.error={message:e.message},le(i)}}function de(e){void 0===e&&(e={}),(ce=uni.connectSocket({url:e.wsEndpoint||se,complete:function(){}})).onMessage(fe),ce.onOpen((function(n){e.success&&e.success(),console.log("已开启自动化测试...")})),ce.onError((function(e){console.log("automator.onError",e)})),ce.onClose((function(){e.fail&&e.fail({errMsg:"$$initRuntimeAutomator:fail"}),console.log("automator.onClose")}))}ae=function(e,n,t,r){var o=t.pageId,i=function(e){var n=getCurrentPages();if(!e)return n[n.length-1];return n.find((function(n){return h(n).id===e}))}(o);return i?(h(i).meta.isNVue,UniServiceJSBridge.publishHandler("sendAutoMessage",{id:e,method:n,params:t},o),!0):(r.error={message:"page["+o+"] not exists"},le(r),!0)},UniServiceJSBridge.subscribe("onAutoMessageReceive",(function(e){le(e)})),setTimeout((function(){if(U())H({action:"ready"});else{if(se&&se.endsWith(":0000"))return;de()}}),500);
