<template>
  <view class="result-container">
    <!-- 视频播放器 -->
    <view class="video-section">
      <video 
        v-if="videoUrl" 
        :src="videoUrl" 
        controls 
        class="result-video"
        :poster="videoPoster"
        show-fullscreen-btn
        show-play-btn
        show-center-play-btn
      ></video>
      <view v-else class="video-loading">
        <text>视频加载中...</text>
      </view>
    </view>
    
    <!-- 视频信息 -->
    <view class="video-info">
      <view class="info-item">
        <text class="info-label">处理时间:</text>
        <text class="info-value">{{ formatTime(taskInfo.finishTime) }}</text>
      </view>
      <view class="info-item">
        <text class="info-label">视频时长:</text>
        <text class="info-value">{{ formatDuration(taskInfo.duration) }}</text>
      </view>
      <view class="info-item">
        <text class="info-label">文件大小:</text>
        <text class="info-value">{{ formatFileSize(taskInfo.fileSize) }}</text>
      </view>
    </view>
    
    <!-- 操作按钮 -->
    <view class="action-buttons">
      <button @click="downloadVideo" class="download-btn" type="primary">
        下载视频
      </button>
      <button @click="shareVideo" class="share-btn">
        分享视频
      </button>
      <button @click="viewSubtitle" class="subtitle-btn">
        查看字幕
      </button>
    </view>
    
    <!-- 字幕预览弹窗 -->
    <view v-if="showSubtitleModal" class="subtitle-modal" @click="closeSubtitleModal">
      <view class="subtitle-content" @click.stop>
        <view class="subtitle-header">
          <text class="subtitle-title">字幕内容</text>
          <text class="close-btn" @click="closeSubtitleModal">×</text>
        </view>
        <scroll-view class="subtitle-list" scroll-y>
          <view v-for="(item, index) in subtitleList" :key="index" class="subtitle-item">
            <text class="subtitle-time">{{ item.startTime }} --> {{ item.endTime }}</text>
            <text class="subtitle-text">{{ item.text }}</text>
          </view>
        </scroll-view>
        <view class="subtitle-actions">
          <button @click="copySubtitle" class="copy-btn" size="mini">复制字幕</button>
          <button @click="downloadSubtitle" class="download-subtitle-btn" size="mini">下载SRT</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

// 响应式数据
const videoUrl = ref('')
const videoPoster = ref('')
const taskInfo = ref<any>({})
const showSubtitleModal = ref(false)
const subtitleList = ref<any[]>([])
const taskId = ref('')

// 页面加载时获取参数
onMounted(() => {
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  const options = currentPage.options || {}
  
  taskId.value = options.taskId || ''
  
  if (taskId.value) {
    loadTaskResult()
  } else {
    uni.showToast({
      title: '参数错误',
      icon: 'none'
    })
  }
})

// 加载任务结果
const loadTaskResult = async () => {
  try {
    uni.showLoading({ title: '加载中...' })
    
    // 获取任务详情
    const taskRes = await wx.cloud.callFunction({
      name: 'get-task-detail',
      data: { taskId: taskId.value }
    })
    
    taskInfo.value = taskRes.result
    
    // 获取视频播放URL
    const videoRes = await wx.cloud.callFunction({
      name: 'get-video-url',
      data: { fileId: taskInfo.value.processedVideoFileId }
    })
    
    videoUrl.value = videoRes.result.url
    videoPoster.value = videoRes.result.poster || ''
    
    uni.hideLoading()
  } catch (error) {
    console.error('加载结果失败:', error)
    uni.hideLoading()
    uni.showToast({
      title: '加载失败',
      icon: 'none'
    })
  }
}

// 下载视频
const downloadVideo = () => {
  if (!videoUrl.value) {
    uni.showToast({
      title: '视频未加载',
      icon: 'none'
    })
    return
  }
  
  uni.showLoading({ title: '下载中...' })
  
  uni.downloadFile({
    url: videoUrl.value,
    success: (res) => {
      if (res.statusCode === 200) {
        uni.saveVideoToPhotosAlbum({
          filePath: res.tempFilePath,
          success: () => {
            uni.hideLoading()
            uni.showToast({
              title: '保存成功',
              icon: 'success'
            })
          },
          fail: (err) => {
            console.error('保存失败:', err)
            uni.hideLoading()
            uni.showToast({
              title: '保存失败',
              icon: 'none'
            })
          }
        })
      }
    },
    fail: (err) => {
      console.error('下载失败:', err)
      uni.hideLoading()
      uni.showToast({
        title: '下载失败',
        icon: 'none'
      })
    }
  })
}

// 分享视频
const shareVideo = () => {
  uni.share({
    provider: 'weixin',
    scene: 'WXSceneSession',
    type: 5,
    videoPath: videoUrl.value,
    title: '视语翻译 - 带字幕视频',
    success: () => {
      uni.showToast({
        title: '分享成功',
        icon: 'success'
      })
    },
    fail: (err) => {
      console.error('分享失败:', err)
      uni.showToast({
        title: '分享失败',
        icon: 'none'
      })
    }
  })
}

// 查看字幕
const viewSubtitle = async () => {
  try {
    uni.showLoading({ title: '加载字幕...' })
    
    const res = await wx.cloud.callFunction({
      name: 'get-subtitle',
      data: { taskId: taskId.value }
    })
    
    subtitleList.value = res.result || []
    showSubtitleModal.value = true
    
    uni.hideLoading()
  } catch (error) {
    console.error('加载字幕失败:', error)
    uni.hideLoading()
    uni.showToast({
      title: '加载字幕失败',
      icon: 'none'
    })
  }
}

// 关闭字幕弹窗
const closeSubtitleModal = () => {
  showSubtitleModal.value = false
}

// 复制字幕
const copySubtitle = () => {
  const subtitleText = subtitleList.value
    .map(item => `${item.startTime} --> ${item.endTime}\n${item.text}`)
    .join('\n\n')
  
  uni.setClipboardData({
    data: subtitleText,
    success: () => {
      uni.showToast({
        title: '复制成功',
        icon: 'success'
      })
    }
  })
}

// 下载字幕文件
const downloadSubtitle = async () => {
  try {
    const res = await wx.cloud.callFunction({
      name: 'get-subtitle-file',
      data: { taskId: taskId.value }
    })
    
    // 这里可以实现SRT文件下载逻辑
    uni.showToast({
      title: '字幕文件已生成',
      icon: 'success'
    })
  } catch (error) {
    console.error('下载字幕失败:', error)
    uni.showToast({
      title: '下载失败',
      icon: 'none'
    })
  }
}

// 格式化时间
const formatTime = (timestamp: string): string => {
  if (!timestamp) return ''
  const date = new Date(timestamp)
  return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')} ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`
}

// 格式化时长
const formatDuration = (duration: number): string => {
  if (!duration) return ''
  const minutes = Math.floor(duration / 60)
  const seconds = Math.floor(duration % 60)
  return `${minutes}:${seconds.toString().padStart(2, '0')}`
}

// 格式化文件大小
const formatFileSize = (size: number): string => {
  if (!size) return ''
  if (size < 1024) return size + 'B'
  if (size < 1024 * 1024) return (size / 1024).toFixed(1) + 'KB'
  return (size / (1024 * 1024)).toFixed(1) + 'MB'
}
</script>

<style scoped>
.result-container {
  padding: 40rpx;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.video-section {
  background: white;
  border-radius: 20rpx;
  padding: 20rpx;
  margin-bottom: 40rpx;
}

.result-video {
  width: 100%;
  height: 400rpx;
  border-radius: 10rpx;
}

.video-loading {
  height: 400rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 28rpx;
}

.video-info {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 40rpx;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 28rpx;
  color: #666;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.download-btn {
  background-color: #007AFF;
  color: white;
  border: none;
  border-radius: 10rpx;
  height: 88rpx;
  font-size: 32rpx;
}

.share-btn, .subtitle-btn {
  background-color: white;
  color: #007AFF;
  border: 2rpx solid #007AFF;
  border-radius: 10rpx;
  height: 88rpx;
  font-size: 32rpx;
}

.subtitle-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.subtitle-content {
  background: white;
  border-radius: 20rpx;
  width: 90%;
  max-height: 80%;
  display: flex;
  flex-direction: column;
}

.subtitle-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.subtitle-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.close-btn {
  font-size: 60rpx;
  color: #999;
  line-height: 1;
}

.subtitle-list {
  flex: 1;
  padding: 20rpx 30rpx;
}

.subtitle-item {
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.subtitle-item:last-child {
  border-bottom: none;
}

.subtitle-time {
  display: block;
  font-size: 24rpx;
  color: #999;
  margin-bottom: 10rpx;
}

.subtitle-text {
  display: block;
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
}

.subtitle-actions {
  display: flex;
  gap: 20rpx;
  padding: 30rpx;
  border-top: 1rpx solid #f0f0f0;
}

.copy-btn, .download-subtitle-btn {
  flex: 1;
  background-color: #007AFF;
  color: white;
  border: none;
  border-radius: 8rpx;
}
</style>
