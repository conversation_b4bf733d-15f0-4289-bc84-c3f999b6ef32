"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "history",
  setup(__props) {
    const historyList = common_vendor.ref([]);
    const searchKeyword = common_vendor.ref("");
    const currentFilter = common_vendor.ref("all");
    const hasMore = common_vendor.ref(true);
    const loading = common_vendor.ref(false);
    const filterOptions = [
      { label: "全部", value: "all" },
      { label: "处理中", value: "processing" },
      { label: "已完成", value: "completed" },
      { label: "失败", value: "failed" }
    ];
    const filteredList = common_vendor.computed(() => {
      let list = historyList.value;
      if (currentFilter.value !== "all") {
        list = list.filter((item) => item.status === currentFilter.value);
      }
      if (searchKeyword.value) {
        const keyword = searchKeyword.value.toLowerCase();
        list = list.filter(
          (item) => (item.fileName || "").toLowerCase().includes(keyword) || (item.errorMsg || "").toLowerCase().includes(keyword)
        );
      }
      return list;
    });
    common_vendor.onMounted(() => {
      loadHistoryList();
    });
    const loadHistoryList = async (isLoadMore = false) => {
      if (loading.value)
        return;
      try {
        loading.value = true;
        const res = await common_vendor.wx$1.cloud.callFunction({
          name: "get-history-list",
          data: {
            skip: isLoadMore ? historyList.value.length : 0,
            limit: 20
          }
        });
        const newList = res.result || [];
        if (isLoadMore) {
          historyList.value = [...historyList.value, ...newList];
        } else {
          historyList.value = newList;
        }
        hasMore.value = newList.length === 20;
      } catch (error) {
        console.error("加载历史记录失败:", error);
        common_vendor.index.showToast({
          title: "加载失败",
          icon: "none"
        });
      } finally {
        loading.value = false;
      }
    };
    const onSearchInput = () => {
    };
    const changeFilter = (filter) => {
      currentFilter.value = filter;
    };
    const loadMore = () => {
      if (hasMore.value && !loading.value) {
        loadHistoryList(true);
      }
    };
    const viewDetail = (item) => {
      if (item.status === "processing") {
        common_vendor.index.navigateTo({
          url: `/pages/process/process?fileId=${item.originalVideoFileId}`
        });
      } else if (item.status === "completed") {
        common_vendor.index.navigateTo({
          url: `/pages/result/result?taskId=${item._id}`
        });
      }
    };
    const viewResult = (item) => {
      common_vendor.index.navigateTo({
        url: `/pages/result/result?taskId=${item._id}`
      });
    };
    const retryProcess = (item) => {
      common_vendor.index.showModal({
        title: "确认重新处理",
        content: "是否重新处理该视频？",
        success: async (res) => {
          if (res.confirm) {
            try {
              await common_vendor.wx$1.cloud.callFunction({
                name: "retry-task",
                data: { taskId: item._id }
              });
              common_vendor.index.showToast({
                title: "已重新开始处理",
                icon: "success"
              });
              loadHistoryList();
            } catch (error) {
              console.error("重新处理失败:", error);
              common_vendor.index.showToast({
                title: "操作失败",
                icon: "none"
              });
            }
          }
        }
      });
    };
    const deleteItem = (item) => {
      common_vendor.index.showModal({
        title: "确认删除",
        content: "删除后无法恢复，是否确认删除？",
        success: async (res) => {
          if (res.confirm) {
            try {
              await common_vendor.wx$1.cloud.callFunction({
                name: "delete-task",
                data: { taskId: item._id }
              });
              const index = historyList.value.findIndex((i) => i._id === item._id);
              if (index > -1) {
                historyList.value.splice(index, 1);
              }
              common_vendor.index.showToast({
                title: "删除成功",
                icon: "success"
              });
            } catch (error) {
              console.error("删除失败:", error);
              common_vendor.index.showToast({
                title: "删除失败",
                icon: "none"
              });
            }
          }
        }
      });
    };
    const getStatusText = (status) => {
      switch (status) {
        case "processing":
          return "处理中";
        case "completed":
          return "已完成";
        case "failed":
          return "失败";
        default:
          return "未知";
      }
    };
    const formatTime = (timestamp) => {
      if (!timestamp)
        return "";
      const date = new Date(timestamp);
      const now = /* @__PURE__ */ new Date();
      const diff = now.getTime() - date.getTime();
      if (diff < 6e4)
        return "刚刚";
      if (diff < 36e5)
        return `${Math.floor(diff / 6e4)}分钟前`;
      if (diff < 864e5)
        return `${Math.floor(diff / 36e5)}小时前`;
      return `${date.getMonth() + 1}-${date.getDate()} ${date.getHours().toString().padStart(2, "0")}:${date.getMinutes().toString().padStart(2, "0")}`;
    };
    const formatFileSize = (size) => {
      if (!size)
        return "";
      if (size < 1024)
        return size + "B";
      if (size < 1024 * 1024)
        return (size / 1024).toFixed(1) + "KB";
      return (size / (1024 * 1024)).toFixed(1) + "MB";
    };
    const getProcessDuration = (item) => {
      if (!item.createTime || !item.finishTime)
        return "";
      const start = new Date(item.createTime).getTime();
      const end = new Date(item.finishTime).getTime();
      const duration = Math.floor((end - start) / 1e3);
      if (duration < 60)
        return `${duration}秒`;
      return `${Math.floor(duration / 60)}分${duration % 60}秒`;
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.o([($event) => searchKeyword.value = $event.detail.value, onSearchInput]),
        b: searchKeyword.value,
        c: common_vendor.f(filterOptions, (filter, k0, i0) => {
          return {
            a: common_vendor.t(filter.label),
            b: filter.value,
            c: currentFilter.value === filter.value ? 1 : "",
            d: common_vendor.o(($event) => changeFilter(filter.value), filter.value)
          };
        }),
        d: filteredList.value.length === 0
      }, filteredList.value.length === 0 ? {} : {
        e: common_vendor.f(filteredList.value, (item, k0, i0) => {
          return common_vendor.e({
            a: common_vendor.t(getStatusText(item.status)),
            b: common_vendor.n(item.status),
            c: common_vendor.t(formatTime(item.createTime)),
            d: common_vendor.t(item.fileName || "未命名视频"),
            e: common_vendor.t(formatFileSize(item.fileSize)),
            f: item.status === "completed"
          }, item.status === "completed" ? {
            g: common_vendor.t(getProcessDuration(item))
          } : {}, {
            h: item.status === "failed"
          }, item.status === "failed" ? {
            i: common_vendor.t(item.errorMsg || "处理失败")
          } : {}, {
            j: item.status === "completed"
          }, item.status === "completed" ? {
            k: common_vendor.o(($event) => viewResult(item), item._id)
          } : {}, {
            l: item.status === "failed"
          }, item.status === "failed" ? {
            m: common_vendor.o(($event) => retryProcess(item), item._id)
          } : {}, {
            n: common_vendor.o(($event) => deleteItem(item), item._id),
            o: item._id,
            p: common_vendor.o(($event) => viewDetail(item), item._id)
          });
        })
      }, {
        f: hasMore.value
      }, hasMore.value ? {} : {}, {
        g: common_vendor.o(loadMore)
      });
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-73685b36"]]);
wx.createPage(MiniProgramPage);
