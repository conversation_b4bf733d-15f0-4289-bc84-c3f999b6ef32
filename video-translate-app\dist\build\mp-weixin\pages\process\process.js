"use strict";const e=require("../../common/vendor.js"),a=e.defineComponent({__name:"process",setup(a){const l=e.ref("processing"),t=e.ref(1),s=e.ref(""),u=e.ref(""),r=e.ref("");let n=null;e.onMounted(()=>{const a=getCurrentPages(),l=a[a.length-1].options||{};u.value=l.fileId||"",u.value?c():e.index.showToast({title:"参数错误",icon:"none"})}),e.onUnmounted(()=>{n&&clearInterval(n)});const c=()=>{o(),n=setInterval(()=>{"processing"===l.value?o():clearInterval(n)},3e3)},o=async()=>{try{const a=(await e.wx$1.cloud.callFunction({name:"get-task-status",data:{fileId:u.value}})).result;a&&(r.value=a._id,l.value=a.status,s.value=a.errorMsg||"",v(a.status))}catch(a){console.error("查询任务状态失败:",a)}},v=e=>{switch(e){case"processing":t.value=2;break;case"completed":t.value=4}},i=()=>{switch(l.value){case"processing":return"正在处理中...";case"completed":return"处理完成";case"failed":return"处理失败";default:return"未知状态"}},d=()=>{switch(l.value){case"processing":return"请耐心等待，通常需要1-3分钟";case"completed":return"视频字幕已生成完成";case"failed":return"处理过程中出现错误";default:return""}},p=()=>{e.index.navigateTo({url:`/pages/result/result?taskId=${r.value}`})},f=()=>{e.index.showModal({title:"确认重新处理",content:"是否重新处理该视频？",success:e=>{e.confirm&&(l.value="processing",t.value=1,s.value="",c())}})},g=()=>{e.index.switchTab({url:"/pages/index/index"})};return(a,u)=>e.e({a:"processing"===l.value},("processing"===l.value||"completed"===l.value||l.value,{}),{b:"completed"===l.value,c:"failed"===l.value,d:e.t(i()),e:e.t(d()),f:t.value>=1?1:"",g:t.value>1?1:"",h:t.value>=2?1:"",i:t.value>2?1:"",j:t.value>=3?1:"",k:t.value>3?1:"",l:t.value>=4?1:"",m:t.value>4?1:"",n:"completed"===l.value},"completed"===l.value?{o:e.o(p)}:{},{p:"failed"===l.value},"failed"===l.value?{q:e.o(f)}:{},{r:e.o(g),s:s.value},s.value?{t:e.t(s.value)}:{})}}),l=e._export_sfc(a,[["__scopeId","data-v-cf63811e"]]);wx.createPage(l);
