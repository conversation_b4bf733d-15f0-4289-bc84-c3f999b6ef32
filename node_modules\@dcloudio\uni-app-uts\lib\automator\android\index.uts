import {
  type CallUniMethodParams,
  type CaptureScreenshotParams,
  type GetCurrentPageParams,
  callUniMethod,
  captureScreenshot,
  getCurrentPage,
  getPageStack,
  // @ts-expect-error
} from './apis/App/index.uts'
import {
  type CallMethodParams,
  type GetWindowPropertiesParams,
  type GetDataParams as PageGetDataParams,
  type GetElementParams as PageGetElementParams,
  type SetDataParams as PageSetDataParams,
  getWindowProperties,
  callMethod as pageCallMethod,
  getData as pageGetData,
  getElement as pageGetElement,
  getElements as pageGetElements,
  setData as pageSetData,
  // @ts-expect-error
} from './apis/Page.uts'
// @ts-expect-error
import { type SocketEmitterParams, socketEmitter } from './apis/App/Socket.uts'
import {
  type CallFunctionParams as ElementCallFunctionParams,
  type CallMethodParams as ElementCallMethodParams,
  type GetDataParams as ElementGetDataParams,
  type GetElementParams as ElementGetElementParams,
  type SetDataParams as ElementSetDataParams,
  type GetAttributesParams,
  type GetDOMPropertiesParams,
  type GetOffsetParams,
  type GetPropertiesParams,
  type GetStylesParams,
  type HandleTouchEventParams,
  type LongpressParams,
  type TapParams,
  type TriggerEventParams,
  callFunction as elementCallFunction,
  callMethod as elementCallMethod,
  getData as elementGetData,
  getElement as elementGetElement,
  getElements as elementGetElements,
  setData as elementSetData,
  getAttributes,
  getDOMProperties,
  getOffset,
  getProperties,
  getStyles,
  handleTouchEvent,
  longpress,
  tap,
  triggerEvent
  // @ts-expect-error
} from './apis/Element.uts'

let socketTask: SocketTask | null = null
// @ts-expect-error
const wsEndpoint = process.env.UNI_AUTOMATOR_WS_ENDPOINT

export function send(data: any) {
  socketTask?.send({ data: JSON.stringify(data) } as SendSocketMessageOptions)
}

export type Callback = (result: any | null, error: any | null) => void

type Msg = {
  id: string,
  method: string,
  params: any
}

export function onMessage(msg: string) {
  // @ts-expect-error
  const json = JSON.parse<Msg>(msg)!
  const method = json.method
  if ((method == 'ping')) {
    send('pong')
    return
  }
  const params = JSON.stringify(json.params)
  const res = { id: json.id }
  try {
    const callback = (result?: any | null, error?: any | null) => {
      res['result'] = result
      res['error'] = error
      send(res)
    }
    if (method.startsWith('App.')) {
      switch (method) {
        case 'App.callUniMethod':
          // @ts-expect-error
          callUniMethod(JSON.parse<CallUniMethodParams>(params)!, callback)
          break
        case 'App.captureScreenshot':
          // @ts-expect-error
          captureScreenshot(JSON.parse<CaptureScreenshotParams>(params)!, callback)
          break
        case 'App.getPageStack':
          getPageStack(callback)
          break
        case 'App.getCurrentPage':
          getCurrentPage({ callback } as GetCurrentPageParams)
          break
        case 'App.socketEmitter':
          // @ts-expect-error
          socketEmitter(JSON.parse<SocketEmitterParams>(params)!, callback)
          break
      }
    } else if (method.startsWith('Page.')) {
      switch (method) {
        case 'Page.getData':
          // @ts-expect-error
          pageGetData(JSON.parse<PageGetDataParams>(params)!, callback)
          break
        case 'Page.setData':
          // @ts-expect-error
          pageSetData(JSON.parse<PageSetDataParams>(params)!, callback)
          break
        case 'Page.callMethod':
          // @ts-expect-error
          pageCallMethod(JSON.parse<CallMethodParams>(params)!, callback)
          break
        case 'Page.getElement':
          // @ts-expect-error
          pageGetElement(JSON.parse<PageGetElementParams>(params)!, callback)
          break
        case 'Page.getElements':
          // @ts-expect-error
          pageGetElements(JSON.parse<PageGetElementParams>(params)!, callback)
          break
        case 'Page.getWindowProperties':
          // @ts-expect-error
          getWindowProperties(JSON.parse<GetWindowPropertiesParams>(params)!, callback)
          break
      }
    } else if (method.startsWith('Element.')) {
      switch (method) {
        case 'Element.getElement':
          // @ts-expect-error
          elementGetElement(JSON.parse<ElementGetElementParams>(params)!, callback)
          break
        case 'Element.getElements':
          // @ts-expect-error
          elementGetElements(JSON.parse<ElementGetElementParams>(params)!, callback)
          break
        case 'Element.getDOMProperties':
          // @ts-expect-error
          getDOMProperties(JSON.parse<GetDOMPropertiesParams>(params)!, callback)
          break
        case 'Element.getProperties':
          // @ts-expect-error
          getProperties(JSON.parse<GetPropertiesParams>(params)!, callback)
          break
        case 'Element.callFunction':
          // @ts-expect-error
          elementCallFunction(JSON.parse<ElementCallFunctionParams>(params)!, callback)
          break
        case 'Element.tap':
          // @ts-expect-error
          tap(JSON.parse<TapParams>(params)!, callback)
          break
        case 'Element.callMethod':
          // @ts-expect-error
          elementCallMethod(JSON.parse<ElementCallMethodParams>(params)!, callback)
          break
        case 'Element.getData':
          // @ts-expect-error
          elementGetData(JSON.parse<ElementGetDataParams>(params)!, callback)
          break
        case 'Element.setData':
          // @ts-expect-error
          elementSetData(JSON.parse<ElementSetDataParams>(params)!, callback)
          break
        case 'Element.getOffset':
          // @ts-expect-error
          getOffset(JSON.parse<GetOffsetParams>(params)!, callback)
          break
        case 'Element.longpress':
          // @ts-expect-error
          longpress(JSON.parse<LongpressParams>(params)!, callback)
          break
        case 'Element.touchstart':
        case 'Element.touchmove':
        case 'Element.touchend':
          // @ts-expect-error
          handleTouchEvent(JSON.parse<HandleTouchEventParams>(params)!, method.split('.')[1], callback)
          break
        case 'Element.getAttributes':
          // @ts-expect-error
          getAttributes(JSON.parse<GetAttributesParams>(params)!, callback)
          break
        case 'Element.getStyles':
          // @ts-expect-error
          getStyles(JSON.parse<GetStylesParams>(params)!, callback)
          break
        case 'Element.triggerEvent':
          // @ts-expect-error
          triggerEvent(JSON.parse<TriggerEventParams>(params)!, callback)
          break
      }
    }
  } catch (error) {
    res['error'] = { message: error.stackTraceToString() }
    send(res)
  }
}

export function initAutomator() {
  // @ts-expect-error
  socketTask = uni.connectSocket({
    url: wsEndpoint
  });
  socketTask!.onMessage((res) => {
    onMessage(res.data as string)
  })
  socketTask!.onOpen((_) => {
    console.warn("automator.onOpen")
  })
  socketTask!.onError((err) => {
    console.warn(`automator.onError: ${JSON.stringify(err)}`);
  })
  socketTask!.onClose((_) => {
    console.warn("automator.onClose");
  })
}
