"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.uniDecryptUniModulesPlugin = exports.resolveExtApiProvider = exports.buildUniExtApis = exports.uniUTSAppUniModulesPlugin = exports.getCurrentCompiledUTSProviders = exports.getCurrentCompiledUTSPlugins = exports.createAppHarmonyUniModulesSyncFilePreprocessorOnce = exports.createAppIosUniModulesSyncFilePreprocessorOnce = exports.createAppAndroidUniModulesSyncFilePreprocessorOnce = exports.rewriteUniModulesConsoleExpr = void 0;
const fs_extra_1 = __importDefault(require("fs-extra"));
const path_1 = __importDefault(require("path"));
const uni_shared_1 = require("@dcloudio/uni-shared");
const pluginutils_1 = require("@rollup/pluginutils");
const uts_1 = require("../../../uts");
const utils_1 = require("../../utils");
const uni_modules_1 = require("../../../uni_modules");
const uni_modules_cloud_1 = require("../../../uni_modules.cloud");
const utils_2 = require("../../../utils");
const json_1 = require("../../../json");
const fs_1 = require("../../../fs");
const context_1 = require("../../../preprocess/context");
const hbx_1 = require("../../../hbx");
const console_1 = require("../../../logs/console");
/* eslint-disable no-restricted-globals */
const { preprocess } = require('../../../../lib/preprocess');
function rewriteUniModulesConsoleExpr(fileName, content) {
    // 仅开发模式补充console.log的at信息
    if (process.env.NODE_ENV !== 'development') {
        return content;
    }
    if (content.includes('console.')) {
        return (0, console_1.rewriteConsoleExpr)('__f__', '', (0, utils_2.normalizePath)(path_1.default.relative(process.env.UNI_INPUT_DIR, fileName.split('?')[0])), content, false).code;
    }
    return content;
}
exports.rewriteUniModulesConsoleExpr = rewriteUniModulesConsoleExpr;
function createUniModulesSyncFilePreprocessor(platform, utsPlatform, isX) {
    const { preVueContext, preUVueContext } = (0, context_1.initScopedPreContext)(platform, undefined, utsPlatform, isX);
    const preContext = isX ? preUVueContext : preVueContext;
    if (!isX) {
        if (utsPlatform === 'app-android') {
            preContext.APP_ANDROID = true;
        }
        if (utsPlatform === 'app-ios') {
            preContext.APP_IOS = true;
        }
        if (utsPlatform === 'app-harmony') {
            preContext.APP_HARMONY = true;
        }
    }
    function preJs(jsCode) {
        return preprocess(jsCode, preContext, { type: 'js' });
    }
    function preHtml(htmlCode) {
        return preprocess(htmlCode, preContext, { type: 'html' });
    }
    return async (content, fileName) => {
        const extname = path_1.default.extname(fileName);
        if (extname === '.json') {
            return (0, pluginutils_1.dataToEsm)(JSON.parse(preJs(content)), {
                namedExports: true,
                preferConst: true,
            });
        }
        else if (extname === '.uts' || extname === '.ts') {
            return replaceExtApiPages(rewriteUniModulesConsoleExpr(fileName, preJs(content)));
        }
        else if (extname === '.uvue' || extname === '.vue') {
            return rewriteUniModulesConsoleExpr(fileName, preJs(preHtml(content)));
        }
        return content;
    };
}
function replaceExtApiPages(code) {
    // 定制实现
    if (process.env.UNI_COMPILE_EXT_API_PAGES) {
        const pages = JSON.parse(process.env.UNI_COMPILE_EXT_API_PAGES);
        Object.keys(pages).forEach((page) => {
            code = code.replaceAll(page, pages[page]);
        });
    }
    return code;
}
function createAppAndroidUniModulesSyncFilePreprocessorOnce(isX) {
    return isX
        ? createUniXAppAndroidUniModulesSyncFilePreprocessorOnce()
        : createUniAppAndroidUniModulesSyncFilePreprocessorOnce();
}
exports.createAppAndroidUniModulesSyncFilePreprocessorOnce = createAppAndroidUniModulesSyncFilePreprocessorOnce;
function createAppIosUniModulesSyncFilePreprocessorOnce(isX) {
    return isX
        ? createUniXAppIosUniModulesSyncFilePreprocessorOnce()
        : createUniAppIosUniModulesSyncFilePreprocessorOnce();
}
exports.createAppIosUniModulesSyncFilePreprocessorOnce = createAppIosUniModulesSyncFilePreprocessorOnce;
function createAppHarmonyUniModulesSyncFilePreprocessorOnce(isX) {
    return isX
        ? createUniXAppHarmonyUniModulesSyncFilePreprocessorOnce()
        : createUniAppHarmonyUniModulesSyncFilePreprocessorOnce();
}
exports.createAppHarmonyUniModulesSyncFilePreprocessorOnce = createAppHarmonyUniModulesSyncFilePreprocessorOnce;
const createUniAppAndroidUniModulesSyncFilePreprocessorOnce = (0, uni_shared_1.once)(() => {
    return createUniModulesSyncFilePreprocessor('app', 'app-android', false);
});
const createUniAppIosUniModulesSyncFilePreprocessorOnce = (0, uni_shared_1.once)(() => {
    return createUniModulesSyncFilePreprocessor('app', 'app-ios', false);
});
const createUniAppHarmonyUniModulesSyncFilePreprocessorOnce = (0, uni_shared_1.once)(() => {
    return createUniModulesSyncFilePreprocessor('app', 'app-harmony', false);
});
const createUniXAppAndroidUniModulesSyncFilePreprocessorOnce = (0, uni_shared_1.once)(() => {
    return createUniModulesSyncFilePreprocessor('app', 'app-android', true);
});
const createUniXAppIosUniModulesSyncFilePreprocessorOnce = (0, uni_shared_1.once)(() => {
    return createUniModulesSyncFilePreprocessor('app', 'app-ios', true);
});
const createUniXAppHarmonyUniModulesSyncFilePreprocessorOnce = (0, uni_shared_1.once)(() => {
    return createUniModulesSyncFilePreprocessor('app', 'app-harmony', true);
});
const utsModuleCaches = new Map();
const utsPlugins = new Set();
const utsProviders = new Set();
function getCurrentCompiledUTSPlugins() {
    return utsPlugins;
}
exports.getCurrentCompiledUTSPlugins = getCurrentCompiledUTSPlugins;
function getCurrentCompiledUTSProviders() {
    return utsProviders;
}
exports.getCurrentCompiledUTSProviders = getCurrentCompiledUTSProviders;
let uniExtApiCompiler = async () => { };
function emptyCacheDir(platform) {
    const uvueOutputDir = (0, uts_1.uvueOutDir)(platform);
    const tscOutputDir = (0, uts_1.tscOutDir)(platform);
    function emptyUVueDir() {
        if (fs_extra_1.default.existsSync(uvueOutputDir)) {
            (0, fs_1.emptyDir)(uvueOutputDir);
        }
    }
    emptyUVueDir();
    function emptyTscDir() {
        if (fs_extra_1.default.existsSync(tscOutputDir)) {
            (0, fs_1.emptyDir)(tscOutputDir);
        }
    }
    emptyTscDir();
}
const emptyKotlinCacheDirOnce = (0, uni_shared_1.once)(() => {
    emptyCacheDir('app-android');
});
const emptySwiftCacheDirOnce = (0, uni_shared_1.once)(() => {
    emptyCacheDir('app-ios');
});
const emptyHarmonyCacheDirOnce = (0, uni_shared_1.once)(() => {
    emptyCacheDir('app-harmony');
});
// 该插件仅限app-android、app-ios、app-harmony
function uniUTSAppUniModulesPlugin(options = {}) {
    const isX = process.env.UNI_APP_X === 'true';
    const inputDir = process.env.UNI_INPUT_DIR;
    process.env.UNI_UTS_USING_ROLLUP = 'true';
    const uniModulesDir = (0, utils_2.normalizePath)(path_1.default.resolve(inputDir, 'uni_modules'));
    // 非 x 项目，非 HBuilderX
    if (!isX && !(0, hbx_1.isInHBuilderX)()) {
        try {
            (0, uts_1.resolveUTSCompiler)(true);
        }
        catch (e) {
            return {
                name: 'uni:uts-uni_modules-placeholder',
                apply: 'build',
                enforce: 'pre',
            };
        }
    }
    const { createUniXKotlinCompilerOnce, createUniXSwiftCompilerOnce, createUniXArkTSCompilerOnce, syncUniModuleFilesByCompiler, resolveTscUniModuleIndexFileName, } = (0, uts_1.resolveUTSCompiler)();
    const uniXKotlinCompiler = process.env.UNI_APP_X_TSC === 'true' &&
        (process.env.UNI_UTS_PLATFORM === 'app-android' ||
            process.env.UNI_UTS_PLATFORM === 'app')
        ? createUniXKotlinCompilerOnce()
        : null;
    const uniXSwiftCompiler = process.env.UNI_APP_X_TSC === 'true' &&
        (process.env.UNI_UTS_PLATFORM === 'app-ios' ||
            process.env.UNI_UTS_PLATFORM === 'app')
        ? createUniXSwiftCompilerOnce()
        : null;
    const uniXArkTSCompiler = process.env.UNI_APP_X_TSC === 'true' &&
        process.env.UNI_UTS_PLATFORM === 'app-harmony'
        ? createUniXArkTSCompilerOnce()
        : null;
    if (uniXKotlinCompiler) {
        emptyKotlinCacheDirOnce();
        (0, uts_1.genUniExtApiDeclarationFileOnce)((0, uts_1.tscOutDir)('app-android'));
    }
    if (uniXSwiftCompiler) {
        emptySwiftCacheDirOnce();
        (0, uts_1.genUniExtApiDeclarationFileOnce)((0, uts_1.tscOutDir)('app-ios'));
    }
    if (uniXArkTSCompiler) {
        emptyHarmonyCacheDirOnce();
        (0, uts_1.genUniExtApiDeclarationFileOnce)((0, uts_1.tscOutDir)('app-harmony'));
    }
    const changedFiles = new Map();
    const compilePlugin = async (pluginDir) => {
        const pluginId = path_1.default.basename(pluginDir);
        if (uniXKotlinCompiler) {
            const platform = 'app-android';
            await syncUniModuleFilesByCompiler(platform, uniXKotlinCompiler, pluginDir, createAppAndroidUniModulesSyncFilePreprocessorOnce(isX));
        }
        if (uniXSwiftCompiler) {
            const platform = 'app-ios';
            await syncUniModuleFilesByCompiler(platform, uniXSwiftCompiler, pluginDir, createAppIosUniModulesSyncFilePreprocessorOnce(isX));
        }
        if (uniXArkTSCompiler) {
            const platform = 'app-harmony';
            await syncUniModuleFilesByCompiler(platform, uniXArkTSCompiler, pluginDir, createAppHarmonyUniModulesSyncFilePreprocessorOnce(isX));
        }
        if (!utsPlugins.has(pluginId)) {
            utsPlugins.add(pluginId);
            if (uniXKotlinCompiler) {
                const platform = 'app-android';
                const indexFileName = resolveTscUniModuleIndexFileName(platform, pluginDir);
                if (indexFileName) {
                    await uniXKotlinCompiler.addRootFile(indexFileName);
                }
            }
            if (uniXSwiftCompiler) {
                const platform = 'app-ios';
                const indexFileName = resolveTscUniModuleIndexFileName(platform, pluginDir);
                if (indexFileName) {
                    await uniXSwiftCompiler.addRootFile(indexFileName);
                }
            }
            if (uniXArkTSCompiler) {
                const platform = 'app-harmony';
                const indexFileName = resolveTscUniModuleIndexFileName(platform, pluginDir);
                if (indexFileName) {
                    await uniXArkTSCompiler.addRootFile(indexFileName);
                }
            }
        }
        // 处理uni_modules中的文件变更
        const files = changedFiles.get(pluginId);
        if (files) {
            // 仅限watch模式是会生效
            changedFiles.delete(pluginId);
            if (uniXKotlinCompiler) {
                await uniXKotlinCompiler.invalidate(files);
            }
            if (uniXSwiftCompiler) {
                await uniXSwiftCompiler.invalidate(files);
            }
            if (uniXArkTSCompiler) {
                await uniXArkTSCompiler.invalidate(files);
            }
        }
        const pkgJson = require(path_1.default.join(pluginDir, 'package.json'));
        const isExtApi = !!pkgJson.uni_modules?.['uni-ext-api'];
        const extApiProvider = resolveExtApiProvider(pkgJson);
        // 如果是 provider 扩展，需要判断 provider 的宿主插件是否在本地，在的话，自动导入该宿主插件包名
        let uniExtApiProviderServicePlugin = '';
        if (extApiProvider?.servicePlugin) {
            if (fs_extra_1.default.existsSync(path_1.default.resolve(inputDir, 'uni_modules', extApiProvider.servicePlugin))) {
                uniExtApiProviderServicePlugin = extApiProvider.servicePlugin;
            }
        }
        const compiler = (0, uts_1.resolveUTSCompiler)();
        // 处理依赖的 uts 插件
        // TODO 当本地有ext-api时，也应该自动加入deps，不然uts内部使用了该api，也会导致编译失败
        const deps = (0, uni_modules_1.parseUTSModuleDeps)(pkgJson.uni_modules?.dependencies || [], inputDir);
        if (deps.length) {
            for (const dep of deps) {
                await compilePlugin(path_1.default.resolve(inputDir, 'uni_modules', dep));
            }
        }
        if (process.env.UNI_PLATFORM === 'app-harmony') {
            return compiler.compileArkTS(pluginDir, {
                isX: !!options.x,
                isExtApi,
                transform: {
                    uniExtApiProviderName: extApiProvider?.name,
                    uniExtApiProviderService: extApiProvider?.service,
                    uniExtApiProviderServicePlugin,
                },
            });
        }
        function filterAutoImports(autoImports, source) {
            if (autoImports[source]) {
                // 移除 source
                return Object.keys(autoImports).reduce((imports, key) => {
                    if (key !== source) {
                        imports[key] = autoImports[key];
                    }
                    return imports;
                }, {});
            }
            return autoImports;
        }
        return compiler.compile(pluginDir, {
            isX: !!options.x,
            isSingleThread: !!options.isSingleThread,
            isPlugin: true,
            isExtApi,
            extApis: options.extApis,
            sourceMap: (0, utils_2.enableSourceMap)(),
            uni_modules: deps,
            transform: {
                uniExtApiProviderName: extApiProvider?.name,
                uniExtApiProviderService: extApiProvider?.service,
                uniExtApiProviderServicePlugin,
            },
            async kotlinAutoImports() {
                return (0, uts_1.initUTSKotlinAutoImportsOnce)().then((autoImports) => {
                    return filterAutoImports(autoImports, (0, uts_1.parseKotlinPackageWithPluginId)(pluginId, true));
                });
            },
            async swiftAutoImports() {
                return (0, uts_1.initUTSSwiftAutoImportsOnce)().then((autoImports) => {
                    return filterAutoImports(autoImports, (0, uts_1.parseSwiftPackageWithPluginId)(pluginId, true));
                });
            },
        });
    };
    uniExtApiCompiler = async () => {
        // 此方法为兜底方法，确保uni_modules中的所有插件都会编译，目前仅用于编译provider
        // 获取 provider 扩展(编译所有uni)
        const plugins = (0, uni_modules_1.getUniExtApiPlugins)().filter((provider) => !utsPlugins.has(provider.plugin));
        for (const plugin of plugins) {
            const pluginDir = path_1.default.resolve(inputDir, 'uni_modules', plugin.plugin);
            // 如果是 app-js 环境
            if (process.env.UNI_APP_X_UVUE_SCRIPT_ENGINE === 'js') {
                if (fs_extra_1.default.existsSync(path_1.default.resolve(pluginDir, 'utssdk', 'app-js', 'index.uts'))) {
                    continue;
                }
            }
            if (process.env.UNI_APP_X !== 'true' &&
                process.env.UNI_PLATFORM === 'app-harmony') {
                // uniExtApiCompiler本身是为X项目准备的，但是在app-harmony项目中也会调用
                if (!fs_extra_1.default.existsSync(path_1.default.resolve(pluginDir, 'utssdk', 'app-harmony', 'index.uts'))) {
                    continue;
                }
            }
            utsProviders.add(plugin.plugin);
            const result = await compilePlugin(pluginDir);
            if (result) {
                // 时机不对，不能addWatch
                // result.deps.forEach((dep) => {
                //   this.addWatchFile(dep)
                // })
            }
        }
    };
    return {
        name: 'uni:uts-uni_modules',
        apply: 'build',
        enforce: 'pre',
        async configResolved() {
            if (uniXKotlinCompiler) {
                await uniXKotlinCompiler.init();
            }
            if (uniXSwiftCompiler) {
                await uniXSwiftCompiler.init();
            }
            if (uniXArkTSCompiler) {
                await uniXArkTSCompiler.init();
            }
        },
        resolveId(id, importer) {
            if ((0, uts_1.isUTSProxy)(id) || (0, uts_1.isUniHelpers)(id)) {
                return id;
            }
            // 加密插件缓存目录的css文件
            if (id.endsWith('.css')) {
                return;
            }
            const module = (0, uts_1.resolveUTSAppModule)(process.env.UNI_UTS_PLATFORM, id, importer ? path_1.default.dirname(importer) : inputDir, options.x !== true);
            if (module) {
                // app-js 会直接返回 index.uts 路径，不需要 uts-proxy
                if (module.endsWith('.uts')) {
                    return module;
                }
                // prefix the polyfill id with \0 to tell other plugins not to try to load or transform it
                return module + '?uts-proxy';
            }
        },
        load(id) {
            if ((0, uts_1.isUTSProxy)(id)) {
                return '';
            }
        },
        async buildEnd() {
            utsModuleCaches.clear();
            changedFiles.clear();
            if (process.env.NODE_ENV !== 'development' || !(0, utils_2.isNormalCompileTarget)()) {
                if (uniXKotlinCompiler) {
                    await uniXKotlinCompiler.close();
                }
                if (uniXSwiftCompiler) {
                    await uniXSwiftCompiler.close();
                }
            }
        },
        watchChange(fileName, change) {
            if (uniXKotlinCompiler || uniXSwiftCompiler || uniXArkTSCompiler) {
                fileName = (0, utils_2.normalizePath)(fileName);
                if (fileName.startsWith(uniModulesDir)) {
                    // 仅处理uni_modules中的文件
                    const plugin = fileName.slice(uniModulesDir.length + 1).split('/')[0];
                    if (utsPlugins.has(plugin)) {
                        const changeFile = { fileName, event: change.event };
                        if (!changedFiles.has(plugin)) {
                            changedFiles.set(plugin, [changeFile]);
                        }
                        else {
                            changedFiles.get(plugin).push(changeFile);
                        }
                    }
                }
            }
        },
        async transform(_, id, opts) {
            if (opts && opts.ssr) {
                return;
            }
            if (!(0, uts_1.isUTSProxy)(id)) {
                return;
            }
            const { filename } = (0, utils_1.parseVueRequest)(id.replace('\0', ''));
            // 当 vue 和 nvue 均引用了相同 uts 插件，解决两套编译器会编译两次 uts 插件的问题
            // 通过缓存，保证同一个 uts 插件只编译一次
            const pluginDir = (0, utils_2.normalizePath)(filename);
            if (utsModuleCaches.get(pluginDir)) {
                return utsModuleCaches.get(pluginDir)().then((result) => {
                    if (result) {
                        result.deps.forEach((dep) => {
                            this.addWatchFile(dep);
                        });
                        return {
                            code: result.code,
                            map: null,
                            syntheticNamedExports: result.encrypt,
                            meta: result.meta,
                        };
                    }
                });
            }
            const compile = (0, uni_shared_1.once)(() => {
                return compilePlugin(pluginDir);
            });
            utsModuleCaches.set(pluginDir, compile);
            const result = await compile();
            if (result) {
                result.deps.forEach((dep) => {
                    this.addWatchFile(dep);
                });
                return {
                    code: result.code,
                    map: null,
                    syntheticNamedExports: result.encrypt,
                    meta: result.meta,
                };
            }
        },
    };
}
exports.uniUTSAppUniModulesPlugin = uniUTSAppUniModulesPlugin;
async function buildUniExtApis() {
    await uniExtApiCompiler();
}
exports.buildUniExtApis = buildUniExtApis;
function resolveExtApiProvider(pkg) {
    const provider = pkg.uni_modules?.['uni-ext-api']?.provider;
    if (provider?.service) {
        if (provider.name && !provider.servicePlugin) {
            provider.servicePlugin = 'uni-' + provider.service;
        }
        return provider;
    }
}
exports.resolveExtApiProvider = resolveExtApiProvider;
function uniDecryptUniModulesPlugin() {
    const inputDir = process.env.UNI_INPUT_DIR;
    const isX = process.env.UNI_APP_X === 'true';
    return {
        name: 'uni:uni_modules-d',
        enforce: 'pre',
        async configResolved() {
            if (isX && (0, utils_2.isNormalCompileTarget)()) {
                const manifest = (0, json_1.parseManifestJsonOnce)(inputDir);
                await (0, uni_modules_cloud_1.checkEncryptUniModules)(inputDir, {
                    mode: process.env.NODE_ENV !== 'development'
                        ? 'production'
                        : 'development',
                    packType: process.env.UNI_APP_PACK_TYPE ||
                        (process.env.NODE_ENV !== 'development' ? 'release' : 'debug'),
                    compilerVersion: process.env.UNI_COMPILER_VERSION,
                    appid: manifest.appid,
                    appname: manifest.name,
                    platform: process.env.UNI_UTS_PLATFORM,
                    'uni-app-x': isX,
                });
            }
        },
        resolveId(id) {
            if ((0, uts_1.isUTSProxy)(id) || (0, uts_1.isUniHelpers)(id)) {
                return id;
            }
            if (isX && (0, utils_2.isNormalCompileTarget)() && !id.endsWith('.css')) {
                const resolvedId = (0, uni_modules_cloud_1.resolveEncryptUniModule)(id, process.env.UNI_UTS_PLATFORM, process.env.UNI_APP_X === 'true');
                if (resolvedId) {
                    return resolvedId;
                }
            }
        },
    };
}
exports.uniDecryptUniModulesPlugin = uniDecryptUniModulesPlugin;
