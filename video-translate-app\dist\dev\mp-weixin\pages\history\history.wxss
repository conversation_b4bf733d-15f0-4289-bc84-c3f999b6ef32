
.history-container.data-v-73685b36 {
  height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
}
.search-bar.data-v-73685b36 {
  padding: 20rpx 40rpx;
  background: white;
}
.search-input.data-v-73685b36 {
  width: 100%;
  height: 70rpx;
  background-color: #f5f5f5;
  border-radius: 35rpx;
  padding: 0 30rpx;
  font-size: 28rpx;
}
.filter-bar.data-v-73685b36 {
  display: flex;
  padding: 20rpx 40rpx;
  background: white;
  border-bottom: 1rpx solid #f0f0f0;
}
.filter-item.data-v-73685b36 {
  padding: 10rpx 20rpx;
  margin-right: 20rpx;
  border-radius: 20rpx;
  background-color: #f5f5f5;
  font-size: 26rpx;
  color: #666;
}
.filter-item.active.data-v-73685b36 {
  background-color: #007AFF;
  color: white;
}
.history-list.data-v-73685b36 {
  flex: 1;
  padding: 20rpx 40rpx;
}
.empty-state.data-v-73685b36 {
  text-align: center;
  padding: 100rpx 40rpx;
}
.empty-icon.data-v-73685b36 {
  display: block;
  font-size: 120rpx;
  margin-bottom: 30rpx;
}
.empty-text.data-v-73685b36 {
  display: block;
  font-size: 32rpx;
  color: #333;
  margin-bottom: 20rpx;
}
.empty-desc.data-v-73685b36 {
  display: block;
  font-size: 26rpx;
  color: #999;
}
.history-item.data-v-73685b36 {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}
.item-header.data-v-73685b36 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}
.status-badge.data-v-73685b36 {
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  color: white;
}
.status-badge.processing.data-v-73685b36 {
  background-color: #1890ff;
}
.status-badge.completed.data-v-73685b36 {
  background-color: #52c41a;
}
.status-badge.failed.data-v-73685b36 {
  background-color: #ff4d4f;
}
.create-time.data-v-73685b36 {
  font-size: 24rpx;
  color: #999;
}
.item-content.data-v-73685b36 {
  margin-bottom: 20rpx;
}
.video-info.data-v-73685b36 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}
.file-name.data-v-73685b36 {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}
.file-size.data-v-73685b36 {
  font-size: 24rpx;
  color: #666;
}
.process-info.data-v-73685b36, .error-info.data-v-73685b36 {
  font-size: 24rpx;
  color: #666;
}
.error-info.data-v-73685b36 {
  color: #ff4d4f;
}
.item-actions.data-v-73685b36 {
  display: flex;
  gap: 20rpx;
}
.action-btn.data-v-73685b36 {
  border-radius: 8rpx;
  font-size: 24rpx;
}
.action-btn.primary.data-v-73685b36 {
  background-color: #007AFF;
  color: white;
  border: none;
}
.action-btn.danger.data-v-73685b36 {
  background-color: white;
  color: #ff4d4f;
  border: 1rpx solid #ff4d4f;
}
.load-more.data-v-73685b36 {
  text-align: center;
  padding: 40rpx;
  color: #999;
  font-size: 26rpx;
}
