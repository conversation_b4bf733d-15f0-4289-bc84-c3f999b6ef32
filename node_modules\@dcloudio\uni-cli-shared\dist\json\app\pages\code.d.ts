export declare const arrayBufferCode = "\nif (typeof uni !== 'undefined' && uni && uni.requireGlobal) {\n  const global = uni.requireGlobal()\n  ArrayBuffer = global.ArrayBuffer\n  Int8Array = global.Int8Array\n  Uint8Array = global.Uint8Array\n  Uint8ClampedArray = global.Uint8ClampedArray\n  Int16Array = global.Int16Array\n  Uint16Array = global.Uint16Array\n  Int32Array = global.Int32Array\n  Uint32Array = global.Uint32Array\n  Float32Array = global.Float32Array\n  Float64Array = global.Float64Array\n  BigInt64Array = global.BigInt64Array\n  BigUint64Array = global.BigUint64Array\n};\n";
export declare const polyfillCode = "\nif (typeof Promise !== 'undefined' && !Promise.prototype.finally) {\n  Promise.prototype.finally = function(callback) {\n    const promise = this.constructor\n    return this.then(\n      value => promise.resolve(callback()).then(() => value),\n      reason => promise.resolve(callback()).then(() => {\n        throw reason\n      })\n    )\n  }\n};\n\nif (typeof uni !== 'undefined' && uni && uni.requireGlobal) {\n  const global = uni.requireGlobal()\n  ArrayBuffer = global.ArrayBuffer\n  Int8Array = global.Int8Array\n  Uint8Array = global.Uint8Array\n  Uint8ClampedArray = global.Uint8ClampedArray\n  Int16Array = global.Int16Array\n  Uint16Array = global.Uint16Array\n  Int32Array = global.Int32Array\n  Uint32Array = global.Uint32Array\n  Float32Array = global.Float32Array\n  Float64Array = global.Float64Array\n  BigInt64Array = global.BigInt64Array\n  BigUint64Array = global.BigUint64Array\n};\n\n";
export declare const restoreGlobalCode = "\nif(uni.restoreGlobal){\n  uni.restoreGlobal(Vue,weex,plus,setTimeout,clearTimeout,setInterval,clearInterval)\n}\n";
export declare const globalCode: string;
