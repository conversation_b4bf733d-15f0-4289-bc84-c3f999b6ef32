/*! *****************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */
var e=function(){return e=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},e.apply(this,arguments)};function t(){for(var e=0,t=0,n=arguments.length;t<n;t++)e+=arguments[t].length;var r=Array(e),o=0;for(t=0;t<n;t++)for(var i=arguments[t],u=0,c=i.length;u<c;u++,o++)r[o]=i[u];return r}var n,r=Object.prototype.hasOwnProperty,o=function(e){return null==e},i=Array.isArray,u=function(e){var t=Object.create(null);return function(n){return t[n]||(t[n]=e(n))}},c=/\B([A-Z])/g,a=u((function(e){return e.replace(c,"-$1").toLowerCase()})),s=/-(\w)/g,l=u((function(e){return e.replace(s,(function(e,t){return t?t.toUpperCase():""}))})),f=u((function(e){return e.charAt(0).toUpperCase()+e.slice(1)})),d=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g;function p(e,t){if(i(e))return e;if(t&&(n=t,o=e,r.call(n,o)))return[e];var n,o,u=[];return e.replace(d,(function(e,t,n,r){return u.push(n?r.replace(/\\(\\)?/g,"$1"):t||e),r})),u}function g(e,t){var n,r=p(t,e);for(n=r.shift();!o(n);){if(null==(e=e[n]))return;n=r.shift()}return e}function v(e){return!!(null==e?void 0:e.getElementById)}function m(e){return v(e)?e.vm:e}function h(e){return v(e)?e.vm.$basePage:e.$page}function _(e){return v(e.$page)?e.$basePage:e.$page}function E(e){var t;return e.__wxWebviewId__?e.__wxWebviewId__:e.privateProperties?e.privateProperties.slaveId:_(e)?null===(t=_(e))||void 0===t?void 0:t.id:void 0}function w(e){return e.route||e.uri}function y(e){return e.options||e.$page&&e.$page.options||{}}function T(e){return{id:E(e),path:w(e),query:y(e)}}function S(e){var t=m(function(e){return getCurrentPages().find((function(t){return E(m(t))===e}))}(e));return t&&t.$vm}function P(e,t){return function(e){if(e._$weex)return e._uid;if(e._$id)return e._$id;if(e.uid)return e.uid;var t=function(e){for(var t=e.$parent;t;){if(t._$id)return t;t=t.$parent}}(e);if(!e.$parent)return"-1";var n=e.$vnode,r=n.context;return r&&r!==t&&r._$id?r._$id+";"+t._$id+","+n.data.attrs._i:t._$id+","+n.data.attrs._i}(e)===t}function b(e,t,n){var r,o,i,u,c,a,s,l,f,d,p,g,v;if(void 0===n&&(n=!1),n)if(e.component&&P(e.component,t))v=e.component;else{var m=[];e.children instanceof Array?m=e.children:(null===(o=null===(r=e.component)||void 0===r?void 0:r.subTree)||void 0===o?void 0:o.children)&&(null===(u=null===(i=e.component)||void 0===i?void 0:i.subTree)||void 0===u?void 0:u.children)instanceof Array?m=e.component.subTree.children:(null===(l=null===(s=null===(a=null===(c=e.component)||void 0===c?void 0:c.subTree)||void 0===a?void 0:a.component)||void 0===s?void 0:s.subTree)||void 0===l?void 0:l.children)&&(null===(g=null===(p=null===(d=null===(f=e.component)||void 0===f?void 0:f.subTree)||void 0===d?void 0:d.component)||void 0===p?void 0:p.subTree)||void 0===g?void 0:g.children)instanceof Array&&(m=e.component.subTree.component.subTree.children),m.find((function(e){return v=b(e,t,!0)}))}else e&&(P(e,t)?v=e:e.$children.find((function(e){return v=b(e,t)})));return v}function x(e,t){var n=S(e);if(n)return I(n)?b(n.$.subTree,t,!0):b(n,t)}function O(t,n){var r,o=t.$data||t.data;return t.exposed?o=e(e({},o),t.exposed):t.$&&t.$.exposed&&(o=e(e({},o),t.$.exposed)),t&&(r=n?g(o,n):Object.assign({},o)),Promise.resolve({data:r})}function $(e,t){if(e){var n=I(e);Object.keys(t).forEach((function(r){n?(e.$data||e.data)[r]=t[r]:e[r]=t[r]}))}return Promise.resolve()}function M(e,t,r){return I(e)&&(e=e.$vm||e.ctx),new Promise((function(o,i){var u,c;if(!e)return i(n.VM_NOT_EXISTS);if(!e[t]&&!(null===(c=e.$.exposed)||void 0===c?void 0:c[t]))return i(n.METHOD_NOT_EXISTS);var a,s=e[t]?e[t].apply(e,r):(u=e.$.exposed)[t].apply(u,r);!(a=s)||"object"!=typeof a&&"function"!=typeof a||"function"!=typeof a.then?o({result:s}):s.then((function(e){o({result:e})}))}))}function I(e){return!e.$children}!function(e){e.VM_NOT_EXISTS="VM_NOT_EXISTS",e.METHOD_NOT_EXISTS="METHOD_NOT_EXISTS"}(n||(n={}));var C=1,A={};var k=new Map,q=function(t){return new Promise((function(n,r){var o=k.values().next().value;if(o){var i=t.method;if("onOpen"===i)return N(o,n);if(i.startsWith("on"))return o.instance[i]((function(e){n(e)}));"sendMessage"===i&&(i="send"),o.instance[i](e(e({},t),{success:function(e){n({result:e}),"close"===i&&k.delete(k.keys().next().value)},fail:function(e){r(e)}}))}else r({errMsg:"socketTask not exists."})}))};function N(e,t){if(e.isOpend)t({data:e.openData});else{var n=setInterval((function(){e.isOpend&&(clearInterval(n),t(e.openData))}),200);setTimeout((function(){clearInterval(n)}),2e3)}}var D=["stopRecord","getRecorderManager","pauseVoice","stopVoice","pauseBackgroundAudio","stopBackgroundAudio","getBackgroundAudioManager","createAudioContext","createInnerAudioContext","createVideoContext","createCameraContext","createMapContext","canIUse","startAccelerometer","stopAccelerometer","startCompass","stopCompass","hideToast","hideLoading","showNavigationBarLoading","hideNavigationBarLoading","navigateBack","createAnimation","pageScrollTo","createSelectorQuery","createCanvasContext","createContext","drawCanvas","hideKeyboard","stopPullDownRefresh","arrayBufferToBase64","base64ToArrayBuffer"],U=new Map,W=["onCompassChange","onThemeChange","onUserCaptureScreen","onWindowResize","onMemoryWarning","onAccelerometerChange","onKeyboardHeightChange","onNetworkStatusChange","onPushMessage","onLocationChange","onGetWifiList","onWifiConnected","onWifiConnectedWithPartialInfo","onSocketOpen","onSocketError","onSocketMessage","onSocketClose"],L={},H=/^\$|Sync$|Window$|WindowStyle$|sendHostEvent|sendNativeEvent|restoreGlobal|requireGlobal|getCurrentSubNVue|getMenuButtonBoundingClientRect|^report|interceptors|Interceptor$|getSubNVueById|requireNativePlugin|upx2px|hideKeyboard|canIUse|^create|Sync$|Manager$|base64ToArrayBuffer|arrayBufferToBase64|getLocale|setLocale|invokePushCallback|getWindowInfo|getDeviceInfo|getAppBaseInfo|getSystemSetting|getAppAuthorizeSetting|initUTS|requireUTS|registerUTS/,X=/^on|^off/;function B(e){return H.test(e)||-1!==D.indexOf(e)}var j={getPageStack:function(){return Promise.resolve({pageStack:getCurrentPages().map((function(e){return T(m(e))}))})},getCurrentPage:function(){var e=getCurrentPages(),t=e.length;return new Promise((function(n,r){t?n(T(m(e[t-1]))):r(Error("getCurrentPages().length=0"))}))},callUniMethod:function(t,n){var r=t.method,o=t.args;return new Promise((function(t,i){if("connectSocket"!==r){var u,c;if(W.includes(r)){U.has(r)||U.set(r,new Map);var a=o[0],s=function(e){n({id:a,result:{method:r,data:e}})};return r.startsWith("onSocket")?q({method:r.replace("Socket","")}).then((function(e){return s(e)})).catch((function(e){return s(e)})):(U.get(r).set(a,s),uni[r](s)),t({result:null})}if(r.startsWith("off")&&W.includes(r.replace("off","on"))){var l=r.replace("off","on");if(U.has(l)){var f=o[0];if(void 0!==f){var d=U.get(l).get(f);uni[r](d),U.get(l).delete(f)}else{U.get(l).forEach((function(e){uni[r](e)})),U.delete(l)}}return t({result:null})}if(r.indexOf("Socket")>0)return q(e({method:r.replace("Socket","")},o[0])).then((function(e){return t(e)})).catch((function(e){return i(e)}));if(!uni[r])return i(Error("uni."+r+" not exists"));if(B(r))return t({result:uni[r].apply(uni,o)});var p=[Object.assign({},o[0]||{},{success:function(e){setTimeout((function(){t({result:e})}),"pageScrollTo"===r?350:0)},fail:function(e){i(Error(e.errMsg.replace(r+":fail ","")))}})];uni[r].apply(uni,p)}else(u=o[0].id,c=o[0].url,new Promise((function(e,t){var n=uni.connectSocket({url:c,success:function(){e({result:{errMsg:"connectSocket:ok"}})},fail:function(){t({result:{errMsg:"connectSocket:fail"}})}});k.set(u,{instance:n,isOpend:!1}),n.onOpen((function(e){k.get(u).isOpend=!0,k.get(u).openData=e}))}))).then((function(e){return t(e)})).catch((function(e){return i(e)}))}))},mockUniMethod:function(e){var t=e.method;if(!uni[t])throw Error("uni."+t+" not exists");if(!function(e){return!X.test(e)}(t))throw Error("You can't mock uni."+t);var n,r=e.result,i=e.functionDeclaration;return o(r)&&o(i)?(L[t]&&(uni[t]=L[t],delete L[t]),Promise.resolve()):(n=o(i)?B(t)?function(){return r}:function(e){setTimeout((function(){r.errMsg&&-1!==r.errMsg.indexOf(":fail")?e.fail&&e.fail(r):e.success&&e.success(r),e.complete&&e.complete(r)}),4)}:function(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];return new Function("return "+i)().apply(n,t.concat(e.args))},n.origin=L[t]||uni[t],L[t]||(L[t]=uni[t]),uni[t]=n,Promise.resolve())},captureScreenshot:function(e){return new Promise((function(t,n){var r=getCurrentPages(),o=r.length;if(o){var i=m(r[o-1]);if(i)if("undefined"!=typeof UniElement)i.$viewToTempFilePath({id:e.id,offsetX:null!==e.offsetX?e.offsetX:"0",offsetY:null!==e.offsetY?e.offsetY:"0",wholeContent:1==e.fullPage,path:e.path||"screenshot",overwrite:!0,success:function(e){nativeFileManager.readFile({encoding:"base64",filePath:e.tempFilePath,success:function(e){t({data:e.data})},fail:function(e){n(Error("captureScreenshot fail: "+(e.message||e.errMsg)))}})},fail:function(e){n(Error("captureScreenshot fail: "+(e.message||e.errMsg)))}});else{var u=i.$getAppWebview(),c=new plus.nativeObj.Bitmap("captureScreenshot","captureScreenshot.png");u.draw(c,(function(e){var n=c.toBase64Data().replace("data:image/png;base64,","").replace("data:image/(null);base64,","");c.clear(),t({data:n})}),(function(e){n(Error("captureScreenshot fail: "+e.message))}),{wholeContent:!!e.fullPage})}}else n(Error("getCurrentPage fail."))}))},socketEmitter:function(t){return new Promise((function(n,r){(function(t){return new Promise((function(n,r){if(k.has(t.id)){var o=k.get(t.id),i=o.instance,u=t.method,c=t.id;if("onOpen"==u)return N(o,n);if(u.startsWith("on"))return i[u]((function(e){n({method:"Socket."+u,id:c,data:e})}));i[u](e(e({},t),{success:function(e){n(e),"close"===u&&k.delete(t.id)},fail:function(e){r(e)}}))}else r({errMsg:"socketTask not exists."})}))})(t).then((function(e){return n(e)})).catch((function(e){return r(e)}))}))}},V=j,Y={getData:function(e){return O(S(e.pageId),e.path)},setData:function(e){return $(S(e.pageId),e.data)},callMethod:function(e){var t,r=((t={})[n.VM_NOT_EXISTS]="Page["+e.pageId+"] not exists",t[n.METHOD_NOT_EXISTS]="page."+e.method+" not exists",t);return new Promise((function(t,n){M(S(e.pageId),e.method,e.args).then((function(e){return t(e)})).catch((function(e){n(Error(r[e]))}))}))},callMethodWithCallback:function(e){var t,r=((t={})[n.VM_NOT_EXISTS]="callMethodWithCallback:fail, Page["+e.pageId+"] not exists",t[n.METHOD_NOT_EXISTS]="callMethodWithCallback:fail, page."+e.method+" not exists",t),o=e.args[e.args.length-1];M(S(e.pageId),e.method,e.args).catch((function(e){o({errMsg:r[e]})}))}};function R(e){return e.nodeId||e.elementId}var F={getData:function(e){return O(x(e.pageId,R(e)),e.path)},setData:function(e){return $(x(e.pageId,R(e)),e.data)},callMethod:function(e){var t,r=R(e),o=((t={})[n.VM_NOT_EXISTS]="Component["+e.pageId+":"+r+"] not exists",t[n.METHOD_NOT_EXISTS]="component."+e.method+" not exists",t);return new Promise((function(t,n){M(x(e.pageId,r),e.method,e.args).then((function(e){return t(e)})).catch((function(e){n(Error(o[e]))}))}))}};function J(e){var t=getCurrentPages().find((function(t){return h(t).id===e}));if(!t)throw Error("page["+e+"] not found");var n=t.$vm._$weex;return n.document.__$weex__||(n.document.__$weex__=n),n.document}var z={},G={};["text","image","input","textarea","video","web-view","slider"].forEach((function(e){z[e]=!0,G["u-"+e]=!0}));var K=["movable-view","picker","ad","button","checkbox-group","checkbox","form","icon","label","movable-area","navigator","picker-view-column","picker-view","progress","radio-group","radio","rich-text","u-slider","swiper-item","swiper","switch"],Q=K.map((function(e){return f(l(e))}));function Z(e){var t=e.type;if(G[t])return t.replace("u-","");var n=e.__vue__&&e.__vue__.$options.name;return"USlider"===n?"slider":n&&-1!==Q.indexOf(n)?a(n):t}function ee(e){var t={elementId:e.nodeId,tagName:Z(e),nvue:!0},n=e.__vue__;return n&&!n.$options.isReserved&&(t.nodeId=n._uid),"video"===t.tagName&&(t.videoId=t.nodeId),t}function te(e,t,n){for(var r=e.children,o=0;o<r.length;o++){var i=r[o];if(t(i)){if(!n)return i;n.push(i)}if(n)te(i,t,n);else{var u=te(i,t,n);if(u)return u}}return n}function ne(e,t,n){var r,o;if(0===t.indexOf("#")?(r=t.substr(1),o=function(e){return e.attr&&e.attr.id===r}):0===t.indexOf(".")&&(r=t.substr(1),o=function(e){return e.classList&&-1!==e.classList.indexOf(r)}),o){var i=te(e,o,n);if(!i)throw Error("Node("+t+") not exists");return i}if("body"===t)return Object.assign({},e,{type:"page"});0===t.indexOf("uni-")&&(t=t.replace("uni-",""));var u=z[t]?"u-"+t:t,c=-1!==K.indexOf(u)?f(l(u)):"",a=te(e,(function(e){return e.type===u||c&&e.__vue__&&e.__vue__.$options.name===c}),n);if(!a)throw Error("Node("+t+") not exists");return a}var re=[{test:function(e){return 2===e.length&&-1!==e.indexOf("document.documentElement.scrollWidth")&&-1!==e.indexOf("document.documentElement.scrollHeight")},call:function(e){var t=e.__$weex__||e.ownerDocument.__$weex__;return new Promise((function(n){"scroll-view"===e.type&&1===e.children.length&&(e=e.children[0]),t.requireModule("dom").getComponentRect(e.ref,(function(e){e.result?n([e.size.width,e.size.height]):n([0,0])}))}))}},{test:function(e){return 1===e.length&&"document.documentElement.scrollTop"===e[0]},call:function(e){var t=e.__$weex__||e.ownerDocument.__$weex__;return new Promise((function(n){"scroll-view"===e.type&&1===e.children.length&&(e=e.children[0]),t.requireModule("dom").getComponentRect(e.ref,(function(e){n([e.size&&Math.abs(e.size.top)||0])}))}))}},{test:function(e){return 2===e.length&&-1!==e.indexOf("offsetWidth")&&-1!==e.indexOf("offsetHeight")},call:function(e){var t=e.__$weex__||e.ownerDocument.__$weex__;return new Promise((function(n){t.requireModule("dom").getComponentRect(e.ref,(function(e){e.result?n([e.size.width,e.size.height]):n([0,0])}))}))}},{test:function(e,t){return 1===e.length&&"innerText"===e[0]},call:function(e){return Promise.resolve([oe(e,[]).join("")])}}];function oe(e,t){return"u-text"===e.type?t.push(e.attr.value):e.pureChildren.map((function(e){return oe(e,t)})),t}function ie(e){return e.replace(/\n/g,"").replace(/<u-/g,"<").replace(/<\/u-/g,"</")}function ue(e,t){return"outer"===t?"body"===e.role&&"scroll-view"===e.type?"<page>"+ie(ue(e,"inner"))+"</page>":ie(e.toString()):ie(e.pureChildren.map((function(e){return e.toString()})).join(""))}var ce={input:{input:function(e,t){e.setValue(t)}},textarea:{input:function(e,t){e.setValue(t)}},"scroll-view":{scrollTo:function(e,t,n){e.scrollTo(n)},scrollTop:function(e){return 0},scrollLeft:function(e){return 0},scrollWidth:function(e){return 0},scrollHeight:function(e){return 0}},swiper:{swipeTo:function(e,t){e.__vue__.current=t}},"movable-view":{moveTo:function(e,t,n){var r=e.__vue__;r.x=t,r.y=n}},switch:{tap:function(e){var t=e.__vue__;t.checked=!t.checked}},slider:{slideTo:function(e,t){e.__vue__.value=t}}};function ae(e){return J(e).body}var se={getWindow:function(e){return ae(e)},getDocument:function(e){return ae(e)},getEl:function(e,t){var n=J(t).getRef(e);if(!n)throw Error("element destroyed");return n},getOffset:function(e){var t=e.__$weex__||e.ownerDocument.__$weex__;return new Promise((function(n){t.requireModule("dom").getComponentRect(e.ref,(function(e){e.result?n({left:e.size.left,top:e.size.top}):n({left:0,top:0})}))}))},querySelector:function(e,t){return Promise.resolve(ee(ne(e,t)))},querySelectorAll:function(e,t){return Promise.resolve({elements:ne(e,t,[]).map((function(e){return ee(e)}))})},queryProperties:function(e,t){var n=re.find((function(n){return n.test(t,e)}));return n?n.call(e).then((function(e){return{properties:e}})):Promise.resolve({properties:t.map((function(t){return g(e,t)}))})},queryAttributes:function(e,t){var n=e.attr;return Promise.resolve({attributes:t.map((function(t){return"class"===t?(e.classList||[]).join(" "):String(n[t]||n[l(t)]||"")}))})},queryStyles:function(e,t){var n=e.style;return Promise.resolve({styles:t.map((function(e){return n[e]}))})},queryHTML:function(e,t){return Promise.resolve({html:ue(e,t)})},dispatchTapEvent:function(e){return e.fireEvent("click",{timeStamp:Date.now(),target:e,currentTarget:e},!0),Promise.resolve()},dispatchLongpressEvent:function(e){return e.fireEvent("longpress",{timeStamp:Date.now(),target:e,currentTarget:e},!0),Promise.resolve()},dispatchTouchEvent:function(e,t,n){return n||(n={}),n.touches||(n.touches=[]),n.changedTouches||(n.changedTouches=[]),n.touches.length||n.touches.push({identifier:Date.now(),target:e}),e.fireEvent(t,Object.assign({timeStamp:Date.now(),target:e,currentTarget:e},n),!0),Promise.resolve()},callFunction:function(e,n,r){var o=g(ce,n);return o?Promise.resolve({result:o.apply(null,t([e],r))}):Promise.reject(Error(n+" not exists"))},triggerEvent:function(e,t,n){var r=e.__vue__;return r?r.$trigger&&r.$trigger(t,{},n):e.fireEvent(t,{timeStamp:Date.now(),target:e,currentTarget:e},!1,{params:[{detail:n}]}),Promise.resolve()}};function le(){return Object.assign({},function(e){return{"Page.getElement":function(t){return e.querySelector(e.getDocument(t.pageId),t.selector)},"Page.getElements":function(t){return e.querySelectorAll(e.getDocument(t.pageId),t.selector)},"Page.getWindowProperties":function(t){return e.queryProperties(e.getWindow(t.pageId),t.names)}}}(se),function(e){var t=function(t){return e.getEl(t.elementId,t.pageId)};return{"Element.getElement":function(n){return e.querySelector(t(n),n.selector)},"Element.getElements":function(n){return e.querySelectorAll(t(n),n.selector)},"Element.getDOMProperties":function(n){return e.queryProperties(t(n),n.names)},"Element.getProperties":function(n){var r=t(n),o=r.__vue__||r.attr||{};return r.__vueParentComponent&&(o=Object.assign({},o,r.__vueParentComponent.attrs,r.__vueParentComponent.props)),e.queryProperties(o,n.names)},"Element.getOffset":function(n){return e.getOffset(t(n))},"Element.getAttributes":function(n){return e.queryAttributes(t(n),n.names)},"Element.getStyles":function(n){return e.queryStyles(t(n),n.names)},"Element.getHTML":function(n){return e.queryHTML(t(n),n.type)},"Element.tap":function(n){return e.dispatchTapEvent(t(n))},"Element.longpress":function(n){return e.dispatchLongpressEvent(t(n))},"Element.touchstart":function(n){return e.dispatchTouchEvent(t(n),"touchstart",n)},"Element.touchmove":function(n){return e.dispatchTouchEvent(t(n),"touchmove",n)},"Element.touchend":function(n){return e.dispatchTouchEvent(t(n),"touchend",n)},"Element.callFunction":function(n){return e.callFunction(t(n),n.functionName,n.args)},"Element.triggerEvent":function(n){return e.triggerEvent(t(n),n.type,n.detail)}}}(se))}var fe=function(){};fe.prototype={on:function(e,t,n){var r=this.e||(this.e={});return(r[e]||(r[e]=[])).push({fn:t,ctx:n}),this},once:function(e,t,n){var r=this;function o(){r.off(e,o),t.apply(n,arguments)}return o._=t,this.on(e,o,n)},emit:function(e){for(var t=[].slice.call(arguments,1),n=((this.e||(this.e={}))[e]||[]).slice(),r=0,o=n.length;r<o;r++)n[r].fn.apply(n[r].ctx,t);return this},off:function(e,t){var n=this.e||(this.e={}),r=n[e],o=[];if(r&&t){for(var i=r.length-1;i>=0;i--)if(r[i].fn===t||r[i].fn._===t){r.splice(i,1);break}o=r}return o.length?n[e]=o:delete n[e],this}};var de=fe;function pe(e){var t=new de;return{subscribe:function(n,r,o){void 0===o&&(o=!1),t[o?"once":"on"](e+"."+n,r)},subscribeHandler:function(n,r,o){t.emit(e+"."+n,r,o)}}}var ge=Object.assign,ve=ge(pe("service"),{publishHandler:function(e,t,n){UniViewJSBridge.subscribeHandler(e,t,n)}}),me=ge(pe("view"),{publishHandler:function(e,t,n){UniServiceJSBridge.subscribeHandler(e,t,n)}});if("undefined"==typeof UniServiceJSBridge&&"undefined"==typeof UniViewJSBridge){var he="undefined"==typeof globalThis?Function("return this")():globalThis;he.UniServiceJSBridge=ve,he.UniViewJSBridge=me}var _e={};Object.keys(V).forEach((function(e){_e["App."+e]=V[e]})),Object.keys(Y).forEach((function(e){_e["Page."+e]=Y[e]})),Object.keys(F).forEach((function(e){_e["Element."+e]=F[e]}));var Ee,we,ye,Te=process.env.UNI_AUTOMATOR_WS_ENDPOINT;function Se(e){setTimeout((function(){ye.send({data:JSON.stringify(e)})}),0)}function Pe(e){var t=JSON.parse(e.data),n=t.id,r=t.method,o=t.params,i={id:n},u=_e[r];if(!u){if(we){var c=we(n,r,o,i);if(!0===c)return;u=c}if(!u)return i.error={message:r+" unimplemented"},Se(i)}try{u(o,Se).then((function(e){e&&(i.result=e)})).catch((function(e){i.error={message:e.message}})).finally((function(){Se(i)}))}catch(e){i.error={message:e.message},Se(i)}}we=function(e,t,n,r){var o=n.pageId,i=function(e){var t=getCurrentPages();if(!e)return t[t.length-1];return t.find((function(t){return h(t).id===e}))}(o);return i?!h(i).meta.isNVue?(UniServiceJSBridge.publishHandler("sendAutoMessage",{id:e,method:t,params:n},o),!0):(Ee||(Ee=le()),Ee[t]):(r.error={message:"page["+o+"] not exists"},Se(r),!0)},UniServiceJSBridge.subscribe("onAutoMessageReceive",(function(e){Se(e)})),setTimeout((function(){if("undefined"!=typeof window&&(window.__uniapp_x_||window.__uniapp_x_postMessage))!function(e,t){var n,r=0;t&&(r=C++,A[r]=t);var o={data:{id:r,type:"automator",data:e}};console.log("postMessageToUniXWebView",o),(null===(n=null===window||void 0===window?void 0:window.__uniapp_x_)||void 0===n?void 0:n.postMessage)?window.__uniapp_x_.postMessage(JSON.stringify(o)):(null===window||void 0===window?void 0:window.__uniapp_x_postMessage)&&window.__uniapp_x_postMessage({data:o})}({action:"ready"});else{if(Te&&Te.endsWith(":0000"))return;void 0===e&&(e={}),(ye=uni.connectSocket({url:e.wsEndpoint||Te,complete:function(){}})).onMessage(Pe),ye.onOpen((function(t){e.success&&e.success(),console.log("已开启自动化测试...")})),ye.onError((function(e){console.log("automator.onError",e)})),ye.onClose((function(){e.fail&&e.fail({errMsg:"$$initRuntimeAutomator:fail"}),console.log("automator.onClose")}))}var e}),500);var be=Object.prototype.hasOwnProperty,xe=Array.isArray,Oe=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g;function $e(e,t){if(xe(e))return e;if(t&&(n=t,r=e,be.call(n,r)))return[e];var n,r,o=[];return e.replace(Oe,(function(e,t,n,r){return o.push(n?r.replace(/\\(\\)?/g,"$1"):t||e),r})),o}function Me(e){return"TEXT"===e.tagName||"TEXTAREA"===e.tagName}function Ie(e){var t="";return e.childNodes.forEach((function(e){Me(e)?t+=e.getAttribute("value"):t+=Ie(e)})),t}function Ce(e){return e.startsWith("uni-")?e.replace("uni-",""):e}var Ae=new Map;function ke(e){var t,n,r,o,i,u={elementId:(o=e,i=o._id,i||(i=Date.now()+"-"+Math.random(),o._id=i,Ae.set(i,{id:i,element:o})),i),tagName:e.tagName.toLocaleLowerCase().replace("uni-","")},c=e.__vueParentComponent;return c&&((null===(t=c.ctx)||void 0===t?void 0:t.$el)!==e||(null===(r=null===(n=c.proxy)||void 0===n?void 0:n.$options)||void 0===r?void 0:r.rootElement)||(u.nodeId=function(e){if(e._$weex)return e._uid;if(e._$id)return e._$id;if(e.uid)return e.uid;var t=function(e){for(var t=e.$parent;t;){if(t._$id)return t;t=t.$parent}}(e);if(!e.$parent)return"-1";var n=e.$vnode,r=n.context;return r&&r!==t&&r._$id?r._$id+";"+t._$id+","+n.data.attrs._i:t._$id+","+n.data.attrs._i}(c))),"video"===u.tagName&&(u.videoId=u.nodeId),u}var qe={input:{input:function(e,t){var n=new UniInputEvent("input",{detail:{value:t}});n.detail.value=t,e.dispatchEvent("input",n)}},textarea:{input:function(e,t){var n=new UniInputEvent("input",{detail:{value:t}});n.detail.value=t,e.dispatchEvent("input",n)}},"scroll-view":{scrollTo:function(e,t,n){e.scrollLeft=t,e.scrollTop=n},scrollTop:function(e){return e.scrollTop},scrollLeft:function(e){return e.scrollLeft},scrollWidth:function(e){return e.scrollWidth},scrollHeight:function(e){return e.scrollHeight}},swiper:{swipeTo:function(e,t){e.setAttribute("current",t)}},"movable-view":{moveTo:function(e,t,n){e.__vue__._animationTo(t,n)}},switch:{tap:function(e){e.click()}},slider:{slideTo:function(e,t){e.setAttribute("value",t)}}};function Ne(e){var t=getCurrentPages().find((function(t){return h(t).id===e}));if(!t)throw Error("page["+e+"] not found");return m(t).$el.parentNode}function De(e){return e.map((function(e){var t=new UniTouch;return t.identifier=e.identifier,t.pageX=e.pageX,t.pageY=e.pageY,e.screenX&&(t.screenX=e.screenX),e.screenY&&(t.screenY=e.screenY),e.clientX&&(t.clientX=e.clientX),e.clientY&&(t.clientY=e.clientY),t}))}var Ue,We={getWindow:function(e){var t=Ne(e);return 1===t.childNodes.length?t.childNodes[0]:t},getDocument:function(e){return Ne(e)},getEl:function(e){var t=Ae.get(e);if(!t)throw Error("element destroyed");return t.element},getOffset:function(e){return Promise.resolve({left:e.offsetLeft,top:e.offsetTop})},querySelector:function(e,t){return"page"===(t=Ce(t))&&(t="body"),Promise.resolve(ke(e.querySelector(t)))},querySelectorAll:function(e,t){t=Ce(t);var n=[],r=e.querySelectorAll(t);return[].forEach.call(r,(function(e){try{n.push(ke(e))}catch(e){}})),Promise.resolve({elements:n})},queryProperties:function(e,t){return Promise.resolve({properties:t.map((function(t){return"innerText"==t?Me(e)?e.getAttribute("value"):Ie(e):"value"==t?e.getAnyAttribute("value"):"offsetWidth"==t?e.offsetWidth:"offsetHeight"==t?e.offsetHeight:"document.documentElement.scrollWidth"===t?e.scrollWidth:"document.documentElement.scrollHeight"===t?e.scrollHeight:"document.documentElement.scrollTop"===t?e.scrollTop:e.getAttribute(t)||e[t](Ue||(n=["Element.getDOMProperties not support ",""],r=["Element.getDOMProperties not support ",""],Object.defineProperty?Object.defineProperty(n,"raw",{value:r}):n.raw=r,Ue=n),t);var n,r}))})},queryAttributes:function(e,t){return Promise.resolve({attributes:t.map((function(t){return String(e.getAnyAttribute(t))}))})},queryStyles:function(e,t){var n=e._style;return Promise.resolve({styles:t.map((function(e){return n[e]}))})},queryHTML:function(e,t){return Promise.resolve({html:(n="outer"===t?e.outerHTML:e.innerHTML,n.replace(/\n/g,"").replace(/(<uni-text[^>]*>)(<span[^>]*>[^<]*<\/span>)(.*?<\/uni-text>)/g,"$1$3").replace(/<\/?[^>]*>/g,(function(e){return-1<e.indexOf("<body")?"<page>":"</body>"===e?"</page>":0!==e.indexOf("<uni-")&&0!==e.indexOf("</uni-")?"":e.replace(/uni-/g,"").replace(/ role=""/g,"").replace(/ aria-label=""/g,"")})))});var n},dispatchTapEvent:function(e){return e.dispatchEvent("click",new UniPointerEvent("click",0,0,0,0,0,0,0,0)),Promise.resolve()},dispatchLongpressEvent:function(e){return this.dispatchTouchEvent(e,"longpress",{touches:[{identifier:1,pageX:0,pageY:0,clientX:0,clientY:0,screenX:0,screenY:0}],changedTouches:[{identifier:1,pageX:0,pageY:0,clientX:0,clientY:0,screenX:0,screenY:0}]}),Promise.resolve()},dispatchTouchEvent:function(e,t,n){n||(n={}),n.touches||(n.touches=[]),n.changedTouches||(n.changedTouches=[]),n.touches.length||n.touches.push({identifier:Date.now(),target:e});var r=De(n.touches),o=De(n.changedTouches);return e.dispatchEvent(t,new UniTouchEvent(t,r,o)),Promise.resolve()},callFunction:function(e,n,r){var o=function(e,t){var n,r=$e(t,e);for(n=r.shift();null!=n;){if(null==(e=e[n]))return;n=r.shift()}return e}(qe,n);return o?Promise.resolve({result:o.apply(null,t([e],r))}):Promise.reject(Error(n+" not exists"))},triggerEvent:function(e,t,n){return function(e,t,n){var r={input:{input:UniInputEvent,focus:UniInputFocusEvent,blur:UniInputBlurEvent},textarea:{input:UniInputEvent},"scroll-view":{scroll:UniScrollEvent,scrollend:UniScrollEvent,scrolltoupper:UniScrollToUpperEvent,scrolltolower:UniScrollToLowerEvent}},o=e.tagName.toLocaleLowerCase();if(r[o]&&r[o][t]){var i=new(0,r[o][t])(t,{detail:n});return n&&Object.assign(i,{detail:n}),Promise.resolve(e.dispatchEvent(t,i))}return Promise.reject(Error(o+" component "+t+" event not exists"))}(e,t,n)},callContextMethod:function(e,t,n){return e[t]?Promise.resolve(e[t].apply(e,n)):Promise.reject(Error(t+" method not exists"))}};var Le=Object.assign({},function(e){return{"Page.getElement":function(t){return e.querySelector(e.getDocument(t.pageId),t.selector)},"Page.getElements":function(t){return e.querySelectorAll(e.getDocument(t.pageId),t.selector)},"Page.getWindowProperties":function(t){return e.queryProperties(e.getWindow(t.pageId),t.names)}}}(We),function(e){var t=function(t){return e.getEl(t.elementId,t.pageId)};return{"Element.getElement":function(n){return e.querySelector(t(n),n.selector)},"Element.getElements":function(n){return e.querySelectorAll(t(n),n.selector)},"Element.getDOMProperties":function(n){return e.queryProperties(t(n),n.names)},"Element.getProperties":function(n){var r=t(n);return e.queryProperties(r,n.names)},"Element.getOffset":function(n){return e.getOffset(t(n))},"Element.getAttributes":function(n){return e.queryAttributes(t(n),n.names)},"Element.getStyles":function(n){return e.queryStyles(t(n),n.names)},"Element.getHTML":function(n){return e.queryHTML(t(n),n.type)},"Element.tap":function(n){return e.dispatchTapEvent(t(n))},"Element.longpress":function(n){return e.dispatchLongpressEvent(t(n))},"Element.touchstart":function(n){return e.dispatchTouchEvent(t(n),"touchstart",n)},"Element.touchmove":function(n){return e.dispatchTouchEvent(t(n),"touchmove",n)},"Element.touchend":function(n){return e.dispatchTouchEvent(t(n),"touchend",n)},"Element.callFunction":function(n){return e.callFunction(t(n),n.functionName,n.args)},"Element.triggerEvent":function(n){return e.triggerEvent(t(n),n.type,n.detail)},"Element.callContextMethod":function(n){return e.callContextMethod(t(n),n.method,n.args)}}}(We));function He(e){return UniViewJSBridge.publishHandler("onAutoMessageReceive",e)}UniViewJSBridge.subscribe("sendAutoMessage",(function(e){var t=e.id,n=e.method,r=e.params,o={id:t};if("ping"==n)return o.result="pong",void He(o);var i=Le[n];if(!i)return o.error={message:n+" unimplemented"},He(o);try{i(r).then((function(e){e&&(o.result=e)})).catch((function(e){o.error={message:e.message}})).finally((function(){He(o)}))}catch(e){o.error={message:e.message},He(o)}}));
